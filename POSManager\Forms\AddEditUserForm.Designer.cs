namespace POSManager.Forms
{
    partial class AddEditUserForm
    {
        private System.ComponentModel.IContainer components = null;
        private TableLayoutPanel mainLayout;
        private Panel headerPanel;
        private Panel formPanel;
        private Panel buttonPanel;
        private Label lblTitle;
        private Label lblUsername;
        private TextBox txtUsername;
        private Label lblFullName;
        private TextBox txtFullName;
        private Label lblEmail;
        private TextBox txtEmail;
        private Label lblRole;
        private ComboBox cmbRole;
        private Label lblPassword;
        private TextBox txtPassword;
        private Label lblConfirmPassword;
        private TextBox txtConfirmPassword;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.mainLayout = new TableLayoutPanel();
            this.headerPanel = new Panel();
            this.formPanel = new Panel();
            this.buttonPanel = new Panel();
            this.lblTitle = new Label();
            this.lblUsername = new Label();
            this.txtUsername = new TextBox();
            this.lblFullName = new Label();
            this.txtFullName = new TextBox();
            this.lblEmail = new Label();
            this.txtEmail = new TextBox();
            this.lblRole = new Label();
            this.cmbRole = new ComboBox();
            this.lblPassword = new Label();
            this.txtPassword = new TextBox();
            this.lblConfirmPassword = new Label();
            this.txtConfirmPassword = new TextBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            this.mainLayout.SuspendLayout();
            this.headerPanel.SuspendLayout();
            this.formPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();

            // 
            // AddEditUserForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 600);
            this.Controls.Add(this.mainLayout);
            this.Font = new Font("Segoe UI", 9F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddEditUserForm";
            this.RightToLeft = RightToLeft.Yes;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة/تعديل مستخدم";
            this.KeyDown += new KeyEventHandler(this.AddEditUserForm_KeyDown);

            // 
            // mainLayout
            // 
            this.mainLayout.ColumnCount = 1;
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            this.mainLayout.Controls.Add(this.headerPanel, 0, 0);
            this.mainLayout.Controls.Add(this.formPanel, 0, 1);
            this.mainLayout.Controls.Add(this.buttonPanel, 0, 2);
            this.mainLayout.Dock = DockStyle.Fill;
            this.mainLayout.Location = new Point(0, 0);
            this.mainLayout.Margin = new Padding(4);
            this.mainLayout.Name = "mainLayout";
            this.mainLayout.RowCount = 3;
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 70F));
            this.mainLayout.Size = new Size(500, 600);
            this.mainLayout.TabIndex = 0;

            // 
            // headerPanel
            // 
            this.headerPanel.BackColor = Color.FromArgb(240, 240, 240);
            this.headerPanel.Controls.Add(this.lblTitle);
            this.headerPanel.Dock = DockStyle.Fill;
            this.headerPanel.Location = new Point(4, 4);
            this.headerPanel.Margin = new Padding(4);
            this.headerPanel.Name = "headerPanel";
            this.headerPanel.Size = new Size(492, 72);
            this.headerPanel.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.Dock = DockStyle.Fill;
            this.lblTitle.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.DarkBlue;
            this.lblTitle.Location = new Point(0, 0);
            this.lblTitle.Margin = new Padding(4, 0, 4, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(492, 72);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "إضافة مستخدم جديد";
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // formPanel
            // 
            this.formPanel.Controls.Add(this.chkIsActive);
            this.formPanel.Controls.Add(this.lblConfirmPassword);
            this.formPanel.Controls.Add(this.txtConfirmPassword);
            this.formPanel.Controls.Add(this.lblPassword);
            this.formPanel.Controls.Add(this.txtPassword);
            this.formPanel.Controls.Add(this.lblRole);
            this.formPanel.Controls.Add(this.cmbRole);
            this.formPanel.Controls.Add(this.lblEmail);
            this.formPanel.Controls.Add(this.txtEmail);
            this.formPanel.Controls.Add(this.lblFullName);
            this.formPanel.Controls.Add(this.txtFullName);
            this.formPanel.Controls.Add(this.lblUsername);
            this.formPanel.Controls.Add(this.txtUsername);
            this.formPanel.Dock = DockStyle.Fill;
            this.formPanel.Location = new Point(4, 84);
            this.formPanel.Margin = new Padding(4);
            this.formPanel.Name = "formPanel";
            this.formPanel.Padding = new Padding(30);
            this.formPanel.Size = new Size(492, 442);
            this.formPanel.TabIndex = 1;

            // 
            // lblUsername
            // 
            this.lblUsername.AutoSize = true;
            this.lblUsername.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblUsername.Location = new Point(380, 35);
            this.lblUsername.Margin = new Padding(4, 0, 4, 0);
            this.lblUsername.Name = "lblUsername";
            this.lblUsername.Size = new Size(82, 23);
            this.lblUsername.TabIndex = 0;
            this.lblUsername.Text = "اسم المستخدم";

            // 
            // txtUsername
            // 
            this.txtUsername.Font = new Font("Segoe UI", 11F);
            this.txtUsername.Location = new Point(35, 32);
            this.txtUsername.Margin = new Padding(4);
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Size = new Size(337, 32);
            this.txtUsername.TabIndex = 1;
            this.txtUsername.KeyDown += new KeyEventHandler(this.txtUsername_KeyDown);

            // 
            // lblFullName
            // 
            this.lblFullName.AutoSize = true;
            this.lblFullName.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblFullName.Location = new Point(395, 85);
            this.lblFullName.Margin = new Padding(4, 0, 4, 0);
            this.lblFullName.Name = "lblFullName";
            this.lblFullName.Size = new Size(67, 23);
            this.lblFullName.TabIndex = 2;
            this.lblFullName.Text = "الاسم الكامل";

            // 
            // txtFullName
            // 
            this.txtFullName.Font = new Font("Segoe UI", 11F);
            this.txtFullName.Location = new Point(35, 82);
            this.txtFullName.Margin = new Padding(4);
            this.txtFullName.Name = "txtFullName";
            this.txtFullName.Size = new Size(337, 32);
            this.txtFullName.TabIndex = 3;
            this.txtFullName.KeyDown += new KeyEventHandler(this.txtFullName_KeyDown);

            // 
            // lblEmail
            // 
            this.lblEmail.AutoSize = true;
            this.lblEmail.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblEmail.Location = new Point(370, 135);
            this.lblEmail.Margin = new Padding(4, 0, 4, 0);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new Size(92, 23);
            this.lblEmail.TabIndex = 4;
            this.lblEmail.Text = "البريد الإلكتروني";

            // 
            // txtEmail
            // 
            this.txtEmail.Font = new Font("Segoe UI", 11F);
            this.txtEmail.Location = new Point(35, 132);
            this.txtEmail.Margin = new Padding(4);
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.PlaceholderText = "اختياري";
            this.txtEmail.Size = new Size(337, 32);
            this.txtEmail.TabIndex = 5;
            this.txtEmail.KeyDown += new KeyEventHandler(this.txtEmail_KeyDown);

            // 
            // lblRole
            // 
            this.lblRole.AutoSize = true;
            this.lblRole.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblRole.Location = new Point(420, 185);
            this.lblRole.Margin = new Padding(4, 0, 4, 0);
            this.lblRole.Name = "lblRole";
            this.lblRole.Size = new Size(42, 23);
            this.lblRole.TabIndex = 6;
            this.lblRole.Text = "الدور";

            // 
            // cmbRole
            // 
            this.cmbRole.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbRole.Font = new Font("Segoe UI", 11F);
            this.cmbRole.FormattingEnabled = true;
            this.cmbRole.Location = new Point(35, 182);
            this.cmbRole.Margin = new Padding(4);
            this.cmbRole.Name = "cmbRole";
            this.cmbRole.Size = new Size(337, 33);
            this.cmbRole.TabIndex = 7;
            this.cmbRole.KeyDown += new KeyEventHandler(this.cmbRole_KeyDown);

            // 
            // lblPassword
            // 
            this.lblPassword.AutoSize = true;
            this.lblPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPassword.Location = new Point(400, 235);
            this.lblPassword.Margin = new Padding(4, 0, 4, 0);
            this.lblPassword.Name = "lblPassword";
            this.lblPassword.Size = new Size(62, 23);
            this.lblPassword.TabIndex = 8;
            this.lblPassword.Text = "كلمة المرور";

            // 
            // txtPassword
            // 
            this.txtPassword.Font = new Font("Segoe UI", 11F);
            this.txtPassword.Location = new Point(35, 232);
            this.txtPassword.Margin = new Padding(4);
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.PasswordChar = '*';
            this.txtPassword.Size = new Size(337, 32);
            this.txtPassword.TabIndex = 9;
            this.txtPassword.KeyDown += new KeyEventHandler(this.txtPassword_KeyDown);

            // 
            // lblConfirmPassword
            // 
            this.lblConfirmPassword.AutoSize = true;
            this.lblConfirmPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblConfirmPassword.Location = new Point(370, 285);
            this.lblConfirmPassword.Margin = new Padding(4, 0, 4, 0);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new Size(92, 23);
            this.lblConfirmPassword.TabIndex = 10;
            this.lblConfirmPassword.Text = "تأكيد كلمة المرور";

            // 
            // txtConfirmPassword
            // 
            this.txtConfirmPassword.Font = new Font("Segoe UI", 11F);
            this.txtConfirmPassword.Location = new Point(35, 282);
            this.txtConfirmPassword.Margin = new Padding(4);
            this.txtConfirmPassword.Name = "txtConfirmPassword";
            this.txtConfirmPassword.PasswordChar = '*';
            this.txtConfirmPassword.Size = new Size(337, 32);
            this.txtConfirmPassword.TabIndex = 11;
            this.txtConfirmPassword.KeyDown += new KeyEventHandler(this.txtConfirmPassword_KeyDown);

            // 
            // chkIsActive
            // 
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Checked = true;
            this.chkIsActive.CheckState = CheckState.Checked;
            this.chkIsActive.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.chkIsActive.Location = new Point(380, 340);
            this.chkIsActive.Margin = new Padding(4);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Size = new Size(82, 27);
            this.chkIsActive.TabIndex = 12;
            this.chkIsActive.Text = "مستخدم نشط";
            this.chkIsActive.UseVisualStyleBackColor = true;

            // 
            // buttonPanel
            // 
            this.buttonPanel.BackColor = Color.FromArgb(240, 240, 240);
            this.buttonPanel.Controls.Add(this.btnCancel);
            this.buttonPanel.Controls.Add(this.btnSave);
            this.buttonPanel.Dock = DockStyle.Fill;
            this.buttonPanel.Location = new Point(4, 534);
            this.buttonPanel.Margin = new Padding(4);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new Size(492, 62);
            this.buttonPanel.TabIndex = 2;

            // 
            // btnSave
            // 
            this.btnSave.BackColor = Color.Green;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(350, 15);
            this.btnSave.Margin = new Padding(4);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(120, 35);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "حفظ (F12)";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.Location = new Point(220, 15);
            this.btnCancel.Margin = new Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(120, 35);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء (Esc)";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.mainLayout.ResumeLayout(false);
            this.headerPanel.ResumeLayout(false);
            this.formPanel.ResumeLayout(false);
            this.formPanel.PerformLayout();
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);
        }
    }
}
