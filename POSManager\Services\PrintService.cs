using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using Microsoft.Data.Sqlite;
using POSManager.Data;

namespace POSManager.Services
{
    public class PrintService
    {
        private static PrintService? _instance;
        private static readonly object _lock = new object();
        private readonly Dictionary<string, Models.PrinterSettings> _printers = new Dictionary<string, Models.PrinterSettings>();
        private readonly Dictionary<string, PrintTemplate> _templates = new Dictionary<string, PrintTemplate>();
        private PrintStatistics _statistics = new PrintStatistics();

        private PrintService()
        {
            LoadPrinters();
            LoadTemplates();
            LoadStatistics();
        }

        public static PrintService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new PrintService();
                    }
                }
                return _instance;
            }
        }

        #region إدارة الطابعات

        /// <summary>
        /// تحميل قائمة الطابعات المتاحة
        /// </summary>
        public void LoadPrinters()
        {
            try
            {
                _printers.Clear();

                // تحميل الطابعات المثبتة في النظام
                foreach (string printerName in System.Drawing.Printing.PrinterSettings.InstalledPrinters)
                {
                    var printerSettings = new System.Drawing.Printing.PrinterSettings { PrinterName = printerName };
                    var customPrinter = new Models.PrinterSettings
                    {
                        Name = printerName,
                        DisplayName = printerName,
                        IsOnline = true,
                        Status = "جاهز",
                        SupportedTypes = new[] { PrintType.Invoice, PrintType.Receipt, PrintType.Report },
                        DefaultPaperSize = Models.PaperSize.A4,
                        DefaultOrientation = PrintOrientation.Portrait,
                        DefaultQuality = PrintQuality.Normal,
                        MaxCopies = 99,
                        SupportsDuplex = printerSettings.CanDuplex,
                        SupportsColor = printerSettings.SupportsColor
                    };

                    _printers[printerName] = customPrinter;
                }

                // تحديد الطابعة الافتراضية
                var defaultPrinter = new System.Drawing.Printing.PrinterSettings().PrinterName;
                if (_printers.ContainsKey(defaultPrinter))
                {
                    _printers[defaultPrinter].IsDefault = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطابعات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على قائمة الطابعات المتاحة
        /// </summary>
        public List<Models.PrinterSettings> GetAvailablePrinters()
        {
            return _printers.Values.ToList();
        }

        /// <summary>
        /// الحصول على الطابعة الافتراضية
        /// </summary>
        public Models.PrinterSettings? GetDefaultPrinter()
        {
            return _printers.Values.FirstOrDefault(p => p.IsDefault);
        }

        /// <summary>
        /// تعيين الطابعة الافتراضية
        /// </summary>
        public async Task<bool> SetDefaultPrinter(string printerName)
        {
            try
            {
                // إلغاء تحديد جميع الطابعات كافتراضية
                foreach (var printer in _printers.Values)
                {
                    printer.IsDefault = false;
                }

                // تعيين الطابعة المحددة كافتراضية
                if (_printers.ContainsKey(printerName))
                {
                    _printers[printerName].IsDefault = true;
                    await SavePrinterSettings();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعيين الطابعة الافتراضية: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// فحص حالة الطابعة
        /// </summary>
        public PrinterStatus GetPrinterStatus(string printerName)
        {
            var status = new PrinterStatus
            {
                Name = printerName,
                LastChecked = DateTime.Now
            };

            try
            {
                var printerSettings = new System.Drawing.Printing.PrinterSettings { PrinterName = printerName };
                status.IsOnline = printerSettings.IsValid;
                status.IsReady = status.IsOnline;
                status.HasError = false;
                status.ErrorMessage = "";
                status.IsPaperOut = false;
                status.IsTonerLow = false;
                status.QueueCount = 0;
            }
            catch (Exception ex)
            {
                status.IsOnline = false;
                status.IsReady = false;
                status.HasError = true;
                status.ErrorMessage = ex.Message;
            }

            return status;
        }

        #endregion

        #region إدارة القوالب

        /// <summary>
        /// تحميل قوالب الطباعة
        /// </summary>
        private void LoadTemplates()
        {
            try
            {
                _templates.Clear();

                // إضافة القوالب الافتراضية
                AddDefaultTemplates();

                // تحميل القوالب المخصصة من قاعدة البيانات
                LoadCustomTemplates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قوالب الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إضافة القوالب الافتراضية
        /// </summary>
        private void AddDefaultTemplates()
        {
            // قالب فاتورة حرارية 80mm
            var thermalInvoiceTemplate = new PrintTemplate
            {
                Id = "thermal_invoice_80mm",
                Name = "فاتورة حرارية 80mm",
                Description = "قالب فاتورة للطابعات الحرارية 80mm",
                Type = PrintType.Invoice,
                PaperSize = Models.PaperSize.Thermal80mm,
                Orientation = PrintOrientation.Portrait,
                Width = 300, // pixels
                Height = 0,  // متغير
                MarginTop = 5,
                MarginBottom = 5,
                MarginLeft = 5,
                MarginRight = 5,
                ShowLogo = true,
                LogoWidth = 100,
                LogoHeight = 50,
                FontFamily = "Tahoma",
                FontSize = 8,
                IsDefault = true,
                CreatedDate = DateTime.Now
            };

            // قالب فاتورة A4
            var a4InvoiceTemplate = new PrintTemplate
            {
                Id = "a4_invoice",
                Name = "فاتورة A4",
                Description = "قالب فاتورة للطباعة على ورق A4",
                Type = PrintType.Invoice,
                PaperSize = Models.PaperSize.A4,
                Orientation = PrintOrientation.Portrait,
                Width = 794, // pixels (A4 width at 96 DPI)
                Height = 1123, // pixels (A4 height at 96 DPI)
                MarginTop = 50,
                MarginBottom = 50,
                MarginLeft = 50,
                MarginRight = 50,
                ShowLogo = true,
                LogoWidth = 150,
                LogoHeight = 75,
                FontFamily = "Tahoma",
                FontSize = 10,
                IsDefault = false,
                CreatedDate = DateTime.Now
            };

            // قالب تقرير A4
            var a4ReportTemplate = new PrintTemplate
            {
                Id = "a4_report",
                Name = "تقرير A4",
                Description = "قالب تقرير للطباعة على ورق A4",
                Type = PrintType.Report,
                PaperSize = Models.PaperSize.A4,
                Orientation = PrintOrientation.Portrait,
                Width = 794,
                Height = 1123,
                MarginTop = 50,
                MarginBottom = 50,
                MarginLeft = 50,
                MarginRight = 50,
                ShowLogo = true,
                LogoWidth = 100,
                LogoHeight = 50,
                FontFamily = "Tahoma",
                FontSize = 9,
                IsDefault = true,
                CreatedDate = DateTime.Now
            };

            _templates[thermalInvoiceTemplate.Id] = thermalInvoiceTemplate;
            _templates[a4InvoiceTemplate.Id] = a4InvoiceTemplate;
            _templates[a4ReportTemplate.Id] = a4ReportTemplate;
        }

        /// <summary>
        /// تحميل القوالب المخصصة من قاعدة البيانات
        /// </summary>
        private void LoadCustomTemplates()
        {
            // سيتم تنفيذها لاحقاً عند إضافة جداول القوالب لقاعدة البيانات
        }

        /// <summary>
        /// الحصول على قائمة القوالب
        /// </summary>
        public List<PrintTemplate> GetTemplates(PrintType? type = null)
        {
            var templates = _templates.Values.ToList();
            
            if (type.HasValue)
            {
                templates = templates.Where(t => t.Type == type.Value).ToList();
            }

            return templates;
        }

        /// <summary>
        /// الحصول على قالب محدد
        /// </summary>
        public PrintTemplate? GetTemplate(string templateId)
        {
            return _templates.ContainsKey(templateId) ? _templates[templateId] : null;
        }

        /// <summary>
        /// الحصول على القالب الافتراضي لنوع معين
        /// </summary>
        public PrintTemplate? GetDefaultTemplate(PrintType type)
        {
            return _templates.Values.FirstOrDefault(t => t.Type == type && t.IsDefault);
        }

        #endregion

        #region عمليات الطباعة الأساسية

        /// <summary>
        /// طباعة مستند
        /// </summary>
        public async Task<PrintResult> PrintDocument(Models.PrintDocument document, PrintJobSettings? settings = null)
        {
            var result = new PrintResult
            {
                PrintTime = DateTime.Now
            };

            try
            {
                var startTime = DateTime.Now;

                // التحقق من وجود الطابعة
                var printerName = settings?.PrinterName ?? GetDefaultPrinter()?.Name;
                if (string.IsNullOrEmpty(printerName))
                {
                    result.Message = "لم يتم العثور على طابعة متاحة";
                    return result;
                }

                // التحقق من حالة الطابعة
                var printerStatus = GetPrinterStatus(printerName);
                if (!printerStatus.IsReady)
                {
                    result.Message = $"الطابعة غير جاهزة: {printerStatus.ErrorMessage}";
                    return result;
                }

                // إعداد مستند الطباعة
                var printDoc = new System.Drawing.Printing.PrintDocument();
                printDoc.PrinterSettings.PrinterName = printerName;

                if (settings != null)
                {
                    printDoc.PrinterSettings.Copies = (short)settings.Copies;
                    printDoc.PrinterSettings.Collate = settings.Collate;
                }

                // تحديد معالج الطباعة حسب نوع المستند
                switch (document.Type)
                {
                    case PrintType.Invoice:
                        printDoc.PrintPage += (sender, e) => PrintInvoicePage(sender, e, document);
                        break;
                    case PrintType.Report:
                        printDoc.PrintPage += (sender, e) => PrintReportPage(sender, e, document);
                        break;
                    default:
                        printDoc.PrintPage += (sender, e) => PrintGenericPage(sender, e, document);
                        break;
                }

                // تنفيذ الطباعة
                printDoc.Print();

                // تحديث النتيجة
                result.Success = true;
                result.Message = "تمت الطباعة بنجاح";
                result.PrinterUsed = printerName;
                result.Duration = DateTime.Now - startTime;
                result.PagesPrinted = 1;

                // تحديث الإحصائيات
                await UpdatePrintStatistics(result);

                // تحديث حالة المستند
                document.IsPrinted = true;
                document.PrintedDate = DateTime.Now;
                document.PrinterName = printerName;

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"خطأ في الطباعة: {ex.Message}";
                result.ErrorCode = "PRINT_ERROR";
                return result;
            }
        }

        #endregion

        #region معالجات الطباعة

        /// <summary>
        /// طباعة صفحة فاتورة
        /// </summary>
        private void PrintInvoicePage(object sender, PrintPageEventArgs e, Models.PrintDocument document)
        {
            if (e.Graphics == null) return;

            try
            {
                // استخراج بيانات الفاتورة
                var invoiceData = System.Text.Json.JsonSerializer.Deserialize<InvoicePrintData>(document.Content);
                if (invoiceData == null) return;

                // الحصول على القالب المناسب
                var template = GetTemplate(document.TemplateId) ?? GetDefaultTemplate(PrintType.Invoice);
                if (template == null) return;

                var graphics = e.Graphics;
                var currentY = template.MarginTop;
                var pageWidth = template.Width - template.MarginLeft - template.MarginRight;

                // الخطوط
                var titleFont = new Font(template.FontFamily, template.FontSize + 4, FontStyle.Bold);
                var headerFont = new Font(template.FontFamily, template.FontSize + 2, FontStyle.Bold);
                var normalFont = new Font(template.FontFamily, template.FontSize);
                var smallFont = new Font(template.FontFamily, template.FontSize - 1);

                var blackBrush = new SolidBrush(Color.Black);
                var grayBrush = new SolidBrush(Color.Gray);

                // رسم الشعار (إذا كان متاحاً)
                if (template.ShowLogo && !string.IsNullOrEmpty(template.LogoPath) && System.IO.File.Exists(template.LogoPath))
                {
                    try
                    {
                        var logo = Image.FromFile(template.LogoPath);
                        var logoRect = new Rectangle(template.MarginLeft, currentY, template.LogoWidth, template.LogoHeight);
                        graphics.DrawImage(logo, logoRect);
                        currentY += template.LogoHeight + 10;
                    }
                    catch
                    {
                        // تجاهل أخطاء الشعار
                    }
                }

                // معلومات الشركة
                if (!string.IsNullOrEmpty(invoiceData.CompanyName))
                {
                    graphics.DrawString(invoiceData.CompanyName, titleFont, blackBrush, template.MarginLeft, currentY);
                    currentY += titleFont.Height + 5;

                    if (!string.IsNullOrEmpty(invoiceData.CompanyAddress))
                    {
                        graphics.DrawString(invoiceData.CompanyAddress, normalFont, grayBrush, template.MarginLeft, currentY);
                        currentY += normalFont.Height + 2;
                    }

                    if (!string.IsNullOrEmpty(invoiceData.CompanyPhone))
                    {
                        graphics.DrawString($"هاتف: {invoiceData.CompanyPhone}", normalFont, grayBrush, template.MarginLeft, currentY);
                        currentY += normalFont.Height + 2;
                    }

                    currentY += 10;
                }

                // خط فاصل
                graphics.DrawLine(new Pen(Color.Black, 1), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                currentY += 10;

                // عنوان الفاتورة
                var invoiceTitle = "فــــاتــــورة";
                var titleSize = graphics.MeasureString(invoiceTitle, titleFont);
                graphics.DrawString(invoiceTitle, titleFont, blackBrush,
                    template.MarginLeft + (pageWidth - titleSize.Width) / 2, currentY);
                currentY += titleFont.Height + 15;

                // معلومات الفاتورة
                graphics.DrawString($"رقم الفاتورة: {invoiceData.InvoiceNumber}", headerFont, blackBrush, template.MarginLeft, currentY);
                graphics.DrawString($"التاريخ: {invoiceData.Date:dd/MM/yyyy}", normalFont, blackBrush,
                    template.MarginLeft + pageWidth - 150, currentY);
                currentY += headerFont.Height + 5;

                graphics.DrawString($"الوقت: {invoiceData.Date:HH:mm}", normalFont, grayBrush,
                    template.MarginLeft + pageWidth - 150, currentY);
                currentY += normalFont.Height + 10;

                // معلومات العميل (إذا كانت متاحة)
                if (!string.IsNullOrEmpty(invoiceData.CustomerName))
                {
                    graphics.DrawString($"العميل: {invoiceData.CustomerName}", normalFont, blackBrush, template.MarginLeft, currentY);
                    currentY += normalFont.Height + 5;

                    if (!string.IsNullOrEmpty(invoiceData.CustomerPhone))
                    {
                        graphics.DrawString($"الهاتف: {invoiceData.CustomerPhone}", normalFont, grayBrush, template.MarginLeft, currentY);
                        currentY += normalFont.Height + 5;
                    }
                    currentY += 5;
                }

                // خط فاصل
                graphics.DrawLine(new Pen(Color.Black, 1), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                currentY += 10;

                // رأس جدول الأصناف
                var itemHeaderY = currentY;
                graphics.DrawString("الصنف", headerFont, blackBrush, template.MarginLeft, itemHeaderY);
                graphics.DrawString("الكمية", headerFont, blackBrush, template.MarginLeft + 120, itemHeaderY);
                graphics.DrawString("السعر", headerFont, blackBrush, template.MarginLeft + 170, itemHeaderY);
                graphics.DrawString("المجموع", headerFont, blackBrush, template.MarginLeft + 220, itemHeaderY);
                currentY += headerFont.Height + 5;

                // خط تحت الرأس
                graphics.DrawLine(new Pen(Color.Gray, 1), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                currentY += 5;

                // أصناف الفاتورة
                foreach (var item in invoiceData.Items)
                {
                    graphics.DrawString(item.ProductName, normalFont, blackBrush, template.MarginLeft, currentY);
                    graphics.DrawString(item.Quantity.ToString("0.##"), normalFont, blackBrush, template.MarginLeft + 120, currentY);
                    graphics.DrawString(item.UnitPrice.ToString("0.00"), normalFont, blackBrush, template.MarginLeft + 170, currentY);
                    graphics.DrawString(item.Total.ToString("0.00"), normalFont, blackBrush, template.MarginLeft + 220, currentY);
                    currentY += normalFont.Height + 3;
                }

                currentY += 10;

                // خط فاصل
                graphics.DrawLine(new Pen(Color.Black, 1), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                currentY += 10;

                // المجاميع
                var totalsX = template.MarginLeft + pageWidth - 150;
                graphics.DrawString($"المجموع الفرعي: {invoiceData.Subtotal:0.00}", normalFont, blackBrush, totalsX, currentY);
                currentY += normalFont.Height + 3;

                if (invoiceData.DiscountAmount > 0)
                {
                    graphics.DrawString($"الخصم: {invoiceData.DiscountAmount:0.00}", normalFont, blackBrush, totalsX, currentY);
                    currentY += normalFont.Height + 3;
                }

                if (invoiceData.TaxAmount > 0)
                {
                    graphics.DrawString($"الضريبة: {invoiceData.TaxAmount:0.00}", normalFont, blackBrush, totalsX, currentY);
                    currentY += normalFont.Height + 3;
                }

                graphics.DrawString($"الإجمالي: {invoiceData.Total:0.00}", headerFont, blackBrush, totalsX, currentY);
                currentY += headerFont.Height + 5;

                graphics.DrawString($"المدفوع: {invoiceData.PaidAmount:0.00}", normalFont, blackBrush, totalsX, currentY);
                currentY += normalFont.Height + 3;

                if (invoiceData.ChangeAmount > 0)
                {
                    graphics.DrawString($"الباقي: {invoiceData.ChangeAmount:0.00}", normalFont, blackBrush, totalsX, currentY);
                    currentY += normalFont.Height + 3;
                }

                currentY += 15;

                // معلومات إضافية
                if (!string.IsNullOrEmpty(invoiceData.CashierName))
                {
                    graphics.DrawString($"الكاشير: {invoiceData.CashierName}", smallFont, grayBrush, template.MarginLeft, currentY);
                    currentY += smallFont.Height + 3;
                }

                if (!string.IsNullOrEmpty(invoiceData.PaymentMethod))
                {
                    graphics.DrawString($"طريقة الدفع: {invoiceData.PaymentMethod}", smallFont, grayBrush, template.MarginLeft, currentY);
                    currentY += smallFont.Height + 3;
                }

                // رسالة الشكر
                currentY += 10;
                var thankYouMsg = "شكراً لتسوقكم معنا";
                var thankYouSize = graphics.MeasureString(thankYouMsg, normalFont);
                graphics.DrawString(thankYouMsg, normalFont, blackBrush,
                    template.MarginLeft + (pageWidth - thankYouSize.Width) / 2, currentY);

                // تنظيف الموارد
                titleFont.Dispose();
                headerFont.Dispose();
                normalFont.Dispose();
                smallFont.Dispose();
                blackBrush.Dispose();
                grayBrush.Dispose();
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، اطبع رسالة خطأ بسيطة
                var errorFont = new Font("Tahoma", 10);
                var errorBrush = new SolidBrush(Color.Red);
                e.Graphics.DrawString($"خطأ في طباعة الفاتورة: {ex.Message}", errorFont, errorBrush, 10, 10);
                errorFont.Dispose();
                errorBrush.Dispose();
            }
        }

        /// <summary>
        /// طباعة صفحة تقرير
        /// </summary>
        private void PrintReportPage(object sender, PrintPageEventArgs e, Models.PrintDocument document)
        {
            if (e.Graphics == null) return;

            try
            {
                // استخراج بيانات التقرير
                var reportData = System.Text.Json.JsonSerializer.Deserialize<ReportPrintData>(document.Content);
                if (reportData == null) return;

                // الحصول على القالب المناسب
                var template = GetTemplate(document.TemplateId) ?? GetDefaultTemplate(PrintType.Report);
                if (template == null) return;

                var graphics = e.Graphics;
                var currentY = template.MarginTop;
                var pageWidth = template.Width - template.MarginLeft - template.MarginRight;

                // الخطوط
                var titleFont = new Font(template.FontFamily, template.FontSize + 6, FontStyle.Bold);
                var subtitleFont = new Font(template.FontFamily, template.FontSize + 2, FontStyle.Bold);
                var headerFont = new Font(template.FontFamily, template.FontSize + 1, FontStyle.Bold);
                var normalFont = new Font(template.FontFamily, template.FontSize);
                var smallFont = new Font(template.FontFamily, template.FontSize - 1);

                var blackBrush = new SolidBrush(Color.Black);
                var grayBrush = new SolidBrush(Color.Gray);
                var blueBrush = new SolidBrush(Color.DarkBlue);

                // رسم الشعار (إذا كان متاحاً)
                if (template.ShowLogo && !string.IsNullOrEmpty(template.LogoPath) && System.IO.File.Exists(template.LogoPath))
                {
                    try
                    {
                        var logo = Image.FromFile(template.LogoPath);
                        var logoRect = new Rectangle(template.MarginLeft, currentY, template.LogoWidth, template.LogoHeight);
                        graphics.DrawImage(logo, logoRect);
                        currentY += template.LogoHeight + 10;
                    }
                    catch
                    {
                        // تجاهل أخطاء الشعار
                    }
                }

                // معلومات الشركة
                if (!string.IsNullOrEmpty(reportData.CompanyName))
                {
                    var companySize = graphics.MeasureString(reportData.CompanyName, subtitleFont);
                    graphics.DrawString(reportData.CompanyName, subtitleFont, blueBrush,
                        template.MarginLeft + (pageWidth - companySize.Width) / 2, currentY);
                    currentY += subtitleFont.Height + 5;

                    if (!string.IsNullOrEmpty(reportData.CompanyAddress))
                    {
                        var addressSize = graphics.MeasureString(reportData.CompanyAddress, normalFont);
                        graphics.DrawString(reportData.CompanyAddress, normalFont, grayBrush,
                            template.MarginLeft + (pageWidth - addressSize.Width) / 2, currentY);
                        currentY += normalFont.Height + 2;
                    }

                    if (!string.IsNullOrEmpty(reportData.CompanyPhone))
                    {
                        var phoneText = $"هاتف: {reportData.CompanyPhone}";
                        var phoneSize = graphics.MeasureString(phoneText, normalFont);
                        graphics.DrawString(phoneText, normalFont, grayBrush,
                            template.MarginLeft + (pageWidth - phoneSize.Width) / 2, currentY);
                        currentY += normalFont.Height + 15;
                    }
                }

                // عنوان التقرير
                var titleSize = graphics.MeasureString(reportData.Title, titleFont);
                graphics.DrawString(reportData.Title, titleFont, blackBrush,
                    template.MarginLeft + (pageWidth - titleSize.Width) / 2, currentY);
                currentY += titleFont.Height + 10;

                // العنوان الفرعي (إذا كان متاحاً)
                if (!string.IsNullOrEmpty(reportData.Subtitle))
                {
                    var subtitleSize = graphics.MeasureString(reportData.Subtitle, subtitleFont);
                    graphics.DrawString(reportData.Subtitle, subtitleFont, grayBrush,
                        template.MarginLeft + (pageWidth - subtitleSize.Width) / 2, currentY);
                    currentY += subtitleFont.Height + 10;
                }

                // معلومات التقرير
                graphics.DrawString($"تاريخ الإنشاء: {reportData.GeneratedDate:dd/MM/yyyy HH:mm}", normalFont, grayBrush, template.MarginLeft, currentY);
                if (!string.IsNullOrEmpty(reportData.GeneratedBy))
                {
                    graphics.DrawString($"بواسطة: {reportData.GeneratedBy}", normalFont, grayBrush,
                        template.MarginLeft + pageWidth - 200, currentY);
                }
                currentY += normalFont.Height + 15;

                // خط فاصل
                graphics.DrawLine(new Pen(Color.Black, 2), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                currentY += 15;

                // رؤوس الأعمدة
                if (reportData.Headers.Count > 0)
                {
                    var columnWidth = pageWidth / reportData.Headers.Count;
                    var headerY = currentY;

                    for (int i = 0; i < reportData.Headers.Count; i++)
                    {
                        var headerX = template.MarginLeft + (i * columnWidth);
                        graphics.DrawString(reportData.Headers[i], headerFont, blackBrush, headerX, headerY);
                    }

                    currentY += headerFont.Height + 5;

                    // خط تحت الرؤوس
                    graphics.DrawLine(new Pen(Color.Gray, 1), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                    currentY += 10;

                    // بيانات التقرير
                    foreach (var row in reportData.Data)
                    {
                        for (int i = 0; i < reportData.Headers.Count && i < row.Count; i++)
                        {
                            var cellX = template.MarginLeft + (i * columnWidth);
                            var cellValue = row.Values.ElementAtOrDefault(i)?.ToString() ?? "";
                            graphics.DrawString(cellValue, normalFont, blackBrush, cellX, currentY);
                        }
                        currentY += normalFont.Height + 3;

                        // التحقق من نهاية الصفحة
                        if (currentY > template.Height - template.MarginBottom - 100)
                        {
                            e.HasMorePages = true;
                            break;
                        }
                    }
                }

                // الملخص (إذا كان متاحاً)
                if (reportData.Summary.Count > 0)
                {
                    currentY += 20;

                    // خط فاصل
                    graphics.DrawLine(new Pen(Color.Black, 1), template.MarginLeft, currentY, template.MarginLeft + pageWidth, currentY);
                    currentY += 15;

                    // عنوان الملخص
                    graphics.DrawString("ملخص التقرير", headerFont, blackBrush, template.MarginLeft, currentY);
                    currentY += headerFont.Height + 10;

                    // بيانات الملخص
                    foreach (var summary in reportData.Summary)
                    {
                        graphics.DrawString($"{summary.Key}: {summary.Value}", normalFont, blackBrush, template.MarginLeft, currentY);
                        currentY += normalFont.Height + 3;
                    }
                }

                // تذييل الصفحة
                var footerY = template.Height - template.MarginBottom - 30;
                graphics.DrawLine(new Pen(Color.Gray, 1), template.MarginLeft, footerY, template.MarginLeft + pageWidth, footerY);
                footerY += 10;

                var pageInfo = $"صفحة 1 - طُبع في {DateTime.Now:dd/MM/yyyy HH:mm}";
                var pageInfoSize = graphics.MeasureString(pageInfo, smallFont);
                graphics.DrawString(pageInfo, smallFont, grayBrush,
                    template.MarginLeft + (pageWidth - pageInfoSize.Width) / 2, footerY);

                // تنظيف الموارد
                titleFont.Dispose();
                subtitleFont.Dispose();
                headerFont.Dispose();
                normalFont.Dispose();
                smallFont.Dispose();
                blackBrush.Dispose();
                grayBrush.Dispose();
                blueBrush.Dispose();
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، اطبع رسالة خطأ بسيطة
                var errorFont = new Font("Tahoma", 10);
                var errorBrush = new SolidBrush(Color.Red);
                e.Graphics.DrawString($"خطأ في طباعة التقرير: {ex.Message}", errorFont, errorBrush, 10, 10);
                errorFont.Dispose();
                errorBrush.Dispose();
            }
        }

        /// <summary>
        /// طباعة صفحة عامة
        /// </summary>
        private void PrintGenericPage(object sender, PrintPageEventArgs e, Models.PrintDocument document)
        {
            var graphics = e.Graphics;
            var template = GetTemplate(document.TemplateId) ?? GetDefaultTemplate(document.Type);

            var blackBrush = new SolidBrush(Color.Black);
            var titleFont = new Font(template.FontFamily, template.FontSize + 4, FontStyle.Bold);
            var normalFont = new Font(template.FontFamily, template.FontSize);

            var currentY = template.MarginTop;
            var pageWidth = e.PageBounds.Width - template.MarginLeft - template.MarginRight;

            // عنوان المستند
            graphics.DrawString(document.Title, titleFont, blackBrush, template.MarginLeft, currentY);
            currentY += titleFont.Height + 10;

            // تاريخ الإنشاء
            var dateText = $"التاريخ: {document.CreatedDate:dd/MM/yyyy HH:mm}";
            graphics.DrawString(dateText, normalFont, blackBrush, template.MarginLeft, currentY);
            currentY += normalFont.Height + 15;

            // خط فاصل
            graphics.DrawLine(new Pen(Color.Black), template.MarginLeft, currentY,
                template.MarginLeft + pageWidth, currentY);
            currentY += 10;

            // محتوى المستند
            var contentLines = document.Content.Split('\n');
            foreach (var line in contentLines)
            {
                if (!string.IsNullOrWhiteSpace(line))
                {
                    graphics.DrawString(line, normalFont, blackBrush, template.MarginLeft, currentY);
                }
                currentY += normalFont.Height + 5;

                // التحقق من نهاية الصفحة
                if (currentY > e.PageBounds.Height - template.MarginBottom)
                {
                    e.HasMorePages = true;
                    break;
                }
            }

            // تنظيف الموارد
            blackBrush.Dispose();
            titleFont.Dispose();
            normalFont.Dispose();
        }

        #endregion

        #region الإحصائيات والحفظ

        /// <summary>
        /// تحميل الإحصائيات
        /// </summary>
        private void LoadStatistics()
        {
            _statistics = new PrintStatistics
            {
                PeriodStart = DateTime.Today.AddDays(-30),
                PeriodEnd = DateTime.Today
            };
        }

        /// <summary>
        /// تحديث إحصائيات الطباعة
        /// </summary>
        private async Task UpdatePrintStatistics(PrintResult result)
        {
            _statistics.TotalPrintJobs++;
            
            if (result.Success)
            {
                _statistics.SuccessfulJobs++;
                _statistics.TotalPages += result.PagesPrinted;
            }
            else
            {
                _statistics.FailedJobs++;
            }

            _statistics.TotalPrintTime = _statistics.TotalPrintTime.Add(result.Duration);

            // تحديث استخدام الطابعة
            if (!string.IsNullOrEmpty(result.PrinterUsed))
            {
                if (_statistics.PrinterUsage.ContainsKey(result.PrinterUsed))
                {
                    _statistics.PrinterUsage[result.PrinterUsed]++;
                }
                else
                {
                    _statistics.PrinterUsage[result.PrinterUsed] = 1;
                }
            }
        }

        /// <summary>
        /// حفظ إعدادات الطابعة
        /// </summary>
        private async Task SavePrinterSettings()
        {
            // سيتم تنفيذها لاحقاً عند إضافة جداول الطابعات لقاعدة البيانات
            await Task.CompletedTask;
        }

        /// <summary>
        /// الحصول على إحصائيات الطباعة
        /// </summary>
        public PrintStatistics GetPrintStatistics()
        {
            return _statistics;
        }

        #endregion
    }
}
