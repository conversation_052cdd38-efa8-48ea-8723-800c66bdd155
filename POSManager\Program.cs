using POSManager.Forms;
using POSManager.Data;
using System;
using System.Threading.Tasks;

namespace POSManager;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static async Task Main(string[] args)
    {
        // التحقق من وجود معامل للاختبار
        if (args.Length > 0 && args[0] == "--test")
        {
            Console.WriteLine("🧪 تشغيل وضع الاختبار...");
            await TestRunner.RunAllTestsAsync();
            Console.WriteLine("\n✅ انتهى الاختبار");
            return;
        }

        // اختبار بسيط
        if (args.Length > 0 && args[0] == "--simple-test")
        {
            Console.WriteLine("🧪 تشغيل الاختبار البسيط...");

            // إنشاء قاعدة البيانات أولاً
            DatabaseManager.InitializeDatabase();

            await SimpleTest.RunSimpleTestAsync();
            Console.WriteLine("\n✅ انتهى الاختبار البسيط");
            return;
        }

        // فحص قاعدة البيانات
        if (args.Length > 0 && args[0] == "--inspect-db")
        {
            await DatabaseInspector.InspectDatabase();
            return;
        }

        // اختبار إضافة منتج
        if (args.Length > 0 && args[0] == "--test-product")
        {
            await ProductTest.TestAddProduct();
            return;
        }

        // اختبار قاعدة البيانات
        if (args.Length > 0 && args[0] == "--db-test")
        {
            Console.WriteLine("🔍 تشغيل اختبار قاعدة البيانات...");
            await DatabaseTest.TestDatabaseAsync();
            Console.WriteLine("\n✅ انتهى اختبار قاعدة البيانات");
            return;
        }

        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // تشغيل التطبيق بشاشة تسجيل الدخول
        Application.Run(new LoginForm());
    }
}