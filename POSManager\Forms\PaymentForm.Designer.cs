namespace POSManager.Forms
{
    partial class PaymentForm
    {
        private System.ComponentModel.IContainer components = null;
        private TableLayoutPanel mainLayout;
        private Panel infoPanel;
        private Panel paymentPanel;
        private Panel quickAmountPanel;
        private Panel buttonPanel;
        private Label lblTotalAmount;
        private Label lblPaymentMethod;
        private ComboBox cmbPaymentMethod;
        private Label lblPaidAmount;
        private TextBox txtPaidAmount;
        private Label lblChangeAmount;
        private Label lblNotes;
        private TextBox txtNotes;
        private Button btnExactAmount;
        private Button btnQuick10;
        private Button btnQuick20;
        private Button btnQuick50;
        private Button btnQuick100;
        private Button btnConfirm;
        private Button btnCancel;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.mainLayout = new TableLayoutPanel();
            this.infoPanel = new Panel();
            this.paymentPanel = new Panel();
            this.quickAmountPanel = new Panel();
            this.buttonPanel = new Panel();
            this.lblTotalAmount = new Label();
            this.lblPaymentMethod = new Label();
            this.cmbPaymentMethod = new ComboBox();
            this.lblPaidAmount = new Label();
            this.txtPaidAmount = new TextBox();
            this.lblChangeAmount = new Label();
            this.lblNotes = new Label();
            this.txtNotes = new TextBox();
            this.btnExactAmount = new Button();
            this.btnQuick10 = new Button();
            this.btnQuick20 = new Button();
            this.btnQuick50 = new Button();
            this.btnQuick100 = new Button();
            this.btnConfirm = new Button();
            this.btnCancel = new Button();

            this.mainLayout.SuspendLayout();
            this.infoPanel.SuspendLayout();
            this.paymentPanel.SuspendLayout();
            this.quickAmountPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();

            // 
            // PaymentForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 450);
            this.Controls.Add(this.mainLayout);
            this.Font = new Font("Segoe UI", 9F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "PaymentForm";
            this.RightToLeft = RightToLeft.Yes;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "معالجة الدفع";

            // 
            // mainLayout
            // 
            this.mainLayout.ColumnCount = 1;
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            this.mainLayout.Controls.Add(this.infoPanel, 0, 0);
            this.mainLayout.Controls.Add(this.paymentPanel, 0, 1);
            this.mainLayout.Controls.Add(this.quickAmountPanel, 0, 2);
            this.mainLayout.Controls.Add(this.buttonPanel, 0, 3);
            this.mainLayout.Dock = DockStyle.Fill;
            this.mainLayout.Location = new Point(0, 0);
            this.mainLayout.Margin = new Padding(4);
            this.mainLayout.Name = "mainLayout";
            this.mainLayout.RowCount = 4;
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 60F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 25F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 15F));
            this.mainLayout.Size = new Size(500, 450);
            this.mainLayout.TabIndex = 0;

            // 
            // infoPanel
            // 
            this.infoPanel.Controls.Add(this.lblTotalAmount);
            this.infoPanel.Dock = DockStyle.Fill;
            this.infoPanel.Location = new Point(4, 4);
            this.infoPanel.Margin = new Padding(4);
            this.infoPanel.Name = "infoPanel";
            this.infoPanel.Size = new Size(492, 52);
            this.infoPanel.TabIndex = 0;

            // 
            // lblTotalAmount
            // 
            this.lblTotalAmount.Dock = DockStyle.Fill;
            this.lblTotalAmount.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblTotalAmount.ForeColor = Color.DarkBlue;
            this.lblTotalAmount.Location = new Point(0, 0);
            this.lblTotalAmount.Margin = new Padding(4, 0, 4, 0);
            this.lblTotalAmount.Name = "lblTotalAmount";
            this.lblTotalAmount.Size = new Size(492, 52);
            this.lblTotalAmount.TabIndex = 0;
            this.lblTotalAmount.Text = "المبلغ المطلوب: 0.00";
            this.lblTotalAmount.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // paymentPanel
            // 
            this.paymentPanel.Controls.Add(this.lblPaymentMethod);
            this.paymentPanel.Controls.Add(this.cmbPaymentMethod);
            this.paymentPanel.Controls.Add(this.lblPaidAmount);
            this.paymentPanel.Controls.Add(this.txtPaidAmount);
            this.paymentPanel.Controls.Add(this.lblChangeAmount);
            this.paymentPanel.Controls.Add(this.lblNotes);
            this.paymentPanel.Controls.Add(this.txtNotes);
            this.paymentPanel.Dock = DockStyle.Fill;
            this.paymentPanel.Location = new Point(4, 64);
            this.paymentPanel.Margin = new Padding(4);
            this.paymentPanel.Name = "paymentPanel";
            this.paymentPanel.Padding = new Padding(20);
            this.paymentPanel.Size = new Size(492, 228);
            this.paymentPanel.TabIndex = 1;

            // 
            // lblPaymentMethod
            // 
            this.lblPaymentMethod.AutoSize = true;
            this.lblPaymentMethod.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPaymentMethod.Location = new Point(400, 25);
            this.lblPaymentMethod.Margin = new Padding(4, 0, 4, 0);
            this.lblPaymentMethod.Name = "lblPaymentMethod";
            this.lblPaymentMethod.Size = new Size(88, 23);
            this.lblPaymentMethod.TabIndex = 0;
            this.lblPaymentMethod.Text = "طريقة الدفع";

            // 
            // cmbPaymentMethod
            // 
            this.cmbPaymentMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentMethod.Font = new Font("Segoe UI", 11F);
            this.cmbPaymentMethod.FormattingEnabled = true;
            this.cmbPaymentMethod.Location = new Point(24, 22);
            this.cmbPaymentMethod.Margin = new Padding(4);
            this.cmbPaymentMethod.Name = "cmbPaymentMethod";
            this.cmbPaymentMethod.Size = new Size(368, 33);
            this.cmbPaymentMethod.TabIndex = 1;
            this.cmbPaymentMethod.SelectedIndexChanged += new EventHandler(this.cmbPaymentMethod_SelectedIndexChanged);

            // 
            // lblPaidAmount
            // 
            this.lblPaidAmount.AutoSize = true;
            this.lblPaidAmount.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPaidAmount.Location = new Point(400, 75);
            this.lblPaidAmount.Margin = new Padding(4, 0, 4, 0);
            this.lblPaidAmount.Name = "lblPaidAmount";
            this.lblPaidAmount.Size = new Size(88, 23);
            this.lblPaidAmount.TabIndex = 2;
            this.lblPaidAmount.Text = "المبلغ المدفوع";

            // 
            // txtPaidAmount
            // 
            this.txtPaidAmount.Font = new Font("Segoe UI", 12F);
            this.txtPaidAmount.Location = new Point(24, 72);
            this.txtPaidAmount.Margin = new Padding(4);
            this.txtPaidAmount.Name = "txtPaidAmount";
            this.txtPaidAmount.Size = new Size(368, 34);
            this.txtPaidAmount.TabIndex = 3;
            this.txtPaidAmount.TextAlign = HorizontalAlignment.Center;
            this.txtPaidAmount.TextChanged += new EventHandler(this.txtPaidAmount_TextChanged);
            this.txtPaidAmount.KeyDown += new KeyEventHandler(this.txtPaidAmount_KeyDown);
            this.txtPaidAmount.KeyPress += new KeyPressEventHandler(this.txtPaidAmount_KeyPress);

            // 
            // lblChangeAmount
            // 
            this.lblChangeAmount.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblChangeAmount.ForeColor = Color.Green;
            this.lblChangeAmount.Location = new Point(24, 110);
            this.lblChangeAmount.Margin = new Padding(4, 0, 4, 0);
            this.lblChangeAmount.Name = "lblChangeAmount";
            this.lblChangeAmount.Size = new Size(368, 30);
            this.lblChangeAmount.TabIndex = 4;
            this.lblChangeAmount.Text = "الباقي: 0.00";
            this.lblChangeAmount.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // lblNotes
            // 
            this.lblNotes.AutoSize = true;
            this.lblNotes.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblNotes.Location = new Point(440, 150);
            this.lblNotes.Margin = new Padding(4, 0, 4, 0);
            this.lblNotes.Name = "lblNotes";
            this.lblNotes.Size = new Size(48, 23);
            this.lblNotes.TabIndex = 5;
            this.lblNotes.Text = "ملاحظات";

            // 
            // txtNotes
            // 
            this.txtNotes.Font = new Font("Segoe UI", 10F);
            this.txtNotes.Location = new Point(24, 147);
            this.txtNotes.Margin = new Padding(4);
            this.txtNotes.Multiline = true;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Size = new Size(368, 60);
            this.txtNotes.TabIndex = 6;

            // 
            // quickAmountPanel
            // 
            this.quickAmountPanel.Controls.Add(this.btnExactAmount);
            this.quickAmountPanel.Controls.Add(this.btnQuick10);
            this.quickAmountPanel.Controls.Add(this.btnQuick20);
            this.quickAmountPanel.Controls.Add(this.btnQuick50);
            this.quickAmountPanel.Controls.Add(this.btnQuick100);
            this.quickAmountPanel.Dock = DockStyle.Fill;
            this.quickAmountPanel.Location = new Point(4, 300);
            this.quickAmountPanel.Margin = new Padding(4);
            this.quickAmountPanel.Name = "quickAmountPanel";
            this.quickAmountPanel.Padding = new Padding(20, 10, 20, 10);
            this.quickAmountPanel.Size = new Size(492, 89);
            this.quickAmountPanel.TabIndex = 2;

            // 
            // btnExactAmount
            // 
            this.btnExactAmount.BackColor = Color.LightBlue;
            this.btnExactAmount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnExactAmount.Location = new Point(380, 15);
            this.btnExactAmount.Margin = new Padding(4);
            this.btnExactAmount.Name = "btnExactAmount";
            this.btnExactAmount.Size = new Size(88, 35);
            this.btnExactAmount.TabIndex = 0;
            this.btnExactAmount.Text = "المبلغ بالضبط";
            this.btnExactAmount.UseVisualStyleBackColor = false;
            this.btnExactAmount.Click += new EventHandler(this.btnExactAmount_Click);

            // 
            // btnQuick10
            // 
            this.btnQuick10.BackColor = Color.LightGreen;
            this.btnQuick10.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnQuick10.Location = new Point(284, 15);
            this.btnQuick10.Margin = new Padding(4);
            this.btnQuick10.Name = "btnQuick10";
            this.btnQuick10.Size = new Size(88, 35);
            this.btnQuick10.TabIndex = 1;
            this.btnQuick10.Tag = "10";
            this.btnQuick10.Text = "+10";
            this.btnQuick10.UseVisualStyleBackColor = false;
            this.btnQuick10.Click += new EventHandler(this.btnQuickAmount_Click);

            // 
            // btnQuick20
            // 
            this.btnQuick20.BackColor = Color.LightGreen;
            this.btnQuick20.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnQuick20.Location = new Point(188, 15);
            this.btnQuick20.Margin = new Padding(4);
            this.btnQuick20.Name = "btnQuick20";
            this.btnQuick20.Size = new Size(88, 35);
            this.btnQuick20.TabIndex = 2;
            this.btnQuick20.Tag = "20";
            this.btnQuick20.Text = "+20";
            this.btnQuick20.UseVisualStyleBackColor = false;
            this.btnQuick20.Click += new EventHandler(this.btnQuickAmount_Click);

            // 
            // btnQuick50
            // 
            this.btnQuick50.BackColor = Color.LightGreen;
            this.btnQuick50.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnQuick50.Location = new Point(92, 15);
            this.btnQuick50.Margin = new Padding(4);
            this.btnQuick50.Name = "btnQuick50";
            this.btnQuick50.Size = new Size(88, 35);
            this.btnQuick50.TabIndex = 3;
            this.btnQuick50.Tag = "50";
            this.btnQuick50.Text = "+50";
            this.btnQuick50.UseVisualStyleBackColor = false;
            this.btnQuick50.Click += new EventHandler(this.btnQuickAmount_Click);

            // 
            // btnQuick100
            // 
            this.btnQuick100.BackColor = Color.LightGreen;
            this.btnQuick100.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.btnQuick100.Location = new Point(24, 15);
            this.btnQuick100.Margin = new Padding(4);
            this.btnQuick100.Name = "btnQuick100";
            this.btnQuick100.Size = new Size(60, 35);
            this.btnQuick100.TabIndex = 4;
            this.btnQuick100.Tag = "100";
            this.btnQuick100.Text = "+100";
            this.btnQuick100.UseVisualStyleBackColor = false;
            this.btnQuick100.Click += new EventHandler(this.btnQuickAmount_Click);

            // 
            // buttonPanel
            // 
            this.buttonPanel.Controls.Add(this.btnCancel);
            this.buttonPanel.Controls.Add(this.btnConfirm);
            this.buttonPanel.Dock = DockStyle.Fill;
            this.buttonPanel.Location = new Point(4, 397);
            this.buttonPanel.Margin = new Padding(4);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new Size(492, 49);
            this.buttonPanel.TabIndex = 3;

            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.Location = new Point(260, 8);
            this.btnCancel.Margin = new Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // 
            // btnConfirm
            // 
            this.btnConfirm.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnConfirm.BackColor = Color.Green;
            this.btnConfirm.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnConfirm.ForeColor = Color.White;
            this.btnConfirm.Location = new Point(368, 8);
            this.btnConfirm.Margin = new Padding(4);
            this.btnConfirm.Name = "btnConfirm";
            this.btnConfirm.Size = new Size(120, 35);
            this.btnConfirm.TabIndex = 0;
            this.btnConfirm.Text = "تأكيد الدفع";
            this.btnConfirm.UseVisualStyleBackColor = false;
            this.btnConfirm.Click += new EventHandler(this.btnConfirm_Click);

            this.mainLayout.ResumeLayout(false);
            this.infoPanel.ResumeLayout(false);
            this.paymentPanel.ResumeLayout(false);
            this.paymentPanel.PerformLayout();
            this.quickAmountPanel.ResumeLayout(false);
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);
        }
    }
}
