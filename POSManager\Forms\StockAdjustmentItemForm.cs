using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class StockAdjustmentItemForm : Form
    {
        private readonly ProductService _productService;
        private List<Product> _products;
        public StockAdjustmentItem? AdjustmentItem { get; private set; }

        // UI Controls
        private ComboBox cmbProduct;
        private TextBox txtProductCode, txtProductName, txtCurrentStock, txtNotes;
        private NumericUpDown nudAdjustedStock;
        private Label lblVariance;
        private ComboBox cmbReason;
        private Button btnSave, btnCancel;

        public StockAdjustmentItemForm()
        {
            _productService = ProductService.Instance;
            _products = new List<Product>();
            InitializeComponent();
            SetupForm();
            _ = LoadProductsAsync();
        }

        public StockAdjustmentItemForm(StockAdjustmentItem item) : this()
        {
            AdjustmentItem = item;
            LoadItemData();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(500, 400);
            this.Text = "إضافة/تعديل منتج التعديل";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Product selection
            var lblProduct = new Label { Text = "المنتج:", Location = new Point(0, 20), Size = new Size(80, 20) };
            cmbProduct = new ComboBox 
            { 
                Location = new Point(90, 17), 
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Product details (read-only)
            var lblProductCode = new Label { Text = "كود المنتج:", Location = new Point(0, 60), Size = new Size(80, 20) };
            txtProductCode = new TextBox { Location = new Point(90, 57), Size = new Size(150, 25), ReadOnly = true };

            var lblProductName = new Label { Text = "اسم المنتج:", Location = new Point(0, 100), Size = new Size(80, 20) };
            txtProductName = new TextBox { Location = new Point(90, 97), Size = new Size(300, 25), ReadOnly = true };

            var lblCurrentStock = new Label { Text = "المخزون الحالي:", Location = new Point(0, 140), Size = new Size(80, 20) };
            txtCurrentStock = new TextBox { Location = new Point(90, 137), Size = new Size(100, 25), ReadOnly = true };

            // Adjustment details
            var lblAdjustedStock = new Label { Text = "المخزون المعدل:", Location = new Point(0, 180), Size = new Size(80, 20) };
            nudAdjustedStock = new NumericUpDown 
            { 
                Location = new Point(90, 177), 
                Size = new Size(100, 25),
                DecimalPlaces = 2,
                Minimum = 0,
                Maximum = 999999
            };

            var lblVarianceLabel = new Label { Text = "التباين:", Location = new Point(200, 180), Size = new Size(50, 20) };
            lblVariance = new Label 
            { 
                Text = "0", 
                Location = new Point(260, 180), 
                Size = new Size(100, 20),
                ForeColor = Color.Blue,
                Font = new Font(this.Font, FontStyle.Bold)
            };

            var lblReason = new Label { Text = "السبب:", Location = new Point(0, 220), Size = new Size(80, 20) };
            cmbReason = new ComboBox 
            { 
                Location = new Point(90, 217), 
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbReason.Items.AddRange(new[] { "جرد فعلي", "تلف", "انتهاء صلاحية", "خطأ في الإدخال", "أخرى" });

            var lblNotes = new Label { Text = "ملاحظات:", Location = new Point(0, 260), Size = new Size(80, 20) };
            txtNotes = new TextBox 
            { 
                Location = new Point(90, 257), 
                Size = new Size(300, 50),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Buttons
            btnSave = new Button 
            { 
                Text = "حفظ", 
                Location = new Point(90, 320), 
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            btnCancel = new Button 
            { 
                Text = "إلغاء", 
                Location = new Point(180, 320), 
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            panel.Controls.AddRange(new Control[] 
            {
                lblProduct, cmbProduct,
                lblProductCode, txtProductCode,
                lblProductName, txtProductName,
                lblCurrentStock, txtCurrentStock,
                lblAdjustedStock, nudAdjustedStock,
                lblVarianceLabel, lblVariance,
                lblReason, cmbReason,
                lblNotes, txtNotes,
                btnSave, btnCancel
            });

            this.Controls.Add(panel);
        }

        private void SetupForm()
        {
            cmbProduct.SelectedIndexChanged += CmbProduct_SelectedIndexChanged;
            nudAdjustedStock.ValueChanged += NudAdjustedStock_ValueChanged;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                _products = await _productService.GetAllProductsAsync();
                
                cmbProduct.DisplayMember = "DisplayText";
                cmbProduct.ValueMember = "Id";
                
                var productList = _products.Select(p => new
                {
                    Id = p.Id,
                    DisplayText = $"{p.Code} - {p.Name}"
                }).ToList();

                cmbProduct.DataSource = productList;
                cmbProduct.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadItemData()
        {
            if (AdjustmentItem == null) return;

            // Find and select the product
            var product = _products.FirstOrDefault(p => p.Id == AdjustmentItem.ProductId);
            if (product != null)
            {
                cmbProduct.SelectedValue = product.Id;
                LoadProductDetails(product);
            }

            nudAdjustedStock.Value = AdjustmentItem.AdjustedStock;
            cmbReason.Text = GetReasonText(AdjustmentItem.Reason);
            txtNotes.Text = AdjustmentItem.Notes ?? "";
            
            UpdateVariance();
        }

        private void CmbProduct_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbProduct.SelectedValue != null && int.TryParse(cmbProduct.SelectedValue.ToString(), out int productId))
            {
                var product = _products.FirstOrDefault(p => p.Id == productId);
                if (product != null)
                {
                    LoadProductDetails(product);
                }
            }
        }

        private void LoadProductDetails(Product product)
        {
            txtProductCode.Text = product.Code;
            txtProductName.Text = product.Name;
            txtCurrentStock.Text = product.Stock.ToString();
            
            nudAdjustedStock.Value = product.Stock;
            UpdateVariance();
        }

        private void NudAdjustedStock_ValueChanged(object sender, EventArgs e)
        {
            UpdateVariance();
        }

        private void UpdateVariance()
        {
            if (decimal.TryParse(txtCurrentStock.Text, out decimal currentStock))
            {
                var variance = nudAdjustedStock.Value - currentStock;
                lblVariance.Text = variance.ToString();
                lblVariance.ForeColor = variance >= 0 ? Color.Green : Color.Red;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            var selectedProductId = Convert.ToInt32(cmbProduct.SelectedValue);
            var product = _products.FirstOrDefault(p => p.Id == selectedProductId);
            
            if (product == null) return;

            AdjustmentItem = new StockAdjustmentItem
            {
                Id = AdjustmentItem?.Id ?? 0,
                ProductId = product.Id,
                ProductCode = product.Code,
                ProductName = product.Name,
                CurrentStock = product.Stock,
                AdjustedStock = nudAdjustedStock.Value,
                Variance = nudAdjustedStock.Value - product.Stock,
                Reason = GetReasonValue(cmbReason.Text),
                Notes = txtNotes.Text
            };

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (cmbProduct.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار منتج", "خطأ في التحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbProduct.Focus();
                return false;
            }

            if (cmbReason.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار سبب التعديل", "خطأ في التحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbReason.Focus();
                return false;
            }

            return true;
        }

        private string GetReasonText(string reason)
        {
            return reason switch
            {
                "PhysicalCount" => "جرد فعلي",
                "Damage" => "تلف",
                "Expiry" => "انتهاء صلاحية",
                "DataEntry" => "خطأ في الإدخال",
                "Other" => "أخرى",
                _ => reason
            };
        }

        private string GetReasonValue(string reasonText)
        {
            return reasonText switch
            {
                "جرد فعلي" => "PhysicalCount",
                "تلف" => "Damage",
                "انتهاء صلاحية" => "Expiry",
                "خطأ في الإدخال" => "DataEntry",
                "أخرى" => "Other",
                _ => "Other"
            };
        }
    }
}
