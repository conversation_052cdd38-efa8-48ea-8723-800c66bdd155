using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class UsersForm : Form
    {
        private List<User> _users = new List<User>();
        private User? _selectedUser = null;

        public UsersForm()
        {
            InitializeComponent();
            SetupForm();
            _ = LoadUsersAsync();
        }

        private void SetupForm()
        {
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // إعداد DataGridView
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dgvUsers.AutoGenerateColumns = false;
            dgvUsers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUsers.MultiSelect = false;
            dgvUsers.ReadOnly = true;
            dgvUsers.AllowUserToAddRows = false;
            dgvUsers.AllowUserToDeleteRows = false;

            // إضافة الأعمدة
            dgvUsers.Columns.Clear();
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "Id",
                HeaderText = "الرقم",
                Width = 60,
                Visible = false
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                DataPropertyName = "FullName",
                HeaderText = "الاسم الكامل",
                Width = 200
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                DataPropertyName = "Username",
                HeaderText = "اسم المستخدم",
                Width = 150
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                DataPropertyName = "Email",
                HeaderText = "البريد الإلكتروني",
                Width = 200
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Role",
                DataPropertyName = "RoleDisplayName",
                HeaderText = "الصلاحية",
                Width = 120
            });

            dgvUsers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                DataPropertyName = "IsActive",
                HeaderText = "نشط",
                Width = 60
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                DataPropertyName = "CreatedAtFormatted",
                HeaderText = "تاريخ الإنشاء",
                Width = 120
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastLogin",
                DataPropertyName = "LastLoginFormatted",
                HeaderText = "آخر دخول",
                Width = 120
            });

            // الأحداث
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            dgvUsers.CellDoubleClick += DgvUsers_CellDoubleClick;
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                lblStatus.Text = "جاري تحميل المستخدمين...";
                btnRefresh.Enabled = false;

                _users = await UserService.GetAllUsersAsync();
                
                // إضافة خصائص للعرض
                foreach (var user in _users)
                {
                    user.RoleDisplayName = GetRoleDisplayName(user.Role);
                    user.CreatedAtFormatted = user.CreatedAt.ToString("yyyy/MM/dd");
                    user.LastLoginFormatted = user.LastLogin?.ToString("yyyy/MM/dd HH:mm") ?? "لم يسجل دخول";
                }

                dgvUsers.DataSource = _users;
                
                lblStatus.Text = $"تم تحميل {_users.Count} مستخدم";
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
            finally
            {
                btnRefresh.Enabled = true;
            }
        }

        private string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.Admin => "مدير",
                UserRole.Cashier => "كاشير",
                _ => "غير محدد"
            };
        }

        private void DgvUsers_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var selectedIndex = dgvUsers.SelectedRows[0].Index;
                if (selectedIndex >= 0 && selectedIndex < _users.Count)
                {
                    _selectedUser = _users[selectedIndex];
                }
                else
                {
                    _selectedUser = null;
                }
            }
            else
            {
                _selectedUser = null;
            }

            UpdateButtonStates();
        }

        private void DgvUsers_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && _selectedUser != null)
            {
                EditUser();
            }
        }

        private void UpdateButtonStates()
        {
            var hasSelection = _selectedUser != null;
            var canModify = hasSelection && AuthService.CurrentUser?.Role == UserRole.Admin;
            var canDeleteUser = canModify && _selectedUser?.Id != AuthService.CurrentUser?.Id;

            btnEdit.Enabled = canModify;
            btnDelete.Enabled = canDeleteUser;
            btnChangePassword.Enabled = canModify;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            var addUserForm = new AddEditUserForm();
            if (addUserForm.ShowDialog() == DialogResult.OK)
            {
                await LoadUsersAsync();
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            EditUser();
        }

        private void EditUser()
        {
            if (_selectedUser == null) return;

            var editUserForm = new AddEditUserForm(_selectedUser);
            if (editUserForm.ShowDialog() == DialogResult.OK)
            {
                _ = LoadUsersAsync();
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null) return;

            var result = MessageBox.Show(
                $"هل تريد حذف المستخدم '{_selectedUser.FullName}'؟\n\nملاحظة: إذا كان للمستخدم مبيعات سابقة، سيتم إلغاء تفعيله فقط.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var success = await UserService.DeleteUserAsync(_selectedUser.Id);
                    if (success)
                    {
                        MessageBox.Show("تم حذف/إلغاء تفعيل المستخدم بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadUsersAsync();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المستخدم", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnChangePassword_Click(object sender, EventArgs e)
        {
            if (_selectedUser == null) return;

            var changePasswordForm = new ChangePasswordForm(_selectedUser);
            if (changePasswordForm.ShowDialog() == DialogResult.OK)
            {
                MessageBox.Show("تم تغيير كلمة المرور بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            await LoadUsersAsync();
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            await SearchUsersAsync();
        }

        private async Task SearchUsersAsync()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();

                if (string.IsNullOrEmpty(searchTerm))
                {
                    dgvUsers.DataSource = _users;
                }
                else
                {
                    var filteredUsers = await UserService.SearchUsersAsync(searchTerm);

                    // إضافة خصائص للعرض
                    foreach (var user in filteredUsers)
                    {
                        user.RoleDisplayName = GetRoleDisplayName(user.Role);
                        user.CreatedAtFormatted = user.CreatedAt.ToString("yyyy/MM/dd");
                        user.LastLoginFormatted = user.LastLogin?.ToString("yyyy/MM/dd HH:mm") ?? "لم يسجل دخول";
                    }

                    dgvUsers.DataSource = filteredUsers;
                }

                lblStatus.Text = $"عدد النتائج: {dgvUsers.Rows.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                txtSearch.Clear();
            }
        }

        private void UsersForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F1:
                    btnAdd_Click(sender, e);
                    break;
                case Keys.F2:
                    if (btnEdit.Enabled)
                        btnEdit_Click(sender, e);
                    break;
                case Keys.Delete:
                    if (btnDelete.Enabled)
                        btnDelete_Click(sender, e);
                    break;
                case Keys.F5:
                    btnRefresh_Click(sender, e);
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
