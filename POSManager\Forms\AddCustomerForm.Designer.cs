namespace POSManager.Forms
{
    partial class AddCustomerForm
    {
        private System.ComponentModel.IContainer components = null;
        private TableLayoutPanel mainLayout;
        private Panel formPanel;
        private Panel buttonPanel;
        private Label lblName;
        private TextBox txtName;
        private Label lblPhone;
        private TextBox txtPhone;
        private Label lblEmail;
        private TextBox txtEmail;
        private Label lblAddress;
        private TextBox txtAddress;
        private Button btnSave;
        private Button btnCancel;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.mainLayout = new TableLayoutPanel();
            this.formPanel = new Panel();
            this.buttonPanel = new Panel();
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblPhone = new Label();
            this.txtPhone = new TextBox();
            this.lblEmail = new Label();
            this.txtEmail = new TextBox();
            this.lblAddress = new Label();
            this.txtAddress = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            this.mainLayout.SuspendLayout();
            this.formPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();

            // 
            // AddCustomerForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 350);
            this.Controls.Add(this.mainLayout);
            this.Font = new Font("Segoe UI", 9F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AddCustomerForm";
            this.RightToLeft = RightToLeft.Yes;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة عميل جديد";

            // 
            // mainLayout
            // 
            this.mainLayout.ColumnCount = 1;
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            this.mainLayout.Controls.Add(this.formPanel, 0, 0);
            this.mainLayout.Controls.Add(this.buttonPanel, 0, 1);
            this.mainLayout.Dock = DockStyle.Fill;
            this.mainLayout.Location = new Point(0, 0);
            this.mainLayout.Margin = new Padding(4);
            this.mainLayout.Name = "mainLayout";
            this.mainLayout.RowCount = 2;
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            this.mainLayout.Size = new Size(500, 350);
            this.mainLayout.TabIndex = 0;

            // 
            // formPanel
            // 
            this.formPanel.Controls.Add(this.lblName);
            this.formPanel.Controls.Add(this.txtName);
            this.formPanel.Controls.Add(this.lblPhone);
            this.formPanel.Controls.Add(this.txtPhone);
            this.formPanel.Controls.Add(this.lblEmail);
            this.formPanel.Controls.Add(this.txtEmail);
            this.formPanel.Controls.Add(this.lblAddress);
            this.formPanel.Controls.Add(this.txtAddress);
            this.formPanel.Dock = DockStyle.Fill;
            this.formPanel.Location = new Point(4, 4);
            this.formPanel.Margin = new Padding(4);
            this.formPanel.Name = "formPanel";
            this.formPanel.Padding = new Padding(20);
            this.formPanel.Size = new Size(492, 282);
            this.formPanel.TabIndex = 0;

            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblName.Location = new Point(400, 30);
            this.lblName.Margin = new Padding(4, 0, 4, 0);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(88, 23);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "اسم العميل *";

            // 
            // txtName
            // 
            this.txtName.Font = new Font("Segoe UI", 11F);
            this.txtName.Location = new Point(24, 27);
            this.txtName.Margin = new Padding(4);
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(368, 32);
            this.txtName.TabIndex = 1;
            this.txtName.KeyDown += new KeyEventHandler(this.txtName_KeyDown);

            // 
            // lblPhone
            // 
            this.lblPhone.AutoSize = true;
            this.lblPhone.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPhone.Location = new Point(440, 80);
            this.lblPhone.Margin = new Padding(4, 0, 4, 0);
            this.lblPhone.Name = "lblPhone";
            this.lblPhone.Size = new Size(48, 23);
            this.lblPhone.TabIndex = 2;
            this.lblPhone.Text = "الهاتف";

            // 
            // txtPhone
            // 
            this.txtPhone.Font = new Font("Segoe UI", 11F);
            this.txtPhone.Location = new Point(24, 77);
            this.txtPhone.Margin = new Padding(4);
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.Size = new Size(368, 32);
            this.txtPhone.TabIndex = 3;
            this.txtPhone.KeyDown += new KeyEventHandler(this.txtPhone_KeyDown);
            this.txtPhone.KeyPress += new KeyPressEventHandler(this.txtPhone_KeyPress);

            // 
            // lblEmail
            // 
            this.lblEmail.AutoSize = true;
            this.lblEmail.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblEmail.Location = new Point(380, 130);
            this.lblEmail.Margin = new Padding(4, 0, 4, 0);
            this.lblEmail.Name = "lblEmail";
            this.lblEmail.Size = new Size(108, 23);
            this.lblEmail.TabIndex = 4;
            this.lblEmail.Text = "البريد الإلكتروني";

            // 
            // txtEmail
            // 
            this.txtEmail.Font = new Font("Segoe UI", 11F);
            this.txtEmail.Location = new Point(24, 127);
            this.txtEmail.Margin = new Padding(4);
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Size = new Size(368, 32);
            this.txtEmail.TabIndex = 5;
            this.txtEmail.KeyDown += new KeyEventHandler(this.txtEmail_KeyDown);

            // 
            // lblAddress
            // 
            this.lblAddress.AutoSize = true;
            this.lblAddress.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblAddress.Location = new Point(440, 180);
            this.lblAddress.Margin = new Padding(4, 0, 4, 0);
            this.lblAddress.Name = "lblAddress";
            this.lblAddress.Size = new Size(48, 23);
            this.lblAddress.TabIndex = 6;
            this.lblAddress.Text = "العنوان";

            // 
            // txtAddress
            // 
            this.txtAddress.Font = new Font("Segoe UI", 11F);
            this.txtAddress.Location = new Point(24, 177);
            this.txtAddress.Margin = new Padding(4);
            this.txtAddress.Multiline = true;
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Size = new Size(368, 80);
            this.txtAddress.TabIndex = 7;
            this.txtAddress.KeyDown += new KeyEventHandler(this.txtAddress_KeyDown);

            // 
            // buttonPanel
            // 
            this.buttonPanel.Controls.Add(this.btnCancel);
            this.buttonPanel.Controls.Add(this.btnSave);
            this.buttonPanel.Dock = DockStyle.Fill;
            this.buttonPanel.Location = new Point(4, 294);
            this.buttonPanel.Margin = new Padding(4);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new Size(492, 52);
            this.buttonPanel.TabIndex = 1;

            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.Location = new Point(260, 8);
            this.btnCancel.Margin = new Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(100, 40);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnSave.BackColor = Color.Green;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(368, 8);
            this.btnSave.Margin = new Padding(4);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(120, 40);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.mainLayout.ResumeLayout(false);
            this.formPanel.ResumeLayout(false);
            this.formPanel.PerformLayout();
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);
        }
    }
}
