using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;

namespace POSManager.Services
{
    public class CategoryService
    {
        public static async Task<List<Category>> GetAllCategoriesAsync()
        {
            var categories = new List<Category>();
            
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT c.*, 
                           (SELECT COUNT(*) FROM Products p WHERE p.CategoryId = c.Id AND p.IsActive = 1) as ProductCount
                    FROM Categories c
                    WHERE c.IsActive = 1
                    ORDER BY c.Name";
                
                using var command = new SqliteCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    categories.Add(new Category
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString()),
                        ProductCount = Convert.ToInt32(reader["ProductCount"])
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الفئات: {ex.Message}");
            }
            
            return categories;
        }
        
        public static async Task<Category?> GetCategoryByIdAsync(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT c.*, 
                           (SELECT COUNT(*) FROM Products p WHERE p.CategoryId = c.Id AND p.IsActive = 1) as ProductCount
                    FROM Categories c
                    WHERE c.Id = @id";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    return new Category
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString()),
                        ProductCount = Convert.ToInt32(reader["ProductCount"])
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الفئة: {ex.Message}");
            }
            
            return null;
        }
        
        public static async Task<bool> AddCategoryAsync(Category category)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                // التحقق من عدم تكرار الاسم
                var checkQuery = "SELECT COUNT(*) FROM Categories WHERE Name = @name AND IsActive = 1";
                using var checkCommand = new SqliteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@name", category.Name);
                var existingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                
                if (existingCount > 0)
                {
                    throw new Exception("اسم الفئة موجود مسبقاً");
                }
                
                var query = @"
                    INSERT INTO Categories (Name, Description, IsActive, CreatedAt)
                    VALUES (@name, @description, @isActive, @createdAt)";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@name", category.Name);
                command.Parameters.AddWithValue("@description", category.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isActive", category.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                
                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الفئة: {ex.Message}");
            }
        }
        
        public static async Task<bool> UpdateCategoryAsync(Category category)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                // التحقق من عدم تكرار الاسم
                var checkQuery = "SELECT COUNT(*) FROM Categories WHERE Name = @name AND Id != @id AND IsActive = 1";
                using var checkCommand = new SqliteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@name", category.Name);
                checkCommand.Parameters.AddWithValue("@id", category.Id);
                var existingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                
                if (existingCount > 0)
                {
                    throw new Exception("اسم الفئة موجود مسبقاً");
                }
                
                var query = @"
                    UPDATE Categories 
                    SET Name = @name, Description = @description, IsActive = @isActive
                    WHERE Id = @id";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@id", category.Id);
                command.Parameters.AddWithValue("@name", category.Name);
                command.Parameters.AddWithValue("@description", category.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isActive", category.IsActive ? 1 : 0);
                
                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الفئة: {ex.Message}");
            }
        }
        
        public static async Task<bool> DeleteCategoryAsync(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                // التحقق من وجود منتجات في الفئة
                var checkQuery = "SELECT COUNT(*) FROM Products WHERE CategoryId = @id AND IsActive = 1";
                using var checkCommand = new SqliteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@id", id);
                var productCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                
                if (productCount > 0)
                {
                    throw new Exception($"لا يمكن حذف الفئة لأنها تحتوي على {productCount} منتج");
                }
                
                // إلغاء تفعيل الفئة
                var deleteQuery = "UPDATE Categories SET IsActive = 0 WHERE Id = @id";
                using var deleteCommand = new SqliteCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@id", id);
                
                return await deleteCommand.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الفئة: {ex.Message}");
            }
        }
        
        public static async Task<List<Category>> SearchCategoriesAsync(string searchTerm)
        {
            var categories = new List<Category>();
            
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT c.*, 
                           (SELECT COUNT(*) FROM Products p WHERE p.CategoryId = c.Id AND p.IsActive = 1) as ProductCount
                    FROM Categories c
                    WHERE (c.Name LIKE @search OR c.Description LIKE @search)
                    AND c.IsActive = 1
                    ORDER BY c.Name";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    categories.Add(new Category
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString()),
                        ProductCount = Convert.ToInt32(reader["ProductCount"])
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث: {ex.Message}");
            }
            
            return categories;
        }
        
        public static async Task<List<Product>> GetProductsByCategoryAsync(int categoryId)
        {
            var products = new List<Product>();
            
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT p.*, c.Name as CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.CategoryId = @categoryId AND p.IsActive = 1
                    ORDER BY p.Name";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@categoryId", categoryId);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    products.Add(new Product
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Barcode = reader["Barcode"].ToString(),
                        CategoryId = reader["CategoryId"] == DBNull.Value ? null : Convert.ToInt32(reader["CategoryId"]),
                        CategoryName = reader["CategoryName"].ToString(),
                        PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                        SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                        Stock = Convert.ToInt32(reader["Stock"]),
                        MinStock = Convert.ToInt32(reader["MinStock"]),
                        Unit = reader["Unit"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString())
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب منتجات الفئة: {ex.Message}");
            }
            
            return products;
        }
    }
}
