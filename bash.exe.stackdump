Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDDC430000 ntdll.dll
7FFDDAF70000 KERNEL32.DLL
7FFDD9730000 KERNELBASE.dll
7FFDDB0E0000 USER32.dll
7FFDD9520000 win32u.dll
7FFDDB0A0000 GDI32.dll
7FFDD9B40000 gdi32full.dll
7FFDD9690000 msvcp_win.dll
7FFDD9FA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDDBD90000 advapi32.dll
7FFDDAB40000 msvcrt.dll
7FFDDB380000 sechost.dll
7FFDD9B10000 bcrypt.dll
7FFDDAC00000 RPCRT4.dll
7FFDD8CB0000 CRYPTBASE.DLL
7FFDD9550000 bcryptPrimitives.dll
7FFDDB340000 IMM32.DLL
