using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using POSManager.Models;
using POSManager.Services;

namespace POSManager
{
    public static class ProductTest
    {
        public static async Task TestAddProduct()
        {
            var output = new List<string>();
            output.Add("🧪 اختبار إضافة منتج...");
            Console.WriteLine("🧪 اختبار إضافة منتج...");

            try
            {
                // إنشاء منتج تجريبي
                var testProduct = new Product
                {
                    Name = "منتج تجريبي",
                    Barcode = "TEST123456789",
                    CategoryId = 1,
                    PurchasePrice = 5.0m,
                    SalePrice = 8.0m,
                    Stock = 10,
                    MinStock = 2,
                    Unit = "قطعة",
                    Description = "منتج للاختبار",
                    IsActive = true
                };
                
                Console.WriteLine($"📦 محاولة إضافة منتج: {testProduct.Name}");
                
                // محاولة إضافة المنتج
                var result = await ProductService.AddProductAsync(testProduct);
                
                if (result)
                {
                    Console.WriteLine("✅ تم إضافة المنتج بنجاح");
                    
                    // التحقق من وجود المنتج
                    var products = await ProductService.GetAllProductsAsync();
                    Console.WriteLine($"📊 إجمالي المنتجات في قاعدة البيانات: {products.Count}");
                    
                    // البحث عن المنتج المضاف
                    var addedProduct = products.FirstOrDefault(p => p.Barcode == testProduct.Barcode);
                    if (addedProduct != null)
                    {
                        Console.WriteLine($"🔍 تم العثور على المنتج: ID={addedProduct.Id}, Name={addedProduct.Name}");
                    }
                    else
                    {
                        Console.WriteLine("❌ لم يتم العثور على المنتج المضاف");
                    }
                }
                else
                {
                    Console.WriteLine("❌ فشل في إضافة المنتج");
                }
                // كتابة النتائج في ملف
                await File.WriteAllLinesAsync("product_test_results.txt", output);
                Console.WriteLine("\n✅ تم حفظ نتائج الاختبار في ملف product_test_results.txt");
            }
            catch (Exception ex)
            {
                var errorMsg = $"❌ خطأ في اختبار إضافة المنتج: {ex.Message}";
                var stackMsg = $"📋 تفاصيل الخطأ: {ex.StackTrace}";
                output.Add(errorMsg);
                output.Add(stackMsg);
                Console.WriteLine(errorMsg);
                Console.WriteLine(stackMsg);
                await File.WriteAllLinesAsync("product_test_results.txt", output);
            }
        }
    }
}
