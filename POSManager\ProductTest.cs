using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using POSManager.Models;
using POSManager.Services;
using POSManager.Data;

namespace POSManager
{
    public static class ProductTest
    {
        public static async Task TestAddProduct()
        {
            var output = new List<string>();
            output.Add("🧪 اختبار إضافة منتج...");
            Console.WriteLine("🧪 اختبار إضافة منتج...");

            try
            {
                // إنشاء قاعدة البيانات أولاً
                var msg0 = "🔧 إنشاء قاعدة البيانات...";
                output.Add(msg0);
                Console.WriteLine(msg0);

                DatabaseManager.InitializeDatabase();

                var msg0_1 = "✅ تم إنشاء قاعدة البيانات بنجاح";
                output.Add(msg0_1);
                Console.WriteLine(msg0_1);

                // إضافة فئة تجريبية أولاً
                var msg0_2 = "📁 إضافة فئة تجريبية...";
                output.Add(msg0_2);
                Console.WriteLine(msg0_2);

                var msg0_2_1 = "🔍 إنشاء كائن الفئة...";
                output.Add(msg0_2_1);
                Console.WriteLine(msg0_2_1);

                var testCategory = new Category
                {
                    Name = $"فئة تجريبية {DateTime.Now:yyyyMMdd_HHmmss}",
                    Description = "فئة للاختبار",
                    IsActive = true
                };

                var msg0_2_2 = "📞 استدعاء CategoryService.AddCategoryAsync...";
                output.Add(msg0_2_2);
                Console.WriteLine(msg0_2_2);

                var categoryResult = await CategoryService.AddCategoryAsync(testCategory);

                var msg0_2_3 = $"📊 نتيجة إضافة الفئة: {categoryResult}";
                output.Add(msg0_2_3);
                Console.WriteLine(msg0_2_3);
                if (categoryResult)
                {
                    var msg0_3 = "✅ تم إضافة الفئة بنجاح";
                    output.Add(msg0_3);
                    Console.WriteLine(msg0_3);
                }
                else
                {
                    var msg0_4 = "❌ فشل في إضافة الفئة";
                    output.Add(msg0_4);
                    Console.WriteLine(msg0_4);
                    return;
                }

                // إنشاء منتج تجريبي
                var testProduct = new Product
                {
                    Name = $"منتج تجريبي {DateTime.Now:yyyyMMdd_HHmmss}",
                    Barcode = $"TEST{DateTime.Now:yyyyMMddHHmmss}",
                    CategoryId = 1,
                    PurchasePrice = 5.0m,
                    SalePrice = 8.0m,
                    Stock = 10,
                    MinStock = 2,
                    Unit = "قطعة",
                    Description = "منتج للاختبار",
                    IsActive = true
                };
                
                var msg1 = $"📦 محاولة إضافة منتج: {testProduct.Name}";
                output.Add(msg1);
                Console.WriteLine(msg1);

                // محاولة إضافة المنتج
                var result = await ProductService.AddProductAsync(testProduct);

                if (result)
                {
                    var msg2 = "✅ تم إضافة المنتج بنجاح";
                    output.Add(msg2);
                    Console.WriteLine(msg2);

                    // التحقق من وجود المنتج
                    var products = await ProductService.GetAllProductsAsync();
                    var msg3 = $"📊 إجمالي المنتجات في قاعدة البيانات: {products.Count}";
                    output.Add(msg3);
                    Console.WriteLine(msg3);

                    // البحث عن المنتج المضاف
                    var addedProduct = products.FirstOrDefault(p => p.Barcode == testProduct.Barcode);
                    if (addedProduct != null)
                    {
                        var msg4 = $"🔍 تم العثور على المنتج: ID={addedProduct.Id}, Name={addedProduct.Name}";
                        output.Add(msg4);
                        Console.WriteLine(msg4);
                    }
                    else
                    {
                        var msg5 = "❌ لم يتم العثور على المنتج المضاف";
                        output.Add(msg5);
                        Console.WriteLine(msg5);
                    }
                }
                else
                {
                    var msg6 = "❌ فشل في إضافة المنتج";
                    output.Add(msg6);
                    Console.WriteLine(msg6);
                }
                // كتابة النتائج في ملف
                await File.WriteAllLinesAsync("product_test_results.txt", output);
                Console.WriteLine("\n✅ تم حفظ نتائج الاختبار في ملف product_test_results.txt");
            }
            catch (Exception ex)
            {
                var errorMsg = $"❌ خطأ في اختبار إضافة المنتج: {ex.Message}";
                var stackMsg = $"📋 تفاصيل الخطأ: {ex.StackTrace}";
                output.Add(errorMsg);
                output.Add(stackMsg);
                Console.WriteLine(errorMsg);
                Console.WriteLine(stackMsg);
                await File.WriteAllLinesAsync("product_test_results.txt", output);
            }
        }
    }
}
