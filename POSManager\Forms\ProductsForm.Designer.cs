namespace POSManager.Forms
{
    partial class ProductsForm
    {
        private System.ComponentModel.IContainer components = null;
        private DataGridView dgvProducts;
        private TextBox txtName;
        private TextBox txtBarcode;
        private TextBox txtPurchasePrice;
        private TextBox txtSalePrice;
        private TextBox txtStock;
        private TextBox txtMinStock;
        private TextBox txtUnit;
        private TextBox txtDescription;
        private ComboBox cmbCategory;
        private CheckBox chkIsActive;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnManageCategories;
        private TextBox txtSearch;
        private ComboBox cmbFilterCategory;
        private Label lblTotalProducts;
        private Label lblLowStockProducts;
        private Label lblOutOfStockProducts;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Form
            this.Text = "إدارة المنتجات";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Main Panel
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(10)
            };
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            
            // Left Panel (Products List)
            var leftPanel = new Panel { Dock = DockStyle.Fill };
            
            // Search Panel
            var searchPanel = new Panel { Height = 80, Dock = DockStyle.Top };
            
            var lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 15),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            txtSearch = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 23),
                RightToLeft = RightToLeft.Yes
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            
            var lblFilterCategory = new Label
            {
                Text = "الفئة:",
                Location = new Point(290, 15),
                Size = new Size(40, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            cmbFilterCategory = new ComboBox
            {
                Location = new Point(340, 12),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            cmbFilterCategory.SelectedIndexChanged += CmbFilterCategory_SelectedIndexChanged;
            
            btnRefresh = new Button
            {
                Text = "تحديث",
                Location = new Point(510, 10),
                Size = new Size(80, 27),
                UseVisualStyleBackColor = true
            };
            btnRefresh.Click += BtnRefresh_Click;
            
            searchPanel.Controls.AddRange(new Control[] { lblSearch, txtSearch, lblFilterCategory, cmbFilterCategory, btnRefresh });
            
            // Statistics Panel
            var statsPanel = new Panel { Height = 40, Dock = DockStyle.Top };
            
            lblTotalProducts = new Label
            {
                Text = "إجمالي المنتجات: 0",
                Location = new Point(10, 10),
                Size = new Size(150, 20),
                ForeColor = Color.Blue
            };
            
            lblLowStockProducts = new Label
            {
                Text = "منتجات قليلة المخزون: 0",
                Location = new Point(170, 10),
                Size = new Size(150, 20),
                ForeColor = Color.Orange
            };
            
            lblOutOfStockProducts = new Label
            {
                Text = "منتجات نفدت من المخزون: 0",
                Location = new Point(330, 10),
                Size = new Size(150, 20),
                ForeColor = Color.Red
            };
            
            statsPanel.Controls.AddRange(new Control[] { lblTotalProducts, lblLowStockProducts, lblOutOfStockProducts });
            
            // DataGridView
            dgvProducts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None,
                RightToLeft = RightToLeft.Yes
            };
            
            leftPanel.Controls.Add(dgvProducts);
            leftPanel.Controls.Add(statsPanel);
            leftPanel.Controls.Add(searchPanel);
            
            // Right Panel (Product Form)
            var rightPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            
            var formTitle = new Label
            {
                Text = "بيانات المنتج",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Form Fields
            int yPos = 50;
            int fieldHeight = 30;
            int labelWidth = 100;
            int controlWidth = 200;
            
            var lblName = new Label { Text = "اسم المنتج:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtName = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblBarcode = new Label { Text = "الباركود:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtBarcode = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblCategory = new Label { Text = "الفئة:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            cmbCategory = new ComboBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), DropDownStyle = ComboBoxStyle.DropDownList, RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblPurchasePrice = new Label { Text = "سعر الشراء:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtPurchasePrice = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblSalePrice = new Label { Text = "سعر البيع:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtSalePrice = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblStock = new Label { Text = "المخزون:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtStock = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblMinStock = new Label { Text = "الحد الأدنى:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtMinStock = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblUnit = new Label { Text = "الوحدة:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtUnit = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            var lblDescription = new Label { Text = "الوصف:", Location = new Point(10, yPos), Size = new Size(labelWidth, 23), TextAlign = ContentAlignment.MiddleRight };
            txtDescription = new TextBox { Location = new Point(120, yPos), Size = new Size(controlWidth, 60), Multiline = true, RightToLeft = RightToLeft.Yes };
            yPos += 70;
            
            chkIsActive = new CheckBox { Text = "نشط", Location = new Point(120, yPos), Size = new Size(100, 23), RightToLeft = RightToLeft.Yes };
            yPos += fieldHeight;
            
            // Buttons Panel
            var buttonsPanel = new Panel { Location = new Point(10, yPos + 20), Size = new Size(320, 100) };
            
            btnAdd = new Button { Text = "جديد", Location = new Point(10, 10), Size = new Size(70, 30), UseVisualStyleBackColor = true };
            btnAdd.Click += BtnAdd_Click;
            
            btnEdit = new Button { Text = "تعديل", Location = new Point(90, 10), Size = new Size(70, 30), UseVisualStyleBackColor = true, Enabled = false };
            btnEdit.Click += BtnEdit_Click;
            
            btnSave = new Button { Text = "حفظ", Location = new Point(170, 10), Size = new Size(70, 30), UseVisualStyleBackColor = true };
            btnSave.Click += BtnSave_Click;
            
            btnCancel = new Button { Text = "إلغاء", Location = new Point(250, 10), Size = new Size(70, 30), UseVisualStyleBackColor = true };
            btnCancel.Click += BtnCancel_Click;
            
            btnDelete = new Button { Text = "حذف", Location = new Point(90, 50), Size = new Size(70, 30), UseVisualStyleBackColor = true, Enabled = false, BackColor = Color.LightCoral };
            btnDelete.Click += BtnDelete_Click;
            
            btnManageCategories = new Button { Text = "إدارة الفئات", Location = new Point(170, 50), Size = new Size(100, 30), UseVisualStyleBackColor = true };
            btnManageCategories.Click += BtnManageCategories_Click;
            
            buttonsPanel.Controls.AddRange(new Control[] { btnAdd, btnEdit, btnSave, btnCancel, btnDelete, btnManageCategories });
            
            rightPanel.Controls.AddRange(new Control[] {
                formTitle, lblName, txtName, lblBarcode, txtBarcode, lblCategory, cmbCategory,
                lblPurchasePrice, txtPurchasePrice, lblSalePrice, txtSalePrice,
                lblStock, txtStock, lblMinStock, txtMinStock, lblUnit, txtUnit,
                lblDescription, txtDescription, chkIsActive, buttonsPanel
            });
            
            mainPanel.Controls.Add(leftPanel, 0, 0);
            mainPanel.Controls.Add(rightPanel, 1, 0);
            
            this.Controls.Add(mainPanel);
        }
    }
}
