namespace POSManager.Models
{
    public class Sale
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public int? CustomerId { get; set; }
        public int UserId { get; set; }
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal ChangeAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;
        public string Notes { get; set; } = string.Empty;
        public DateTime SaleDate { get; set; }
        
        // Navigation properties
        public Customer? Customer { get; set; }
        public User? User { get; set; }
        public List<SaleDetail> SaleDetails { get; set; } = new List<SaleDetail>();
        
        // Calculated properties
        public int TotalItems => SaleDetails.Sum(d => d.Quantity);
        public decimal TotalProfit => SaleDetails.Sum(d => d.TotalProfit);
    }
    
    public class SaleDetail
    {
        public int Id { get; set; }
        public int SaleId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        
        // Navigation properties
        public Sale? Sale { get; set; }
        public Product? Product { get; set; }
        
        // Calculated properties
        public decimal TotalProfit => Product != null ? (UnitPrice - Product.PurchasePrice) * Quantity : 0;
    }
    
    public class Expense
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Category { get; set; } = string.Empty;
        public int UserId { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string Notes { get; set; } = string.Empty;
        
        // Navigation properties
        public User? User { get; set; }
    }
    
    public class Setting
    {
        public int Id { get; set; }
        public string Key { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime UpdatedAt { get; set; }
    }
    
    public class InventoryLog
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public InventoryChangeType ChangeType { get; set; }
        public int OldQuantity { get; set; }
        public int NewQuantity { get; set; }
        public int QuantityChanged { get; set; }
        public int UserId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        
        // Navigation properties
        public Product? Product { get; set; }
        public User? User { get; set; }
    }
    
    public class ActivityLog
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string TableName { get; set; } = string.Empty;
        public int? RecordId { get; set; }
        public string Details { get; set; } = string.Empty;
        public string IPAddress { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        
        // Navigation properties
        public User? User { get; set; }
    }
    
    public enum PaymentMethod
    {
        Cash,
        Card,
        Transfer,
        Mixed
    }
    
    public enum InventoryChangeType
    {
        Sale,
        Purchase,
        Adjustment,
        Return
    }
}
