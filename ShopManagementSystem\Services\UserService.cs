using Microsoft.Data.Sqlite;
using ShopManagementSystem.Data;
using ShopManagementSystem.Models;

namespace ShopManagementSystem.Services
{
    public class UserService
    {
        public List<User> GetAllUsers()
        {
            var users = new List<User>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Username, Password, FullName, Role, CreatedAt, LastLogin, IsActive
                    FROM Users
                    WHERE IsActive = 1
                    ORDER BY FullName";

                using var command = connection.CreateCommand();
                command.CommandText = query;

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    users.Add(new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.GetString(1),
                        Password = reader.GetString(2),
                        FullName = reader.GetString(3),
                        Role = (UserRole)reader.GetInt32(4),
                        CreatedAt = reader.GetDateTime(5),
                        LastLogin = reader.IsDBNull(6) ? null : reader.GetDateTime(6),
                        IsActive = reader.GetBoolean(7)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدمين: {ex.Message}");
            }

            return users;
        }

        public User? AuthenticateUser(string username, string password)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Username, Password, FullName, Role, CreatedAt, LastLogin, IsActive
                    FROM Users
                    WHERE Username = @username AND Password = @password AND IsActive = 1";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@username", username);
                command.Parameters.AddWithValue("@password", password);

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    var user = new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.GetString(1),
                        Password = reader.GetString(2),
                        FullName = reader.GetString(3),
                        Role = (UserRole)reader.GetInt32(4),
                        CreatedAt = reader.GetDateTime(5),
                        LastLogin = reader.IsDBNull(6) ? null : reader.GetDateTime(6),
                        IsActive = reader.GetBoolean(7)
                    };

                    // تحديث آخر تسجيل دخول
                    UpdateLastLogin(user.Id);

                    return user;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في المصادقة: {ex.Message}");
            }

            return null;
        }

        public User? GetUserById(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Username, Password, FullName, Role, CreatedAt, LastLogin, IsActive
                    FROM Users
                    WHERE Id = @id AND IsActive = 1";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", id);

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.GetString(1),
                        Password = reader.GetString(2),
                        FullName = reader.GetString(3),
                        Role = (UserRole)reader.GetInt32(4),
                        CreatedAt = reader.GetDateTime(5),
                        LastLogin = reader.IsDBNull(6) ? null : reader.GetDateTime(6),
                        IsActive = reader.GetBoolean(7)
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المستخدم: {ex.Message}");
            }

            return null;
        }

        public bool AddUser(User user)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    INSERT INTO Users (Username, Password, FullName, Role)
                    VALUES (@username, @password, @fullName, @role)";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@username", user.Username);
                command.Parameters.AddWithValue("@password", user.Password);
                command.Parameters.AddWithValue("@fullName", user.FullName);
                command.Parameters.AddWithValue("@role", (int)user.Role);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة المستخدم: {ex.Message}");
            }
        }

        public bool UpdateUser(User user)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    UPDATE Users
                    SET Username = @username, Password = @password, FullName = @fullName, Role = @role
                    WHERE Id = @id";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", user.Id);
                command.Parameters.AddWithValue("@username", user.Username);
                command.Parameters.AddWithValue("@password", user.Password);
                command.Parameters.AddWithValue("@fullName", user.FullName);
                command.Parameters.AddWithValue("@role", (int)user.Role);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }

        public bool DeleteUser(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                // حذف منطقي - تغيير IsActive إلى false
                var query = "UPDATE Users SET IsActive = 0 WHERE Id = @id";
                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", id);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المستخدم: {ex.Message}");
            }
        }

        private void UpdateLastLogin(int userId)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = "UPDATE Users SET LastLogin = @lastLogin WHERE Id = @id";
                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", userId);
                command.Parameters.AddWithValue("@lastLogin", DateTime.Now);

                command.ExecuteNonQuery();
            }
            catch
            {
                // تجاهل الأخطاء في تحديث آخر تسجيل دخول
            }
        }

        public List<User> SearchUsers(string searchTerm)
        {
            var users = new List<User>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Username, Password, FullName, Role, CreatedAt, LastLogin, IsActive
                    FROM Users
                    WHERE IsActive = 1
                    AND (Username LIKE @searchTerm OR FullName LIKE @searchTerm)
                    ORDER BY FullName";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@searchTerm", $"%{searchTerm}%");

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    users.Add(new User
                    {
                        Id = reader.GetInt32(0),
                        Username = reader.GetString(1),
                        Password = reader.GetString(2),
                        FullName = reader.GetString(3),
                        Role = (UserRole)reader.GetInt32(4),
                        CreatedAt = reader.GetDateTime(5),
                        LastLogin = reader.IsDBNull(6) ? null : reader.GetDateTime(6),
                        IsActive = reader.GetBoolean(7)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن المستخدمين: {ex.Message}");
            }

            return users;
        }
    }
}