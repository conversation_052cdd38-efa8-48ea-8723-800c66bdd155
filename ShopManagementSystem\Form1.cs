using ShopManagementSystem.Data;
using ShopManagementSystem.Forms;

namespace ShopManagementSystem;

public partial class Form1 : Form
{
    public Form1()
    {
        InitializeComponent();
        InitializeApplication();
    }

    private void InitializeApplication()
    {
        try
        {
            // إنشاء قاعدة البيانات
            DatabaseManager.InitializeDatabase();

            // إخفاء النموذج الحالي
            this.WindowState = FormWindowState.Minimized;
            this.ShowInTaskbar = false;

            // فتح شاشة تسجيل الدخول
            var loginForm = new LoginForm();
            if (loginForm.ShowDialog() == DialogResult.OK && loginForm.CurrentUser != null)
            {
                // فتح الشاشة الرئيسية
                var mainForm = new MainForm(loginForm.CurrentUser);
                this.Hide();
                mainForm.ShowDialog();
            }

            // إغلاق التطبيق
            Application.Exit();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            Application.Exit();
        }
    }
}
