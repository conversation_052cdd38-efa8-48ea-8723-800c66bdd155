using ShopManagementSystem.Models;
using ShopManagementSystem.Services;

namespace ShopManagementSystem.Forms
{
    public partial class ProductsForm : Form
    {
        private readonly User _currentUser;
        private readonly ProductService _productService;
        private readonly CategoryService _categoryService;
        private List<Product> _products;
        private List<Category> _categories;

        public ProductsForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            _productService = new ProductService();
            _categoryService = new CategoryService();
            _products = new List<Product>();
            _categories = new List<Category>();
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            LoadCategories();
            LoadProducts();
            SetupDataGridView();
            SetupPermissions();
        }

        private void SetupPermissions()
        {
            // إذا كان المستخدم كاشير، منع التعديل والحذف
            if (_currentUser.Role == UserRole.Cashier)
            {
                btnAdd.Enabled = false;
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
            }
        }

        private void LoadCategories()
        {
            try
            {
                _categories = _categoryService.GetAllCategories();
                
                cmbCategory.Items.Clear();
                cmbCategory.Items.Add(new { Id = 0, Name = "جميع التصنيفات" });
                
                foreach (var category in _categories)
                {
                    cmbCategory.Items.Add(new { Id = category.Id, Name = category.Name });
                }
                
                cmbCategory.DisplayMember = "Name";
                cmbCategory.ValueMember = "Id";
                cmbCategory.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التصنيفات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProducts()
        {
            try
            {
                _products = _productService.GetAllProducts();
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupDataGridView()
        {
            dgvProducts.AutoGenerateColumns = false;
            dgvProducts.AllowUserToAddRows = false;
            dgvProducts.AllowUserToDeleteRows = false;
            dgvProducts.ReadOnly = true;
            dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvProducts.MultiSelect = false;

            dgvProducts.Columns.Clear();
            
            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم",
                DataPropertyName = "Id",
                Width = 60,
                Visible = false
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم المنتج",
                DataPropertyName = "Name",
                Width = 200
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Code",
                HeaderText = "الكود",
                DataPropertyName = "Code",
                Width = 100
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Barcode",
                HeaderText = "الباركود",
                DataPropertyName = "Barcode",
                Width = 120
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Price",
                HeaderText = "السعر",
                DataPropertyName = "Price",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                Width = 80
            });

            dgvProducts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CategoryName",
                HeaderText = "التصنيف",
                Width = 120
            });
        }

        private void RefreshDataGridView()
        {
            var filteredProducts = _products.AsEnumerable();

            // تطبيق فلتر التصنيف
            if (cmbCategory.SelectedValue != null && Convert.ToInt32(cmbCategory.SelectedValue) > 0)
            {
                var categoryId = Convert.ToInt32(cmbCategory.SelectedValue);
                filteredProducts = filteredProducts.Where(p => p.CategoryId == categoryId);
            }

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                var searchTerm = txtSearch.Text.ToLower();
                filteredProducts = filteredProducts.Where(p => 
                    p.Name.ToLower().Contains(searchTerm) ||
                    p.Code.ToLower().Contains(searchTerm) ||
                    p.Barcode.ToLower().Contains(searchTerm) ||
                    (p.Category?.Name.ToLower().Contains(searchTerm) ?? false));
            }

            dgvProducts.DataSource = filteredProducts.ToList();

            // تحديث أسماء التصنيفات
            foreach (DataGridViewRow row in dgvProducts.Rows)
            {
                var product = (Product)row.DataBoundItem;
                row.Cells["CategoryName"].Value = product.Category?.Name ?? "غير محدد";
            }

            lblCount.Text = $"عدد المنتجات: {filteredProducts.Count()}";
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            var addForm = new ProductAddEditForm(_categories);
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    if (_productService.AddProduct(addForm.Product))
                    {
                        MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadProducts();
                    }
                    else
                    {
                        MessageBox.Show("فشل في إضافة المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedProduct = (Product)dgvProducts.SelectedRows[0].DataBoundItem;
            var editForm = new ProductAddEditForm(_categories, selectedProduct);
            
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    if (_productService.UpdateProduct(editForm.Product))
                    {
                        MessageBox.Show("تم تحديث المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadProducts();
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث المنتج: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvProducts.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedProduct = (Product)dgvProducts.SelectedRows[0].DataBoundItem;
            var result = MessageBox.Show($"هل تريد حذف المنتج '{selectedProduct.Name}'؟", "تأكيد الحذف", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    if (_productService.DeleteProduct(selectedProduct.Id))
                    {
                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadProducts();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المنتج", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadProducts();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void cmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            RefreshDataGridView();
        }

        private void dgvProducts_DoubleClick(object sender, EventArgs e)
        {
            if (_currentUser.Role == UserRole.Admin)
            {
                btnEdit_Click(sender, e);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
