# نظام إدارة نقطة البيع (POSManager)

## نظرة عامة
نظام شامل لإدارة نقطة البيع مصمم للمتاجر والشركات الصغيرة والمتوسطة. تم تطويره باستخدام C# و Windows Forms مع قاعدة بيانات SQLite.

## الميزات المكتملة

### 1. نظام المصادقة والأمان
- **تسجيل الدخول الآمن**: نظام مصادقة متقدم مع تشفير كلمات المرور باستخدام SHA256
- **إدارة الجلسات**: نظام إدارة جلسات مع انتهاء صلاحية تلقائي
- **الصلاحيات**: نظام صلاحيات متدرج (مدير/كاشير)
- **تسجيل الأنشطة**: تسجيل جميع العمليات والأنشطة في النظام

### 2. قاعدة البيانات
- **SQLite**: قاعدة بيانات محلية سريعة وموثوقة
- **11 جدول**: تصميم شامل لجميع البيانات المطلوبة
- **الفهرسة**: فهارس محسنة لتحسين الأداء
- **النسخ الاحتياطي**: إمكانية عمل نسخ احتياطية

### 3. واجهة المستخدم
- **دعم العربية**: واجهة مصممة للغة العربية مع دعم RTL
- **تصميم حديث**: واجهة عصرية وسهلة الاستخدام
- **شاشة تسجيل الدخول**: شاشة أنيقة لتسجيل الدخول
- **لوحة التحكم الرئيسية**: لوحة تحكم شاملة مع جميع الوظائف

## البنية التقنية

### المجلدات والملفات
```
POSManager/
├── Data/
│   └── DatabaseManager.cs      # إدارة قاعدة البيانات
├── Models/
│   ├── User.cs                 # نماذج المستخدمين
│   ├── Product.cs              # نماذج المنتجات والعملاء
│   └── Sale.cs                 # نماذج المبيعات والمصروفات
├── Services/
│   └── AuthService.cs          # خدمات المصادقة
├── Forms/
│   ├── LoginForm.cs            # شاشة تسجيل الدخول
│   ├── LoginForm.Designer.cs
│   ├── MainForm.cs             # الشاشة الرئيسية
│   └── MainForm.Designer.cs
└── Program.cs                  # نقطة البداية
```

### قاعدة البيانات
- **Users**: المستخدمون والصلاحيات
- **Categories**: فئات المنتجات
- **Products**: المنتجات والمخزون
- **Customers**: العملاء
- **Suppliers**: الموردون
- **Sales**: المبيعات
- **SaleDetails**: تفاصيل المبيعات
- **Expenses**: المصروفات
- **Settings**: إعدادات النظام
- **InventoryLog**: سجل حركة المخزون
- **ActivityLog**: سجل الأنشطة

## كيفية التشغيل

### المتطلبات
- .NET 7.0 أو أحدث
- Windows 10/11
- Microsoft.Data.Sqlite (مثبت تلقائياً)

### التشغيل
```bash
cd POSManager
dotnet run
```

### بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## الوظائف المتاحة

### للمدير (Admin)
- ✅ إدارة المستخدمين
- ✅ إدارة المنتجات
- ✅ معالجة المبيعات
- ✅ عرض التقارير
- ✅ إدارة الإعدادات
- ✅ إدارة المخزون
- ✅ حذف المبيعات
- ✅ منح خصومات

### للكاشير (Cashier)
- ✅ معالجة المبيعات
- ✅ عرض التقارير المحدودة
- ❌ إدارة المستخدمين
- ❌ إدارة الإعدادات
- ❌ حذف المبيعات

## الأمان والحماية

### تشفير كلمات المرور
- استخدام SHA256 لتشفير كلمات المرور
- Salt عشوائي لكل كلمة مرور
- عدم تخزين كلمات المرور بشكل واضح

### إدارة الجلسات
- انتهاء صلاحية تلقائي (افتراضي: 30 دقيقة)
- تتبع النشاط لتجديد الجلسة
- تسجيل خروج آمن

### تسجيل الأنشطة
- تسجيل جميع عمليات تسجيل الدخول/الخروج
- تسجيل جميع العمليات الحساسة
- تتبع التغييرات في البيانات

## الحالة الحالية

### ✅ مكتمل
- نظام المصادقة والأمان
- قاعدة البيانات الأساسية
- شاشة تسجيل الدخول
- الشاشة الرئيسية
- نظام الصلاحيات

### 🔄 قيد التطوير
- شاشة نقطة البيع (POS)
- إدارة المنتجات
- إدارة المستخدمين
- التقارير
- إدارة المخزون
- الإعدادات
- نظام الطباعة

## ملاحظات التطوير

### التقنيات المستخدمة
- **C# .NET 7.0**: اللغة الأساسية
- **Windows Forms**: واجهة المستخدم
- **SQLite**: قاعدة البيانات
- **Microsoft.Data.Sqlite**: مكتبة الاتصال بقاعدة البيانات

### أفضل الممارسات
- فصل الطبقات (UI - Logic - Data)
- استخدام Async/Await للعمليات غير المتزامنة
- معالجة الأخطاء الشاملة
- تسجيل الأنشطة والأخطاء
- التحقق من الصلاحيات في كل عملية

## المساهمة
هذا المشروع في مرحلة التطوير النشط. يرجى مراجعة كل جزء قبل المتابعة للجزء التالي.

## الترخيص
جميع الحقوق محفوظة © 2025
