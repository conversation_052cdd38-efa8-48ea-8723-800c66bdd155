namespace POSManager.Forms
{
    partial class POSForm
    {
        private System.ComponentModel.IContainer components = null;
        private TableLayoutPanel mainLayout;
        private Panel leftPanel;
        private Panel rightPanel;
        private GroupBox grpProducts;
        private GroupBox grpCart;
        private GroupBox grpCustomer;
        private GroupBox grpPayment;
        private TextBox txtProductSearch;
        private DataGridView dgvProducts;
        private DataGridView dgvCart;
        private Label lblSubtotal;
        private Label lblDiscount;
        private Label lblTax;
        private Label lblTotal;
        private Label lblItemCount;
        private Label lblTotalQuantity;
        private Label lblCustomer;
        private Button btnSelectCustomer;
        private Button btnClearCustomer;
        private TextBox txtDiscountPercentage;
        private TextBox txtDiscountAmount;
        private Button btnProcessSale;
        private Button btnClearCart;
        private Label lblProductSearch;
        private Label lblDiscountPercentage;
        private Label lblDiscountAmount;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.mainLayout = new TableLayoutPanel();
            this.leftPanel = new Panel();
            this.rightPanel = new Panel();
            this.grpProducts = new GroupBox();
            this.grpCart = new GroupBox();
            this.grpCustomer = new GroupBox();
            this.grpPayment = new GroupBox();
            this.txtProductSearch = new TextBox();
            this.dgvProducts = new DataGridView();
            this.dgvCart = new DataGridView();
            this.lblSubtotal = new Label();
            this.lblDiscount = new Label();
            this.lblTax = new Label();
            this.lblTotal = new Label();
            this.lblItemCount = new Label();
            this.lblTotalQuantity = new Label();
            this.lblCustomer = new Label();
            this.btnSelectCustomer = new Button();
            this.btnClearCustomer = new Button();
            this.txtDiscountPercentage = new TextBox();
            this.txtDiscountAmount = new TextBox();
            this.btnProcessSale = new Button();
            this.btnClearCart = new Button();
            this.lblProductSearch = new Label();
            this.lblDiscountPercentage = new Label();
            this.lblDiscountAmount = new Label();

            this.mainLayout.SuspendLayout();
            this.leftPanel.SuspendLayout();
            this.rightPanel.SuspendLayout();
            this.grpProducts.SuspendLayout();
            this.grpCart.SuspendLayout();
            this.grpCustomer.SuspendLayout();
            this.grpPayment.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProducts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCart)).BeginInit();
            this.SuspendLayout();

            // 
            // POSForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 800);
            this.Controls.Add(this.mainLayout);
            this.Font = new Font("Segoe UI", 9F);
            this.MinimumSize = new Size(1200, 700);
            this.Name = "POSForm";
            this.RightToLeft = RightToLeft.Yes;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "نقطة البيع - POS";
            this.WindowState = FormWindowState.Maximized;

            // 
            // mainLayout
            // 
            this.mainLayout.ColumnCount = 2;
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60F));
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F));
            this.mainLayout.Controls.Add(this.leftPanel, 0, 0);
            this.mainLayout.Controls.Add(this.rightPanel, 1, 0);
            this.mainLayout.Dock = DockStyle.Fill;
            this.mainLayout.Location = new Point(0, 0);
            this.mainLayout.Margin = new Padding(4);
            this.mainLayout.Name = "mainLayout";
            this.mainLayout.RowCount = 1;
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.mainLayout.Size = new Size(1400, 800);
            this.mainLayout.TabIndex = 0;

            // 
            // leftPanel
            // 
            this.leftPanel.Controls.Add(this.grpProducts);
            this.leftPanel.Dock = DockStyle.Fill;
            this.leftPanel.Location = new Point(564, 4);
            this.leftPanel.Margin = new Padding(4);
            this.leftPanel.Name = "leftPanel";
            this.leftPanel.Size = new Size(832, 792);
            this.leftPanel.TabIndex = 0;

            // 
            // rightPanel
            // 
            this.rightPanel.Controls.Add(this.grpCart);
            this.rightPanel.Controls.Add(this.grpCustomer);
            this.rightPanel.Controls.Add(this.grpPayment);
            this.rightPanel.Dock = DockStyle.Fill;
            this.rightPanel.Location = new Point(4, 4);
            this.rightPanel.Margin = new Padding(4);
            this.rightPanel.Name = "rightPanel";
            this.rightPanel.Size = new Size(552, 792);
            this.rightPanel.TabIndex = 1;

            // 
            // grpProducts
            // 
            this.grpProducts.Controls.Add(this.lblProductSearch);
            this.grpProducts.Controls.Add(this.txtProductSearch);
            this.grpProducts.Controls.Add(this.dgvProducts);
            this.grpProducts.Dock = DockStyle.Fill;
            this.grpProducts.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpProducts.Location = new Point(0, 0);
            this.grpProducts.Margin = new Padding(4);
            this.grpProducts.Name = "grpProducts";
            this.grpProducts.Padding = new Padding(4);
            this.grpProducts.Size = new Size(832, 792);
            this.grpProducts.TabIndex = 0;
            this.grpProducts.TabStop = false;
            this.grpProducts.Text = "المنتجات";

            // 
            // lblProductSearch
            // 
            this.lblProductSearch.AutoSize = true;
            this.lblProductSearch.Font = new Font("Segoe UI", 9F);
            this.lblProductSearch.Location = new Point(750, 30);
            this.lblProductSearch.Margin = new Padding(4, 0, 4, 0);
            this.lblProductSearch.Name = "lblProductSearch";
            this.lblProductSearch.Size = new Size(70, 20);
            this.lblProductSearch.TabIndex = 0;
            this.lblProductSearch.Text = "البحث:";

            // 
            // txtProductSearch
            // 
            this.txtProductSearch.Anchor = ((AnchorStyles)(((AnchorStyles.Top | AnchorStyles.Left) | AnchorStyles.Right)));
            this.txtProductSearch.Font = new Font("Segoe UI", 12F);
            this.txtProductSearch.Location = new Point(8, 27);
            this.txtProductSearch.Margin = new Padding(4);
            this.txtProductSearch.Name = "txtProductSearch";
            this.txtProductSearch.PlaceholderText = "ابحث بالاسم أو الكود أو الباركود...";
            this.txtProductSearch.Size = new Size(734, 34);
            this.txtProductSearch.TabIndex = 1;

            // 
            // dgvProducts
            // 
            this.dgvProducts.AllowUserToAddRows = false;
            this.dgvProducts.AllowUserToDeleteRows = false;
            this.dgvProducts.Anchor = ((AnchorStyles)((((AnchorStyles.Top | AnchorStyles.Bottom) | AnchorStyles.Left) | AnchorStyles.Right)));
            this.dgvProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvProducts.BackgroundColor = SystemColors.Window;
            this.dgvProducts.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvProducts.Font = new Font("Segoe UI", 9F);
            this.dgvProducts.Location = new Point(8, 69);
            this.dgvProducts.Margin = new Padding(4);
            this.dgvProducts.MultiSelect = false;
            this.dgvProducts.Name = "dgvProducts";
            this.dgvProducts.ReadOnly = true;
            this.dgvProducts.RowHeadersVisible = false;
            this.dgvProducts.RowHeadersWidth = 51;
            this.dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvProducts.Size = new Size(816, 715);
            this.dgvProducts.TabIndex = 2;

            // 
            // grpCart
            // 
            this.grpCart.Controls.Add(this.dgvCart);
            this.grpCart.Controls.Add(this.lblItemCount);
            this.grpCart.Controls.Add(this.lblTotalQuantity);
            this.grpCart.Controls.Add(this.lblSubtotal);
            this.grpCart.Controls.Add(this.lblDiscount);
            this.grpCart.Controls.Add(this.lblTax);
            this.grpCart.Controls.Add(this.lblTotal);
            this.grpCart.Dock = DockStyle.Top;
            this.grpCart.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpCart.Location = new Point(0, 0);
            this.grpCart.Margin = new Padding(4);
            this.grpCart.Name = "grpCart";
            this.grpCart.Padding = new Padding(4);
            this.grpCart.Size = new Size(552, 400);
            this.grpCart.TabIndex = 0;
            this.grpCart.TabStop = false;
            this.grpCart.Text = "السلة";

            // 
            // dgvCart
            // 
            this.dgvCart.AllowUserToAddRows = false;
            this.dgvCart.AllowUserToDeleteRows = false;
            this.dgvCart.Anchor = ((AnchorStyles)((((AnchorStyles.Top | AnchorStyles.Bottom) | AnchorStyles.Left) | AnchorStyles.Right)));
            this.dgvCart.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvCart.BackgroundColor = SystemColors.Window;
            this.dgvCart.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCart.Font = new Font("Segoe UI", 9F);
            this.dgvCart.Location = new Point(8, 27);
            this.dgvCart.Margin = new Padding(4);
            this.dgvCart.MultiSelect = false;
            this.dgvCart.Name = "dgvCart";
            this.dgvCart.RowHeadersVisible = false;
            this.dgvCart.RowHeadersWidth = 51;
            this.dgvCart.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvCart.Size = new Size(536, 250);
            this.dgvCart.TabIndex = 0;

            // 
            // lblItemCount
            // 
            this.lblItemCount.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.lblItemCount.AutoSize = true;
            this.lblItemCount.Font = new Font("Segoe UI", 9F);
            this.lblItemCount.Location = new Point(440, 285);
            this.lblItemCount.Margin = new Padding(4, 0, 4, 0);
            this.lblItemCount.Name = "lblItemCount";
            this.lblItemCount.Size = new Size(104, 20);
            this.lblItemCount.TabIndex = 1;
            this.lblItemCount.Text = "عدد الأصناف: 0";

            // 
            // lblTotalQuantity
            // 
            this.lblTotalQuantity.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Left)));
            this.lblTotalQuantity.AutoSize = true;
            this.lblTotalQuantity.Font = new Font("Segoe UI", 9F);
            this.lblTotalQuantity.Location = new Point(8, 285);
            this.lblTotalQuantity.Margin = new Padding(4, 0, 4, 0);
            this.lblTotalQuantity.Name = "lblTotalQuantity";
            this.lblTotalQuantity.Size = new Size(118, 20);
            this.lblTotalQuantity.TabIndex = 2;
            this.lblTotalQuantity.Text = "إجمالي الكمية: 0";

            // 
            // lblSubtotal
            // 
            this.lblSubtotal.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.lblSubtotal.AutoSize = true;
            this.lblSubtotal.Font = new Font("Segoe UI", 9F);
            this.lblSubtotal.Location = new Point(400, 309);
            this.lblSubtotal.Margin = new Padding(4, 0, 4, 0);
            this.lblSubtotal.Name = "lblSubtotal";
            this.lblSubtotal.Size = new Size(144, 20);
            this.lblSubtotal.TabIndex = 3;
            this.lblSubtotal.Text = "المجموع الفرعي: 0.00";

            // 
            // lblDiscount
            // 
            this.lblDiscount.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.lblDiscount.AutoSize = true;
            this.lblDiscount.Font = new Font("Segoe UI", 9F);
            this.lblDiscount.Location = new Point(460, 333);
            this.lblDiscount.Margin = new Padding(4, 0, 4, 0);
            this.lblDiscount.Name = "lblDiscount";
            this.lblDiscount.Size = new Size(84, 20);
            this.lblDiscount.TabIndex = 4;
            this.lblDiscount.Text = "الخصم: 0.00";

            // 
            // lblTax
            // 
            this.lblTax.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.lblTax.AutoSize = true;
            this.lblTax.Font = new Font("Segoe UI", 9F);
            this.lblTax.Location = new Point(420, 357);
            this.lblTax.Margin = new Padding(4, 0, 4, 0);
            this.lblTax.Name = "lblTax";
            this.lblTax.Size = new Size(124, 20);
            this.lblTax.TabIndex = 5;
            this.lblTax.Text = "الضريبة (15%): 0.00";

            // 
            // lblTotal
            // 
            this.lblTotal.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.lblTotal.AutoSize = true;
            this.lblTotal.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            this.lblTotal.ForeColor = Color.DarkGreen;
            this.lblTotal.Location = new Point(440, 381);
            this.lblTotal.Margin = new Padding(4, 0, 4, 0);
            this.lblTotal.Name = "lblTotal";
            this.lblTotal.Size = new Size(104, 25);
            this.lblTotal.TabIndex = 6;
            this.lblTotal.Text = "الإجمالي: 0.00";

            //
            // grpCustomer
            //
            this.grpCustomer.Controls.Add(this.lblCustomer);
            this.grpCustomer.Controls.Add(this.btnSelectCustomer);
            this.grpCustomer.Controls.Add(this.btnClearCustomer);
            this.grpCustomer.Dock = DockStyle.Top;
            this.grpCustomer.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpCustomer.Location = new Point(0, 400);
            this.grpCustomer.Margin = new Padding(4);
            this.grpCustomer.Name = "grpCustomer";
            this.grpCustomer.Padding = new Padding(4);
            this.grpCustomer.Size = new Size(552, 80);
            this.grpCustomer.TabIndex = 1;
            this.grpCustomer.TabStop = false;
            this.grpCustomer.Text = "العميل";

            //
            // lblCustomer
            //
            this.lblCustomer.AutoSize = true;
            this.lblCustomer.Font = new Font("Segoe UI", 9F);
            this.lblCustomer.Location = new Point(8, 27);
            this.lblCustomer.Margin = new Padding(4, 0, 4, 0);
            this.lblCustomer.Name = "lblCustomer";
            this.lblCustomer.Size = new Size(98, 20);
            this.lblCustomer.TabIndex = 0;
            this.lblCustomer.Text = "العميل: غير محدد";

            //
            // btnSelectCustomer
            //
            this.btnSelectCustomer.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnSelectCustomer.Font = new Font("Segoe UI", 9F);
            this.btnSelectCustomer.Location = new Point(440, 50);
            this.btnSelectCustomer.Margin = new Padding(4);
            this.btnSelectCustomer.Name = "btnSelectCustomer";
            this.btnSelectCustomer.Size = new Size(104, 30);
            this.btnSelectCustomer.TabIndex = 1;
            this.btnSelectCustomer.Text = "اختيار عميل";
            this.btnSelectCustomer.UseVisualStyleBackColor = true;

            //
            // btnClearCustomer
            //
            this.btnClearCustomer.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnClearCustomer.Font = new Font("Segoe UI", 9F);
            this.btnClearCustomer.Location = new Point(328, 50);
            this.btnClearCustomer.Margin = new Padding(4);
            this.btnClearCustomer.Name = "btnClearCustomer";
            this.btnClearCustomer.Size = new Size(104, 30);
            this.btnClearCustomer.TabIndex = 2;
            this.btnClearCustomer.Text = "إلغاء العميل";
            this.btnClearCustomer.UseVisualStyleBackColor = true;
            this.btnClearCustomer.Visible = false;

            //
            // grpPayment
            //
            this.grpPayment.Controls.Add(this.lblDiscountPercentage);
            this.grpPayment.Controls.Add(this.txtDiscountPercentage);
            this.grpPayment.Controls.Add(this.lblDiscountAmount);
            this.grpPayment.Controls.Add(this.txtDiscountAmount);
            this.grpPayment.Controls.Add(this.btnProcessSale);
            this.grpPayment.Controls.Add(this.btnClearCart);
            this.grpPayment.Dock = DockStyle.Fill;
            this.grpPayment.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.grpPayment.Location = new Point(0, 480);
            this.grpPayment.Margin = new Padding(4);
            this.grpPayment.Name = "grpPayment";
            this.grpPayment.Padding = new Padding(4);
            this.grpPayment.Size = new Size(552, 312);
            this.grpPayment.TabIndex = 2;
            this.grpPayment.TabStop = false;
            this.grpPayment.Text = "الخصم والدفع";

            //
            // lblDiscountPercentage
            //
            this.lblDiscountPercentage.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.lblDiscountPercentage.AutoSize = true;
            this.lblDiscountPercentage.Font = new Font("Segoe UI", 9F);
            this.lblDiscountPercentage.Location = new Point(460, 30);
            this.lblDiscountPercentage.Margin = new Padding(4, 0, 4, 0);
            this.lblDiscountPercentage.Name = "lblDiscountPercentage";
            this.lblDiscountPercentage.Size = new Size(84, 20);
            this.lblDiscountPercentage.TabIndex = 0;
            this.lblDiscountPercentage.Text = "خصم نسبي %";

            //
            // txtDiscountPercentage
            //
            this.txtDiscountPercentage.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.txtDiscountPercentage.Font = new Font("Segoe UI", 10F);
            this.txtDiscountPercentage.Location = new Point(328, 27);
            this.txtDiscountPercentage.Margin = new Padding(4);
            this.txtDiscountPercentage.Name = "txtDiscountPercentage";
            this.txtDiscountPercentage.Size = new Size(124, 30);
            this.txtDiscountPercentage.TabIndex = 1;
            this.txtDiscountPercentage.TextAlign = HorizontalAlignment.Center;

            //
            // lblDiscountAmount
            //
            this.lblDiscountAmount.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Left)));
            this.lblDiscountAmount.AutoSize = true;
            this.lblDiscountAmount.Font = new Font("Segoe UI", 9F);
            this.lblDiscountAmount.Location = new Point(180, 30);
            this.lblDiscountAmount.Margin = new Padding(4, 0, 4, 0);
            this.lblDiscountAmount.Name = "lblDiscountAmount";
            this.lblDiscountAmount.Size = new Size(76, 20);
            this.lblDiscountAmount.TabIndex = 2;
            this.lblDiscountAmount.Text = "خصم ثابت";

            //
            // txtDiscountAmount
            //
            this.txtDiscountAmount.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Left)));
            this.txtDiscountAmount.Font = new Font("Segoe UI", 10F);
            this.txtDiscountAmount.Location = new Point(48, 27);
            this.txtDiscountAmount.Margin = new Padding(4);
            this.txtDiscountAmount.Name = "txtDiscountAmount";
            this.txtDiscountAmount.Size = new Size(124, 30);
            this.txtDiscountAmount.TabIndex = 3;
            this.txtDiscountAmount.TextAlign = HorizontalAlignment.Center;

            //
            // btnProcessSale
            //
            this.btnProcessSale.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.btnProcessSale.BackColor = Color.Green;
            this.btnProcessSale.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.btnProcessSale.ForeColor = Color.White;
            this.btnProcessSale.Location = new Point(328, 250);
            this.btnProcessSale.Margin = new Padding(4);
            this.btnProcessSale.Name = "btnProcessSale";
            this.btnProcessSale.Size = new Size(216, 50);
            this.btnProcessSale.TabIndex = 4;
            this.btnProcessSale.Text = "إتمام البيع";
            this.btnProcessSale.UseVisualStyleBackColor = false;

            //
            // btnClearCart
            //
            this.btnClearCart.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Left)));
            this.btnClearCart.BackColor = Color.OrangeRed;
            this.btnClearCart.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnClearCart.ForeColor = Color.White;
            this.btnClearCart.Location = new Point(8, 250);
            this.btnClearCart.Margin = new Padding(4);
            this.btnClearCart.Name = "btnClearCart";
            this.btnClearCart.Size = new Size(120, 50);
            this.btnClearCart.TabIndex = 5;
            this.btnClearCart.Text = "مسح السلة";
            this.btnClearCart.UseVisualStyleBackColor = false;

            this.mainLayout.ResumeLayout(false);
            this.leftPanel.ResumeLayout(false);
            this.rightPanel.ResumeLayout(false);
            this.grpProducts.ResumeLayout(false);
            this.grpProducts.PerformLayout();
            this.grpCart.ResumeLayout(false);
            this.grpCart.PerformLayout();
            this.grpCustomer.ResumeLayout(false);
            this.grpCustomer.PerformLayout();
            this.grpPayment.ResumeLayout(false);
            this.grpPayment.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProducts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCart)).EndInit();
            this.ResumeLayout(false);
        }
    }
}
