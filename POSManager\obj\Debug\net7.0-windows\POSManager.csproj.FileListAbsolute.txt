D:\sys\POSManager\bin\Debug\net7.0-windows\POSManager.exe
D:\sys\POSManager\bin\Debug\net7.0-windows\POSManager.deps.json
D:\sys\POSManager\bin\Debug\net7.0-windows\POSManager.runtimeconfig.json
D:\sys\POSManager\bin\Debug\net7.0-windows\POSManager.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\POSManager.pdb
D:\sys\POSManager\bin\Debug\net7.0-windows\Microsoft.Data.Sqlite.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\SQLitePCLRaw.batteries_v2.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\SQLitePCLRaw.core.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\browser-wasm\nativeassets\net7.0\e_sqlite3.a
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-musl-s390x\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\win-arm\native\e_sqlite3.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\win-x64\native\e_sqlite3.dll
D:\sys\POSManager\bin\Debug\net7.0-windows\runtimes\win-x86\native\e_sqlite3.dll
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.csproj.AssemblyReference.cache
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.GeneratedMSBuildEditorConfig.editorconfig
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.AssemblyInfoInputs.cache
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.AssemblyInfo.cs
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.csproj.CoreCompileInputs.cache
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.csproj.CopyComplete
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.dll
D:\sys\POSManager\obj\Debug\net7.0-windows\refint\POSManager.dll
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.pdb
D:\sys\POSManager\obj\Debug\net7.0-windows\POSManager.genruntimeconfig.cache
D:\sys\POSManager\obj\Debug\net7.0-windows\ref\POSManager.dll
