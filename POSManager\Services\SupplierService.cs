using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using POSManager.Models;
using POSManager.Data;

namespace POSManager.Services
{
    public class SupplierService
    {
        private readonly DatabaseManager _dbManager;
        private static SupplierService? _instance;
        private static readonly object _lock = new object();

        private SupplierService()
        {
            _dbManager = DatabaseManager.Instance;
        }

        public static SupplierService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new SupplierService();
                    }
                }
                return _instance;
            }
        }

        #region CRUD Operations

        // إضافة مورد جديد
        public async Task<bool> AddSupplierAsync(Supplier supplier)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    INSERT INTO AdvancedSuppliers 
                    (Name, Code, ContactPerson, Phone, Email, Address, City, PostalCode, Country, 
                     TaxNumber, PaymentTermsDays, CreditLimit, IsActive, Notes, CreatedAt)
                    VALUES 
                    (@Name, @Code, @ContactPerson, @Phone, @Email, @Address, @City, @PostalCode, @Country,
                     @TaxNumber, @PaymentTermsDays, @CreditLimit, @IsActive, @Notes, @CreatedAt)";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Name", supplier.Name);
                command.Parameters.AddWithValue("@Code", supplier.Code ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ContactPerson", supplier.ContactPerson ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Phone", supplier.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", supplier.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", supplier.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@City", supplier.City ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PostalCode", supplier.PostalCode ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Country", supplier.Country ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TaxNumber", supplier.TaxNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PaymentTermsDays", supplier.PaymentTermsDays);
                command.Parameters.AddWithValue("@CreditLimit", supplier.CreditLimit);
                command.Parameters.AddWithValue("@IsActive", supplier.IsActive);
                command.Parameters.AddWithValue("@Notes", supplier.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CreatedAt", supplier.CreatedAt);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // تحديث مورد
        public async Task<bool> UpdateSupplierAsync(Supplier supplier)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    UPDATE AdvancedSuppliers SET
                        Name = @Name,
                        Code = @Code,
                        ContactPerson = @ContactPerson,
                        Phone = @Phone,
                        Email = @Email,
                        Address = @Address,
                        City = @City,
                        PostalCode = @PostalCode,
                        Country = @Country,
                        TaxNumber = @TaxNumber,
                        PaymentTermsDays = @PaymentTermsDays,
                        CreditLimit = @CreditLimit,
                        IsActive = @IsActive,
                        Notes = @Notes
                    WHERE Id = @Id";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", supplier.Id);
                command.Parameters.AddWithValue("@Name", supplier.Name);
                command.Parameters.AddWithValue("@Code", supplier.Code ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ContactPerson", supplier.ContactPerson ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Phone", supplier.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", supplier.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", supplier.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@City", supplier.City ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PostalCode", supplier.PostalCode ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Country", supplier.Country ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TaxNumber", supplier.TaxNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PaymentTermsDays", supplier.PaymentTermsDays);
                command.Parameters.AddWithValue("@CreditLimit", supplier.CreditLimit);
                command.Parameters.AddWithValue("@IsActive", supplier.IsActive);
                command.Parameters.AddWithValue("@Notes", supplier.Notes ?? (object)DBNull.Value);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // حذف مورد (إلغاء تفعيل)
        public async Task<bool> DeleteSupplierAsync(int supplierId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "UPDATE AdvancedSuppliers SET IsActive = 0 WHERE Id = @Id";
                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", supplierId);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // الحصول على مورد بالمعرف
        public async Task<Supplier?> GetSupplierByIdAsync(int supplierId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT * FROM AdvancedSuppliers WHERE Id = @Id";
                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", supplierId);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return MapSupplierFromReader(reader);
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return null;
        }

        // الحصول على جميع الموردين
        public async Task<List<Supplier>> GetSuppliersAsync(bool activeOnly = true)
        {
            var suppliers = new List<Supplier>();

            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT * FROM AdvancedSuppliers";
                if (activeOnly)
                    sql += " WHERE IsActive = 1";
                sql += " ORDER BY Name";

                using var command = new SqliteCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    suppliers.Add(MapSupplierFromReader(reader));
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return suppliers;
        }

        // البحث في الموردين
        public async Task<List<Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            var suppliers = new List<Supplier>();

            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT * FROM AdvancedSuppliers 
                    WHERE IsActive = 1 AND (
                        Name LIKE @SearchTerm OR 
                        Code LIKE @SearchTerm OR 
                        ContactPerson LIKE @SearchTerm OR 
                        Phone LIKE @SearchTerm OR 
                        Email LIKE @SearchTerm
                    )
                    ORDER BY Name";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    suppliers.Add(MapSupplierFromReader(reader));
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return suppliers;
        }

        #endregion

        #region Performance Tracking

        // تحديث إحصائيات المورد
        public async Task<bool> UpdateSupplierStatisticsAsync(int supplierId, decimal orderValue, bool onTimeDelivery)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    UPDATE AdvancedSuppliers SET
                        TotalOrders = TotalOrders + @OrderValue,
                        OrderCount = OrderCount + 1,
                        OnTimeDeliveries = OnTimeDeliveries + @OnTimeDelivery,
                        TotalDeliveries = TotalDeliveries + 1,
                        LastOrderDate = @LastOrderDate
                    WHERE Id = @Id";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", supplierId);
                command.Parameters.AddWithValue("@OrderValue", orderValue);
                command.Parameters.AddWithValue("@OnTimeDelivery", onTimeDelivery ? 1 : 0);
                command.Parameters.AddWithValue("@LastOrderDate", DateTime.Now);

                var result = await command.ExecuteNonQueryAsync() > 0;

                // تحديث تقييم الأداء
                if (result)
                {
                    await UpdatePerformanceRatingAsync(supplierId, connection);
                }

                return result;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // تحديث تقييم الأداء
        private async Task UpdatePerformanceRatingAsync(int supplierId, SqliteConnection connection)
        {
            try
            {
                // حساب تقييم الأداء بناءً على معدل التسليم في الوقت المحدد
                var sql = @"
                    UPDATE AdvancedSuppliers SET
                        PerformanceRating = CASE 
                            WHEN TotalDeliveries > 0 THEN 
                                CAST(OnTimeDeliveries AS REAL) / TotalDeliveries * 5.0
                            ELSE 0 
                        END
                    WHERE Id = @Id";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", supplierId);
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }
        }

        // الحصول على تقرير أداء المورد
        public async Task<SupplierPerformanceReport?> GetSupplierPerformanceReportAsync(int supplierId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT 
                        s.Id,
                        s.Name,
                        s.TotalOrders,
                        s.OrderCount,
                        s.OnTimeDeliveries,
                        s.TotalDeliveries,
                        s.PerformanceRating,
                        CASE WHEN s.TotalDeliveries > 0 THEN 
                            CAST(s.OnTimeDeliveries AS REAL) / s.TotalDeliveries * 100 
                        ELSE 0 END as OnTimeRate,
                        COUNT(po.Id) as PeriodOrders,
                        COALESCE(SUM(po.Total), 0) as PeriodOrderValue
                    FROM AdvancedSuppliers s
                    LEFT JOIN PurchaseOrders po ON s.Id = po.SupplierId 
                        AND po.OrderDate BETWEEN @FromDate AND @ToDate
                    WHERE s.Id = @SupplierId
                    GROUP BY s.Id";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@SupplierId", supplierId);
                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new SupplierPerformanceReport
                    {
                        SupplierId = Convert.ToInt32(reader[0]),
                        SupplierName = Convert.ToString(reader[1]) ?? "",
                        TotalOrders = Convert.ToInt32(reader[3]),
                        TotalOrderValue = Convert.ToDecimal(reader[2]),
                        OnTimeDeliveries = Convert.ToInt32(reader[4]),
                        OnTimeDeliveryRate = Convert.ToDecimal(reader[7]),
                        LateDeliveries = Convert.ToInt32(reader[5]) - Convert.ToInt32(reader[4]),
                        OverallRating = Convert.ToDecimal(reader[6]),
                        FromDate = fromDate,
                        ToDate = toDate
                    };
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return null;
        }

        #endregion

        #region Helper Methods

        // تحويل البيانات من قارئ قاعدة البيانات إلى كائن المورد
        private Supplier MapSupplierFromReader(SqliteDataReader reader)
        {
            return new Supplier
            {
                Id = Convert.ToInt32(reader[0]),
                Name = Convert.ToString(reader[1]) ?? "",
                Code = Convert.ToString(reader[2]),
                ContactPerson = Convert.ToString(reader[3]),
                Phone = Convert.ToString(reader[4]),
                Email = Convert.ToString(reader[5]),
                Address = Convert.ToString(reader[6]),
                City = Convert.ToString(reader[7]),
                PostalCode = Convert.ToString(reader[8]),
                Country = Convert.ToString(reader[9]),
                TaxNumber = Convert.ToString(reader[10]),
                PaymentTermsDays = Convert.ToInt32(reader[11]),
                CreditLimit = Convert.ToDecimal(reader[12]),
                CurrentBalance = Convert.ToDecimal(reader[13]),
                IsActive = Convert.ToBoolean(reader[14]),
                Notes = Convert.ToString(reader[15]),
                CreatedAt = Convert.ToDateTime(reader[16]),
                LastOrderDate = reader[17] != DBNull.Value ? Convert.ToDateTime(reader[17]) : null,
                TotalOrders = Convert.ToDecimal(reader[18]),
                OrderCount = Convert.ToInt32(reader[19]),
                PerformanceRating = Convert.ToDecimal(reader[20]),
                OnTimeDeliveries = Convert.ToInt32(reader[21]),
                TotalDeliveries = Convert.ToInt32(reader[22])
            };
        }

        // التحقق من صحة بيانات المورد
        public bool ValidateSupplier(Supplier supplier, out string errorMessage)
        {
            errorMessage = "";

            if (string.IsNullOrWhiteSpace(supplier.Name))
            {
                errorMessage = "اسم المورد مطلوب";
                return false;
            }

            if (supplier.Name.Length > 100)
            {
                errorMessage = "اسم المورد لا يجب أن يتجاوز 100 حرف";
                return false;
            }

            if (!string.IsNullOrWhiteSpace(supplier.Email) && !IsValidEmail(supplier.Email))
            {
                errorMessage = "البريد الإلكتروني غير صحيح";
                return false;
            }

            if (supplier.PaymentTermsDays < 0)
            {
                errorMessage = "مدة الدفع لا يمكن أن تكون سالبة";
                return false;
            }

            if (supplier.CreditLimit < 0)
            {
                errorMessage = "حد الائتمان لا يمكن أن يكون سالباً";
                return false;
            }

            return true;
        }

        // التحقق من صحة البريد الإلكتروني
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        // التحقق من وجود كود المورد
        public async Task<bool> IsSupplierCodeExistsAsync(string code, int excludeId = 0)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT COUNT(*) FROM AdvancedSuppliers WHERE Code = @Code AND Id != @ExcludeId";
                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Code", code);
                command.Parameters.AddWithValue("@ExcludeId", excludeId);

                var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
