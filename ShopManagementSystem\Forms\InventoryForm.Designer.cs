namespace ShopManagementSystem.Forms
{
    partial class InventoryForm
    {
        private System.ComponentModel.IContainer components = null;

        // Controls
        private TabControl tabControl;
        private TabPage tabOverview;
        private TabPage tabMovements;
        private TabPage tabAlerts;
        private TabPage tabAdjustments;

        // Overview Tab
        private Panel pnlStats;
        private Label lblTotalProducts;
        private Label lblLowStock;
        private Label lblOutOfStock;
        private Label lblTotalValue;
        private Label lblActiveAlerts;
        private DataGridView dgvLowStockProducts;
        private Button btnRefreshOverview;

        // Movements Tab
        private DataGridView dgvMovements;
        private ComboBox cmbMovementProduct;
        private DateTimePicker dtpMovementFrom;
        private DateTimePicker dtpMovementTo;
        private Button btnFilterMovements;
        private Button btnClearMovementFilter;

        // Alerts Tab
        private DataGridView dgvAlerts;
        private Button btnRefreshAlerts;
        private Button btnResolveAlert;
        private CheckBox chkShowResolvedAlerts;

        // Adjustments Tab
        private ComboBox cmbAdjustmentProduct;
        private NumericUpDown nudNewQuantity;
        private NumericUpDown nudMinimumQuantity;
        private TextBox txtAdjustmentNotes;
        private Button btnStockIn;
        private Button btnStockOut;
        private Button btnAdjustStock;
        private Label lblCurrentQuantity;
        private Label lblCurrentMinimum;

        // Common
        private Button btnClose;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.tabOverview = new TabPage();
            this.tabMovements = new TabPage();
            this.tabAlerts = new TabPage();
            this.tabAdjustments = new TabPage();

            // Overview Tab Controls
            this.pnlStats = new Panel();
            this.lblTotalProducts = new Label();
            this.lblLowStock = new Label();
            this.lblOutOfStock = new Label();
            this.lblTotalValue = new Label();
            this.lblActiveAlerts = new Label();
            this.dgvLowStockProducts = new DataGridView();
            this.btnRefreshOverview = new Button();

            // Movements Tab Controls
            this.dgvMovements = new DataGridView();
            this.cmbMovementProduct = new ComboBox();
            this.dtpMovementFrom = new DateTimePicker();
            this.dtpMovementTo = new DateTimePicker();
            this.btnFilterMovements = new Button();
            this.btnClearMovementFilter = new Button();

            // Alerts Tab Controls
            this.dgvAlerts = new DataGridView();
            this.btnRefreshAlerts = new Button();
            this.btnResolveAlert = new Button();
            this.chkShowResolvedAlerts = new CheckBox();

            // Adjustments Tab Controls
            this.cmbAdjustmentProduct = new ComboBox();
            this.nudNewQuantity = new NumericUpDown();
            this.nudMinimumQuantity = new NumericUpDown();
            this.txtAdjustmentNotes = new TextBox();
            this.btnStockIn = new Button();
            this.btnStockOut = new Button();
            this.btnAdjustStock = new Button();
            this.lblCurrentQuantity = new Label();
            this.lblCurrentMinimum = new Label();

            // Common Controls
            this.btnClose = new Button();

            this.SuspendLayout();

            // Form
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.btnClose);
            this.Name = "InventoryForm";
            this.RightToLeft = RightToLeft.Yes;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "إدارة المخزون";
            this.WindowState = FormWindowState.Maximized;

            // Tab Control
            this.tabControl.Controls.Add(this.tabOverview);
            this.tabControl.Controls.Add(this.tabMovements);
            this.tabControl.Controls.Add(this.tabAlerts);
            this.tabControl.Controls.Add(this.tabAdjustments);
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.Location = new Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new Size(1200, 750);
            this.tabControl.TabIndex = 0;

            // Overview Tab
            this.tabOverview.Controls.Add(this.pnlStats);
            this.tabOverview.Controls.Add(this.dgvLowStockProducts);
            this.tabOverview.Controls.Add(this.btnRefreshOverview);
            this.tabOverview.Location = new Point(4, 29);
            this.tabOverview.Name = "tabOverview";
            this.tabOverview.Padding = new Padding(3);
            this.tabOverview.Size = new Size(1192, 717);
            this.tabOverview.TabIndex = 0;
            this.tabOverview.Text = "نظرة عامة";
            this.tabOverview.UseVisualStyleBackColor = true;

            // Stats Panel
            this.pnlStats.Controls.Add(this.lblTotalProducts);
            this.pnlStats.Controls.Add(this.lblLowStock);
            this.pnlStats.Controls.Add(this.lblOutOfStock);
            this.pnlStats.Controls.Add(this.lblTotalValue);
            this.pnlStats.Controls.Add(this.lblActiveAlerts);
            this.pnlStats.Dock = DockStyle.Top;
            this.pnlStats.Location = new Point(3, 3);
            this.pnlStats.Name = "pnlStats";
            this.pnlStats.Size = new Size(1186, 120);
            this.pnlStats.TabIndex = 0;
            this.pnlStats.BorderStyle = BorderStyle.FixedSingle;

            // Stats Labels
            this.lblTotalProducts.AutoSize = true;
            this.lblTotalProducts.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblTotalProducts.Location = new Point(20, 20);
            this.lblTotalProducts.Name = "lblTotalProducts";
            this.lblTotalProducts.Size = new Size(150, 28);
            this.lblTotalProducts.TabIndex = 0;
            this.lblTotalProducts.Text = "إجمالي المنتجات: 0";

            this.lblLowStock.AutoSize = true;
            this.lblLowStock.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblLowStock.ForeColor = Color.Orange;
            this.lblLowStock.Location = new Point(250, 20);
            this.lblLowStock.Name = "lblLowStock";
            this.lblLowStock.Size = new Size(150, 28);
            this.lblLowStock.TabIndex = 1;
            this.lblLowStock.Text = "مخزون منخفض: 0";

            this.lblOutOfStock.AutoSize = true;
            this.lblOutOfStock.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblOutOfStock.ForeColor = Color.Red;
            this.lblOutOfStock.Location = new Point(480, 20);
            this.lblOutOfStock.Name = "lblOutOfStock";
            this.lblOutOfStock.Size = new Size(150, 28);
            this.lblOutOfStock.TabIndex = 2;
            this.lblOutOfStock.Text = "مخزون نافد: 0";

            this.lblTotalValue.AutoSize = true;
            this.lblTotalValue.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblTotalValue.Location = new Point(20, 60);
            this.lblTotalValue.Name = "lblTotalValue";
            this.lblTotalValue.Size = new Size(200, 28);
            this.lblTotalValue.TabIndex = 3;
            this.lblTotalValue.Text = "إجمالي قيمة المخزون: 0.00";

            this.lblActiveAlerts.AutoSize = true;
            this.lblActiveAlerts.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblActiveAlerts.ForeColor = Color.Red;
            this.lblActiveAlerts.Location = new Point(250, 60);
            this.lblActiveAlerts.Name = "lblActiveAlerts";
            this.lblActiveAlerts.Size = new Size(150, 28);
            this.lblActiveAlerts.TabIndex = 4;
            this.lblActiveAlerts.Text = "تنبيهات نشطة: 0";

            // Low Stock Products DataGridView
            this.dgvLowStockProducts.AllowUserToAddRows = false;
            this.dgvLowStockProducts.AllowUserToDeleteRows = false;
            this.dgvLowStockProducts.Anchor = ((AnchorStyles)((((AnchorStyles.Top | AnchorStyles.Bottom) | AnchorStyles.Left) | AnchorStyles.Right)));
            this.dgvLowStockProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvLowStockProducts.Location = new Point(6, 170);
            this.dgvLowStockProducts.Name = "dgvLowStockProducts";
            this.dgvLowStockProducts.ReadOnly = true;
            this.dgvLowStockProducts.RowHeadersVisible = false;
            this.dgvLowStockProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvLowStockProducts.Size = new Size(1180, 500);
            this.dgvLowStockProducts.TabIndex = 1;

            // Refresh Overview Button
            this.btnRefreshOverview.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnRefreshOverview.Location = new Point(1086, 129);
            this.btnRefreshOverview.Name = "btnRefreshOverview";
            this.btnRefreshOverview.Size = new Size(100, 35);
            this.btnRefreshOverview.TabIndex = 2;
            this.btnRefreshOverview.Text = "تحديث";
            this.btnRefreshOverview.UseVisualStyleBackColor = true;
            this.btnRefreshOverview.Click += new EventHandler(this.btnRefreshOverview_Click);

            // Movements Tab
            this.tabMovements.Controls.Add(this.dgvMovements);
            this.tabMovements.Controls.Add(this.cmbMovementProduct);
            this.tabMovements.Controls.Add(this.dtpMovementFrom);
            this.tabMovements.Controls.Add(this.dtpMovementTo);
            this.tabMovements.Controls.Add(this.btnFilterMovements);
            this.tabMovements.Controls.Add(this.btnClearMovementFilter);
            this.tabMovements.Location = new Point(4, 29);
            this.tabMovements.Name = "tabMovements";
            this.tabMovements.Padding = new Padding(3);
            this.tabMovements.Size = new Size(1192, 717);
            this.tabMovements.TabIndex = 1;
            this.tabMovements.Text = "حركات المخزون";
            this.tabMovements.UseVisualStyleBackColor = true;

            // Movements DataGridView
            this.dgvMovements.AllowUserToAddRows = false;
            this.dgvMovements.AllowUserToDeleteRows = false;
            this.dgvMovements.Anchor = ((AnchorStyles)((((AnchorStyles.Top | AnchorStyles.Bottom) | AnchorStyles.Left) | AnchorStyles.Right)));
            this.dgvMovements.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvMovements.Location = new Point(6, 60);
            this.dgvMovements.Name = "dgvMovements";
            this.dgvMovements.ReadOnly = true;
            this.dgvMovements.RowHeadersVisible = false;
            this.dgvMovements.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvMovements.Size = new Size(1180, 650);
            this.dgvMovements.TabIndex = 0;

            // Movement Product ComboBox
            this.cmbMovementProduct.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbMovementProduct.Location = new Point(6, 20);
            this.cmbMovementProduct.Name = "cmbMovementProduct";
            this.cmbMovementProduct.Size = new Size(200, 28);
            this.cmbMovementProduct.TabIndex = 1;

            // Movement Date Pickers
            this.dtpMovementFrom.Location = new Point(220, 20);
            this.dtpMovementFrom.Name = "dtpMovementFrom";
            this.dtpMovementFrom.Size = new Size(150, 27);
            this.dtpMovementFrom.TabIndex = 2;

            this.dtpMovementTo.Location = new Point(380, 20);
            this.dtpMovementTo.Name = "dtpMovementTo";
            this.dtpMovementTo.Size = new Size(150, 27);
            this.dtpMovementTo.TabIndex = 3;

            // Movement Filter Buttons
            this.btnFilterMovements.Location = new Point(540, 18);
            this.btnFilterMovements.Name = "btnFilterMovements";
            this.btnFilterMovements.Size = new Size(80, 30);
            this.btnFilterMovements.TabIndex = 4;
            this.btnFilterMovements.Text = "فلترة";
            this.btnFilterMovements.UseVisualStyleBackColor = true;
            this.btnFilterMovements.Click += new EventHandler(this.btnFilterMovements_Click);

            this.btnClearMovementFilter.Location = new Point(630, 18);
            this.btnClearMovementFilter.Name = "btnClearMovementFilter";
            this.btnClearMovementFilter.Size = new Size(80, 30);
            this.btnClearMovementFilter.TabIndex = 5;
            this.btnClearMovementFilter.Text = "مسح";
            this.btnClearMovementFilter.UseVisualStyleBackColor = true;
            this.btnClearMovementFilter.Click += new EventHandler(this.btnClearMovementFilter_Click);

            // Alerts Tab
            this.tabAlerts.Controls.Add(this.dgvAlerts);
            this.tabAlerts.Controls.Add(this.btnRefreshAlerts);
            this.tabAlerts.Controls.Add(this.btnResolveAlert);
            this.tabAlerts.Controls.Add(this.chkShowResolvedAlerts);
            this.tabAlerts.Location = new Point(4, 29);
            this.tabAlerts.Name = "tabAlerts";
            this.tabAlerts.Padding = new Padding(3);
            this.tabAlerts.Size = new Size(1192, 717);
            this.tabAlerts.TabIndex = 2;
            this.tabAlerts.Text = "التنبيهات";
            this.tabAlerts.UseVisualStyleBackColor = true;

            // Alerts DataGridView
            this.dgvAlerts.AllowUserToAddRows = false;
            this.dgvAlerts.AllowUserToDeleteRows = false;
            this.dgvAlerts.Anchor = ((AnchorStyles)((((AnchorStyles.Top | AnchorStyles.Bottom) | AnchorStyles.Left) | AnchorStyles.Right)));
            this.dgvAlerts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvAlerts.Location = new Point(6, 60);
            this.dgvAlerts.Name = "dgvAlerts";
            this.dgvAlerts.ReadOnly = true;
            this.dgvAlerts.RowHeadersVisible = false;
            this.dgvAlerts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvAlerts.Size = new Size(1180, 650);
            this.dgvAlerts.TabIndex = 0;

            // Alerts Buttons
            this.btnRefreshAlerts.Location = new Point(6, 20);
            this.btnRefreshAlerts.Name = "btnRefreshAlerts";
            this.btnRefreshAlerts.Size = new Size(100, 30);
            this.btnRefreshAlerts.TabIndex = 1;
            this.btnRefreshAlerts.Text = "تحديث";
            this.btnRefreshAlerts.UseVisualStyleBackColor = true;
            this.btnRefreshAlerts.Click += new EventHandler(this.btnRefreshAlerts_Click);

            this.btnResolveAlert.Location = new Point(120, 20);
            this.btnResolveAlert.Name = "btnResolveAlert";
            this.btnResolveAlert.Size = new Size(100, 30);
            this.btnResolveAlert.TabIndex = 2;
            this.btnResolveAlert.Text = "حل التنبيه";
            this.btnResolveAlert.UseVisualStyleBackColor = true;
            this.btnResolveAlert.Click += new EventHandler(this.btnResolveAlert_Click);

            this.chkShowResolvedAlerts.AutoSize = true;
            this.chkShowResolvedAlerts.Location = new Point(240, 25);
            this.chkShowResolvedAlerts.Name = "chkShowResolvedAlerts";
            this.chkShowResolvedAlerts.Size = new Size(150, 24);
            this.chkShowResolvedAlerts.TabIndex = 3;
            this.chkShowResolvedAlerts.Text = "إظهار التنبيهات المحلولة";
            this.chkShowResolvedAlerts.UseVisualStyleBackColor = true;
            this.chkShowResolvedAlerts.CheckedChanged += new EventHandler(this.chkShowResolvedAlerts_CheckedChanged);

            // Adjustments Tab
            this.tabAdjustments.Controls.Add(this.cmbAdjustmentProduct);
            this.tabAdjustments.Controls.Add(this.nudNewQuantity);
            this.tabAdjustments.Controls.Add(this.nudMinimumQuantity);
            this.tabAdjustments.Controls.Add(this.txtAdjustmentNotes);
            this.tabAdjustments.Controls.Add(this.btnStockIn);
            this.tabAdjustments.Controls.Add(this.btnStockOut);
            this.tabAdjustments.Controls.Add(this.btnAdjustStock);
            this.tabAdjustments.Controls.Add(this.lblCurrentQuantity);
            this.tabAdjustments.Controls.Add(this.lblCurrentMinimum);
            this.tabAdjustments.Location = new Point(4, 29);
            this.tabAdjustments.Name = "tabAdjustments";
            this.tabAdjustments.Padding = new Padding(3);
            this.tabAdjustments.Size = new Size(1192, 717);
            this.tabAdjustments.TabIndex = 3;
            this.tabAdjustments.Text = "تعديل المخزون";
            this.tabAdjustments.UseVisualStyleBackColor = true;

            // Adjustment Product ComboBox
            this.cmbAdjustmentProduct.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbAdjustmentProduct.Location = new Point(20, 50);
            this.cmbAdjustmentProduct.Name = "cmbAdjustmentProduct";
            this.cmbAdjustmentProduct.Size = new Size(300, 28);
            this.cmbAdjustmentProduct.TabIndex = 0;
            this.cmbAdjustmentProduct.SelectedIndexChanged += new EventHandler(this.cmbAdjustmentProduct_SelectedIndexChanged);

            // Current Quantity and Minimum Labels
            this.lblCurrentQuantity.AutoSize = true;
            this.lblCurrentQuantity.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblCurrentQuantity.Location = new Point(20, 100);
            this.lblCurrentQuantity.Name = "lblCurrentQuantity";
            this.lblCurrentQuantity.Size = new Size(150, 28);
            this.lblCurrentQuantity.TabIndex = 1;
            this.lblCurrentQuantity.Text = "الكمية الحالية: 0";

            this.lblCurrentMinimum.AutoSize = true;
            this.lblCurrentMinimum.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblCurrentMinimum.Location = new Point(20, 140);
            this.lblCurrentMinimum.Name = "lblCurrentMinimum";
            this.lblCurrentMinimum.Size = new Size(150, 28);
            this.lblCurrentMinimum.TabIndex = 2;
            this.lblCurrentMinimum.Text = "الحد الأدنى: 0";

            // Numeric UpDowns
            this.nudNewQuantity.Location = new Point(20, 200);
            this.nudNewQuantity.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.nudNewQuantity.Name = "nudNewQuantity";
            this.nudNewQuantity.Size = new Size(150, 27);
            this.nudNewQuantity.TabIndex = 3;

            this.nudMinimumQuantity.Location = new Point(20, 250);
            this.nudMinimumQuantity.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.nudMinimumQuantity.Name = "nudMinimumQuantity";
            this.nudMinimumQuantity.Size = new Size(150, 27);
            this.nudMinimumQuantity.TabIndex = 4;

            // Notes TextBox
            this.txtAdjustmentNotes.Location = new Point(20, 300);
            this.txtAdjustmentNotes.Multiline = true;
            this.txtAdjustmentNotes.Name = "txtAdjustmentNotes";
            this.txtAdjustmentNotes.PlaceholderText = "ملاحظات التعديل";
            this.txtAdjustmentNotes.Size = new Size(400, 80);
            this.txtAdjustmentNotes.TabIndex = 5;

            // Action Buttons
            this.btnStockIn.Location = new Point(20, 400);
            this.btnStockIn.Name = "btnStockIn";
            this.btnStockIn.Size = new Size(120, 40);
            this.btnStockIn.TabIndex = 6;
            this.btnStockIn.Text = "إدخال مخزون";
            this.btnStockIn.UseVisualStyleBackColor = true;
            this.btnStockIn.Click += new EventHandler(this.btnStockIn_Click);

            this.btnStockOut.Location = new Point(150, 400);
            this.btnStockOut.Name = "btnStockOut";
            this.btnStockOut.Size = new Size(120, 40);
            this.btnStockOut.TabIndex = 7;
            this.btnStockOut.Text = "إخراج مخزون";
            this.btnStockOut.UseVisualStyleBackColor = true;
            this.btnStockOut.Click += new EventHandler(this.btnStockOut_Click);

            this.btnAdjustStock.Location = new Point(280, 400);
            this.btnAdjustStock.Name = "btnAdjustStock";
            this.btnAdjustStock.Size = new Size(120, 40);
            this.btnAdjustStock.TabIndex = 8;
            this.btnAdjustStock.Text = "تعديل المخزون";
            this.btnAdjustStock.UseVisualStyleBackColor = true;
            this.btnAdjustStock.Click += new EventHandler(this.btnAdjustStock_Click);

            // Close Button
            this.btnClose.Anchor = ((AnchorStyles)((AnchorStyles.Bottom | AnchorStyles.Right)));
            this.btnClose.Location = new Point(1100, 760);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 35);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "إغلاق";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}
