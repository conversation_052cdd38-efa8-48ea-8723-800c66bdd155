using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class CustomerSelectionForm : Form
    {
        private List<Customer> _customers = new List<Customer>();
        public Customer? SelectedCustomer { get; private set; }

        public CustomerSelectionForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private async void InitializeForm()
        {
            try
            {
                await LoadCustomersAsync();
                SetupDataGridView();
                txtSearch.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadCustomersAsync()
        {
            _customers = await CustomerService.GetAllCustomersAsync();
            dgvCustomers.DataSource = _customers;
        }

        private void SetupDataGridView()
        {
            if (dgvCustomers.Columns.Count > 0)
            {
                dgvCustomers.Columns["Id"].Visible = false;
                dgvCustomers.Columns["IsActive"].Visible = false;
                dgvCustomers.Columns["CreatedAt"].Visible = false;
                
                dgvCustomers.Columns["Name"].HeaderText = "اسم العميل";
                dgvCustomers.Columns["Phone"].HeaderText = "الهاتف";
                dgvCustomers.Columns["Email"].HeaderText = "البريد الإلكتروني";
                dgvCustomers.Columns["Address"].HeaderText = "العنوان";
                
                dgvCustomers.Columns["Name"].Width = 200;
                dgvCustomers.Columns["Phone"].Width = 120;
                dgvCustomers.Columns["Email"].Width = 180;
                dgvCustomers.Columns["Address"].Width = 200;
            }
        }

        private async void txtSearch_TextChanged(object sender, EventArgs e)
        {
            await SearchCustomersAsync();
        }

        private async Task SearchCustomersAsync()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                
                if (string.IsNullOrEmpty(searchTerm))
                {
                    dgvCustomers.DataSource = _customers;
                }
                else
                {
                    var filteredCustomers = await CustomerService.SearchCustomersAsync(searchTerm);
                    dgvCustomers.DataSource = filteredCustomers;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            SelectCustomer();
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            SelectCustomer();
        }

        private void SelectCustomer()
        {
            try
            {
                if (dgvCustomers.CurrentRow != null)
                {
                    SelectedCustomer = (Customer)dgvCustomers.CurrentRow.DataBoundItem;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار عميل", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار العميل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            try
            {
                using var addCustomerForm = new AddCustomerForm();
                if (addCustomerForm.ShowDialog() == DialogResult.OK)
                {
                    // إعادة تحميل العملاء
                    _ = LoadCustomersAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة عميل جديد: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                if (dgvCustomers.Rows.Count == 1)
                {
                    dgvCustomers.CurrentCell = dgvCustomers.Rows[0].Cells[0];
                    SelectCustomer();
                }
                else if (dgvCustomers.Rows.Count > 1)
                {
                    dgvCustomers.Focus();
                    if (dgvCustomers.Rows.Count > 0)
                    {
                        dgvCustomers.CurrentCell = dgvCustomers.Rows[0].Cells[0];
                    }
                }
            }
        }

        private void dgvCustomers_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                SelectCustomer();
            }
        }
    }
}
