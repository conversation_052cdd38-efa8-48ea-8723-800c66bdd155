using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;

namespace POSManager.Services
{
    public static class SaleService
    {
        public static async Task<string> GenerateInvoiceNumberAsync()
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                // الحصول على بادئة الفاتورة من الإعدادات
                var prefixCommand = connection.CreateCommand();
                prefixCommand.CommandText = "SELECT Value FROM Settings WHERE Key = 'InvoicePrefix'";
                var prefixResult = await prefixCommand.ExecuteScalarAsync();
                var prefix = prefixResult?.ToString() ?? "INV";

                // الحصول على آخر رقم فاتورة
                var command = connection.CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM Sales WHERE DATE(SaleDate) = DATE('now')";
                var todayCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                var invoiceNumber = $"{prefix}-{DateTime.Now:yyyyMMdd}-{(todayCount + 1):D4}";
                return invoiceNumber;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في توليد رقم الفاتورة: {ex.Message}");
            }
        }

        public static async Task<bool> CreateSaleAsync(Sale sale)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // إدراج الفاتورة الرئيسية
                var saleCommand = connection.CreateCommand();
                saleCommand.Transaction = transaction;
                saleCommand.CommandText = @"
                    INSERT INTO Sales (InvoiceNumber, CustomerId, UserId, SubTotal, DiscountAmount, 
                                     DiscountPercentage, TaxAmount, TaxPercentage, TotalAmount, 
                                     PaidAmount, ChangeAmount, PaymentMethod, Notes, SaleDate)
                    VALUES (@InvoiceNumber, @CustomerId, @UserId, @SubTotal, @DiscountAmount, 
                           @DiscountPercentage, @TaxAmount, @TaxPercentage, @TotalAmount, 
                           @PaidAmount, @ChangeAmount, @PaymentMethod, @Notes, @SaleDate);
                    SELECT last_insert_rowid();";

                saleCommand.Parameters.AddWithValue("@InvoiceNumber", sale.InvoiceNumber);
                saleCommand.Parameters.AddWithValue("@CustomerId", sale.CustomerId.HasValue ? sale.CustomerId.Value : DBNull.Value);
                saleCommand.Parameters.AddWithValue("@UserId", sale.UserId);
                saleCommand.Parameters.AddWithValue("@SubTotal", sale.SubTotal);
                saleCommand.Parameters.AddWithValue("@DiscountAmount", sale.DiscountAmount);
                saleCommand.Parameters.AddWithValue("@DiscountPercentage", sale.DiscountPercentage);
                saleCommand.Parameters.AddWithValue("@TaxAmount", sale.TaxAmount);
                saleCommand.Parameters.AddWithValue("@TaxPercentage", sale.TaxPercentage);
                saleCommand.Parameters.AddWithValue("@TotalAmount", sale.TotalAmount);
                saleCommand.Parameters.AddWithValue("@PaidAmount", sale.PaidAmount);
                saleCommand.Parameters.AddWithValue("@ChangeAmount", sale.ChangeAmount);
                saleCommand.Parameters.AddWithValue("@PaymentMethod", sale.PaymentMethod.ToString());
                saleCommand.Parameters.AddWithValue("@Notes", sale.Notes ?? "");
                saleCommand.Parameters.AddWithValue("@SaleDate", sale.SaleDate);

                var saleId = Convert.ToInt32(await saleCommand.ExecuteScalarAsync());

                // إدراج تفاصيل الفاتورة
                foreach (var detail in sale.SaleDetails)
                {
                    var detailCommand = connection.CreateCommand();
                    detailCommand.Transaction = transaction;
                    detailCommand.CommandText = @"
                        INSERT INTO SaleDetails (SaleId, ProductId, ProductName, ProductCode, 
                                               Quantity, UnitPrice, TotalPrice)
                        VALUES (@SaleId, @ProductId, @ProductName, @ProductCode, 
                               @Quantity, @UnitPrice, @TotalPrice)";

                    detailCommand.Parameters.AddWithValue("@SaleId", saleId);
                    detailCommand.Parameters.AddWithValue("@ProductId", detail.ProductId);
                    detailCommand.Parameters.AddWithValue("@ProductName", detail.ProductName);
                    detailCommand.Parameters.AddWithValue("@ProductCode", detail.ProductCode ?? "");
                    detailCommand.Parameters.AddWithValue("@Quantity", detail.Quantity);
                    detailCommand.Parameters.AddWithValue("@UnitPrice", detail.UnitPrice);
                    detailCommand.Parameters.AddWithValue("@TotalPrice", detail.TotalPrice);

                    await detailCommand.ExecuteNonQueryAsync();

                    // تحديث المخزون
                    await UpdateProductStockAsync(connection, transaction, detail.ProductId, detail.Quantity, sale.UserId);
                }

                // تسجيل النشاط
                await LogActivityAsync(connection, transaction, sale.UserId, "إنشاء فاتورة", "Sales", saleId, $"فاتورة رقم: {sale.InvoiceNumber}");

                transaction.Commit();
                return true;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw new Exception($"خطأ في حفظ الفاتورة: {ex.Message}");
            }
        }

        private static async Task UpdateProductStockAsync(SqliteConnection connection, SqliteTransaction transaction, int productId, int soldQuantity, int userId)
        {
            // الحصول على المخزون الحالي
            var stockCommand = connection.CreateCommand();
            stockCommand.Transaction = transaction;
            stockCommand.CommandText = "SELECT Stock, Name FROM Products WHERE Id = @ProductId";
            stockCommand.Parameters.AddWithValue("@ProductId", productId);

            using var reader = await stockCommand.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var currentStock = Convert.ToInt32(reader["Stock"]);
                var productName = reader["Name"].ToString();
                var newStock = currentStock - soldQuantity;

                reader.Close();

                // تحديث المخزون
                var updateCommand = connection.CreateCommand();
                updateCommand.Transaction = transaction;
                updateCommand.CommandText = "UPDATE Products SET Stock = @NewStock WHERE Id = @ProductId";
                updateCommand.Parameters.AddWithValue("@NewStock", newStock);
                updateCommand.Parameters.AddWithValue("@ProductId", productId);
                await updateCommand.ExecuteNonQueryAsync();

                // تسجيل حركة المخزون
                var logCommand = connection.CreateCommand();
                logCommand.Transaction = transaction;
                logCommand.CommandText = @"
                    INSERT INTO InventoryLog (ProductId, ProductName, ChangeType, OldQuantity,
                                            NewQuantity, QuantityChanged, UserId, Reason, CreatedAt)
                    VALUES (@ProductId, @ProductName, @ChangeType, @OldQuantity,
                           @NewQuantity, @QuantityChanged, @UserId, @Reason, @CreatedAt)";

                var user = AuthService.CurrentUser;
                logCommand.Parameters.AddWithValue("@ProductId", productId);
                logCommand.Parameters.AddWithValue("@ProductName", productName);
                logCommand.Parameters.AddWithValue("@ChangeType", "Sale");
                logCommand.Parameters.AddWithValue("@OldQuantity", currentStock);
                logCommand.Parameters.AddWithValue("@NewQuantity", newStock);
                logCommand.Parameters.AddWithValue("@QuantityChanged", -soldQuantity);
                logCommand.Parameters.AddWithValue("@UserId", userId);
                logCommand.Parameters.AddWithValue("@Reason", "بيع منتج");
                logCommand.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

                await logCommand.ExecuteNonQueryAsync();
            }
        }

        private static async Task LogActivityAsync(SqliteConnection connection, SqliteTransaction transaction, int userId, string action, string tableName, int recordId, string details)
        {
            var command = connection.CreateCommand();
            command.Transaction = transaction;
            command.CommandText = @"
                INSERT INTO ActivityLog (UserId, UserName, Action, TableName, RecordId, Details, IPAddress, CreatedAt)
                VALUES (@UserId, @UserName, @Action, @TableName, @RecordId, @Details, @IPAddress, @CreatedAt)";

            var user = AuthService.CurrentUser;
            command.Parameters.AddWithValue("@UserId", userId);
            command.Parameters.AddWithValue("@UserName", user?.FullName ?? "");
            command.Parameters.AddWithValue("@Action", action);
            command.Parameters.AddWithValue("@TableName", tableName);
            command.Parameters.AddWithValue("@RecordId", recordId);
            command.Parameters.AddWithValue("@Details", details);
            command.Parameters.AddWithValue("@IPAddress", "127.0.0.1");
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        public static async Task<List<Sale>> GetTodaySalesAsync()
        {
            try
            {
                var sales = new List<Sale>();
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT s.*, c.Name as CustomerName, u.FullName as UserName
                    FROM Sales s
                    LEFT JOIN Customers c ON s.CustomerId = c.Id
                    LEFT JOIN Users u ON s.UserId = u.Id
                    WHERE DATE(s.SaleDate) = DATE('now')
                    ORDER BY s.SaleDate DESC";

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var sale = new Sale
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        InvoiceNumber = reader["InvoiceNumber"].ToString() ?? "",
                        CustomerId = reader["CustomerId"] != DBNull.Value ? Convert.ToInt32(reader["CustomerId"]) : null,
                        UserId = Convert.ToInt32(reader["UserId"]),
                        SubTotal = Convert.ToDecimal(reader["SubTotal"]),
                        DiscountAmount = Convert.ToDecimal(reader["DiscountAmount"]),
                        DiscountPercentage = Convert.ToDecimal(reader["DiscountPercentage"]),
                        TaxAmount = Convert.ToDecimal(reader["TaxAmount"]),
                        TaxPercentage = Convert.ToDecimal(reader["TaxPercentage"]),
                        TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                        PaidAmount = Convert.ToDecimal(reader["PaidAmount"]),
                        ChangeAmount = Convert.ToDecimal(reader["ChangeAmount"]),
                        PaymentMethod = Enum.Parse<PaymentMethod>(reader["PaymentMethod"].ToString() ?? "Cash"),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        SaleDate = Convert.ToDateTime(reader["SaleDate"])
                    };

                    // إضافة معلومات العميل والمستخدم
                    if (reader["CustomerName"] != DBNull.Value)
                    {
                        sale.Customer = new Customer { Name = reader["CustomerName"].ToString() ?? "" };
                    }
                    if (reader["UserName"] != DBNull.Value)
                    {
                        sale.User = new User { FullName = reader["UserName"].ToString() ?? "" };
                    }

                    sales.Add(sale);
                }

                return sales;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب مبيعات اليوم: {ex.Message}");
            }
        }

        public static async Task<decimal> GetTodayTotalSalesAsync()
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT COALESCE(SUM(TotalAmount), 0) FROM Sales WHERE DATE(SaleDate) = DATE('now')";
                
                var result = await command.ExecuteScalarAsync();
                return Convert.ToDecimal(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب إجمالي مبيعات اليوم: {ex.Message}");
            }
        }

        public static async Task<int> GetTodaySalesCountAsync()
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM Sales WHERE DATE(SaleDate) = DATE('now')";
                
                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب عدد مبيعات اليوم: {ex.Message}");
            }
        }
    }
}
