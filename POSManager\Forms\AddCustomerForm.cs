using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class AddCustomerForm : Form
    {
        public Customer? NewCustomer { get; private set; }

        public AddCustomerForm()
        {
            InitializeComponent();
            txtName.Focus();
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var customer = new Customer
                {
                    Name = txtName.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Address = txtAddress.Text.Trim(),
                    IsActive = true
                };

                var success = await CustomerService.AddCustomerAsync(customer);
                
                if (success)
                {
                    NewCustomer = customer;
                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة العميل", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من الهاتف (اختياري ولكن إذا تم إدخاله يجب أن يكون صحيحاً)
            if (!string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                var phone = txtPhone.Text.Trim();
                if (phone.Length < 10)
                {
                    MessageBox.Show("رقم الهاتف يجب أن يكون 10 أرقام على الأقل", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPhone.Focus();
                    return false;
                }
            }

            // التحقق من البريد الإلكتروني (اختياري ولكن إذا تم إدخاله يجب أن يكون صحيحاً)
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                var email = txtEmail.Text.Trim();
                if (!IsValidEmail(email))
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                txtPhone.Focus();
            }
        }

        private void txtPhone_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                txtEmail.Focus();
            }
        }

        private void txtEmail_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                txtAddress.Focus();
            }
        }

        private void txtAddress_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                btnSave.Focus();
            }
        }

        private void txtPhone_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح فقط بالأرقام والمسافات والرموز الخاصة بأرقام الهاتف
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && 
                e.KeyChar != ' ' && e.KeyChar != '-' && e.KeyChar != '(' && 
                e.KeyChar != ')' && e.KeyChar != '+')
            {
                e.Handled = true;
            }
        }
    }
}
