using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;
using System.Security.Cryptography;
using System.Text;

namespace POSManager.Services
{
    public static class UserService
    {

        public static async Task<List<User>> GetAllUsersAsync()
        {
            var users = new List<User>();

            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT Id, Username, FullName, Email, Role, IsActive, CreatedAt, LastLogin
                FROM Users
                ORDER BY FullName";

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                users.Add(new User
                {
                    Id = Convert.ToInt32(reader["Id"]),
                    Username = reader["Username"].ToString() ?? "",
                    FullName = reader["FullName"].ToString() ?? "",
                    Email = reader["Email"].ToString() ?? "",
                    Role = Enum.Parse<UserRole>(reader["Role"].ToString() ?? "Cashier"),
                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                    CreatedAt = Convert.ToDateTime(reader["CreatedAt"]),
                    LastLogin = reader["LastLogin"] != DBNull.Value ? Convert.ToDateTime(reader["LastLogin"]) : null
                });
            }

            return users;
        }

        public static async Task<User?> GetUserByIdAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT Id, Username, FullName, Email, Role, IsActive, CreatedAt, LastLogin
                FROM Users
                WHERE Id = @Id";
            command.Parameters.AddWithValue("@Id", id);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                return new User
                {
                    Id = Convert.ToInt32(reader["Id"]),
                    Username = reader["Username"].ToString() ?? "",
                    FullName = reader["FullName"].ToString() ?? "",
                    Email = reader["Email"].ToString() ?? "",
                    Role = Enum.Parse<UserRole>(reader["Role"].ToString() ?? "Cashier"),
                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                    CreatedAt = Convert.ToDateTime(reader["CreatedAt"]),
                    LastLogin = reader["LastLogin"] != DBNull.Value ? Convert.ToDateTime(reader["LastLogin"]) : null
                };
            }

            return null;
        }

        public static async Task<(bool Success, string ErrorMessage)> CreateUserAsync(User user, string password)
        {
            try
            {
                // التحقق من عدم وجود اسم المستخدم مسبقاً
                if (await IsUsernameExistsAsync(user.Username))
                {
                    return (false, "اسم المستخدم موجود مسبقاً. يرجى اختيار اسم مستخدم آخر.");
                }

                // تشفير كلمة المرور
                var hashedPassword = HashPassword(password);

                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    INSERT INTO Users (Username, PasswordHash, FullName, Email, Role, IsActive, CreatedAt)
                    VALUES (@Username, @PasswordHash, @FullName, @Email, @Role, @IsActive, @CreatedAt)";

                command.Parameters.AddWithValue("@Username", user.Username);
                command.Parameters.AddWithValue("@PasswordHash", hashedPassword);
                command.Parameters.AddWithValue("@FullName", user.FullName);
                command.Parameters.AddWithValue("@Email", user.Email ?? "");
                command.Parameters.AddWithValue("@Role", user.Role.ToString());
                command.Parameters.AddWithValue("@IsActive", user.IsActive);
                command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

                var result = await command.ExecuteNonQueryAsync();

                if (result > 0)
                {
                    return (true, "تم إضافة المستخدم بنجاح");
                }
                else
                {
                    return (false, "فشل في إضافة المستخدم إلى قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                return (false, $"خطأ في إضافة المستخدم: {ex.Message}");
            }
        }

        public static async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    UPDATE Users
                    SET FullName = @FullName, Email = @Email, Role = @Role, IsActive = @IsActive
                    WHERE Id = @Id";

                command.Parameters.AddWithValue("@Id", user.Id);
                command.Parameters.AddWithValue("@FullName", user.FullName);
                command.Parameters.AddWithValue("@Email", user.Email ?? "");
                command.Parameters.AddWithValue("@Role", user.Role.ToString());
                command.Parameters.AddWithValue("@IsActive", user.IsActive);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static async Task<bool> ChangePasswordAsync(int userId, string newPassword)
        {
            try
            {
                var hashedPassword = HashPassword(newPassword);

                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    UPDATE Users
                    SET PasswordHash = @PasswordHash
                    WHERE Id = @Id";

                command.Parameters.AddWithValue("@Id", userId);
                command.Parameters.AddWithValue("@PasswordHash", hashedPassword);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                // التحقق من عدم وجود مبيعات للمستخدم
                var salesCommand = connection.CreateCommand();
                salesCommand.CommandText = "SELECT COUNT(*) FROM Sales WHERE UserId = @UserId";
                salesCommand.Parameters.AddWithValue("@UserId", userId);
                var salesCount = Convert.ToInt32(await salesCommand.ExecuteScalarAsync());

                if (salesCount > 0)
                {
                    // إلغاء تفعيل المستخدم بدلاً من الحذف
                    var deactivateCommand = connection.CreateCommand();
                    deactivateCommand.CommandText = "UPDATE Users SET IsActive = 0 WHERE Id = @Id";
                    deactivateCommand.Parameters.AddWithValue("@Id", userId);

                    var result = await deactivateCommand.ExecuteNonQueryAsync();
                    return result > 0;
                }
                else
                {
                    // حذف المستخدم نهائياً
                    var deleteCommand = connection.CreateCommand();
                    deleteCommand.CommandText = "DELETE FROM Users WHERE Id = @Id";
                    deleteCommand.Parameters.AddWithValue("@Id", userId);

                    var result = await deleteCommand.ExecuteNonQueryAsync();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static async Task<bool> IsUsernameExistsAsync(string username)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var command = connection.CreateCommand();
            command.CommandText = "SELECT COUNT(*) FROM Users WHERE LOWER(Username) = LOWER(@Username)";
            command.Parameters.AddWithValue("@Username", username.Trim());

            var count = Convert.ToInt32(await command.ExecuteScalarAsync());
            return count > 0;
        }

        public static async Task<List<User>> SearchUsersAsync(string searchTerm)
        {
            var users = new List<User>();

            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT Id, Username, FullName, Email, Role, IsActive, CreatedAt, LastLogin
                FROM Users
                WHERE (FullName LIKE @SearchTerm OR Username LIKE @SearchTerm OR Email LIKE @SearchTerm)
                ORDER BY FullName";

            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                users.Add(new User
                {
                    Id = Convert.ToInt32(reader["Id"]),
                    Username = reader["Username"].ToString() ?? "",
                    FullName = reader["FullName"].ToString() ?? "",
                    Email = reader["Email"].ToString() ?? "",
                    Role = Enum.Parse<UserRole>(reader["Role"].ToString() ?? "Cashier"),
                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                    CreatedAt = Convert.ToDateTime(reader["CreatedAt"]),
                    LastLogin = reader["LastLogin"] != DBNull.Value ? Convert.ToDateTime(reader["LastLogin"]) : null
                });
            }

            return users;
        }

        private static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
