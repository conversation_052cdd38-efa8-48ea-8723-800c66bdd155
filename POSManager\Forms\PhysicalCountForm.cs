using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class PhysicalCountForm : Form
    {
        private readonly User _currentUser;
        private readonly ProductService _productService;
        private readonly AdvancedInventoryService _inventoryService;
        private List<Product> _products;
        private List<PhysicalCountItem> _countItems;

        // UI Controls
        private DataGridView dgvProducts;
        private TextBox txtSearch, txtNotes;
        private Button btnSearch, btnStartCount, btnSaveCount, btnCancel;
        private Button btnScanBarcode, btnGenerateAdjustment;
        private Label lblTotalProducts, lblCountedProducts, lblVarianceProducts;
        private ProgressBar progressBar;
        private DateTimePicker dtpCountDate;

        public PhysicalCountForm(User currentUser)
        {
            _currentUser = currentUser;
            _productService = ProductService.Instance;
            _inventoryService = AdvancedInventoryService.Instance;
            _products = new List<Product>();
            _countItems = new List<PhysicalCountItem>();
            
            InitializeComponent();
            SetupForm();
            _ = LoadProductsAsync();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1000, 700);
            this.Text = "الجرد الفعلي";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;

            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Header panel
            var headerPanel = new Panel { Height = 80, Dock = DockStyle.Top };
            
            var lblCountDate = new Label { Text = "تاريخ الجرد:", Location = new Point(10, 15), Size = new Size(80, 20) };
            dtpCountDate = new DateTimePicker { Location = new Point(100, 12), Size = new Size(150, 25) };
            
            txtSearch = new TextBox { Location = new Point(10, 45), Size = new Size(200, 25) };
            btnSearch = new Button { Text = "بحث", Location = new Point(220, 44), Size = new Size(80, 27) };
            btnScanBarcode = new Button { Text = "مسح الباركود", Location = new Point(310, 44), Size = new Size(100, 27) };

            headerPanel.Controls.AddRange(new Control[] { lblCountDate, dtpCountDate, txtSearch, btnSearch, btnScanBarcode });

            // Statistics panel
            var statsPanel = new Panel { Height = 50, Dock = DockStyle.Top };
            
            lblTotalProducts = new Label { Text = "إجمالي المنتجات: 0", Location = new Point(10, 15), Size = new Size(150, 20) };
            lblCountedProducts = new Label { Text = "تم جردها: 0", Location = new Point(170, 15), Size = new Size(100, 20) };
            lblVarianceProducts = new Label { Text = "بها تباين: 0", Location = new Point(280, 15), Size = new Size(100, 20) };
            
            progressBar = new ProgressBar { Location = new Point(400, 15), Size = new Size(200, 20) };

            statsPanel.Controls.AddRange(new Control[] { lblTotalProducts, lblCountedProducts, lblVarianceProducts, progressBar });

            // DataGridView
            dgvProducts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RightToLeft = RightToLeft.Yes
            };

            SetupProductsGrid();

            // Footer panel
            var footerPanel = new Panel { Height = 80, Dock = DockStyle.Bottom };
            
            var lblNotes = new Label { Text = "ملاحظات:", Location = new Point(10, 15), Size = new Size(60, 20) };
            txtNotes = new TextBox { Location = new Point(80, 12), Size = new Size(400, 25) };
            
            btnStartCount = new Button 
            { 
                Text = "بدء الجرد", 
                Location = new Point(10, 45), 
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            btnSaveCount = new Button 
            { 
                Text = "حفظ الجرد", 
                Location = new Point(120, 45), 
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            
            btnGenerateAdjustment = new Button 
            { 
                Text = "إنشاء تعديل", 
                Location = new Point(230, 45), 
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            
            btnCancel = new Button 
            { 
                Text = "إغلاق", 
                Location = new Point(340, 45), 
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            footerPanel.Controls.AddRange(new Control[] { 
                lblNotes, txtNotes, btnStartCount, btnSaveCount, btnGenerateAdjustment, btnCancel 
            });

            mainPanel.Controls.Add(dgvProducts);
            mainPanel.Controls.Add(statsPanel);
            mainPanel.Controls.Add(headerPanel);
            mainPanel.Controls.Add(footerPanel);
            
            this.Controls.Add(mainPanel);
        }

        private void SetupForm()
        {
            btnSearch.Click += BtnSearch_Click;
            btnScanBarcode.Click += BtnScanBarcode_Click;
            btnStartCount.Click += BtnStartCount_Click;
            btnSaveCount.Click += BtnSaveCount_Click;
            btnGenerateAdjustment.Click += BtnGenerateAdjustment_Click;
            btnCancel.Click += BtnCancel_Click;
            
            dgvProducts.CellEndEdit += DgvProducts_CellEndEdit;
            dgvProducts.CellValidating += DgvProducts_CellValidating;
        }

        private void SetupProductsGrid()
        {
            dgvProducts.Columns.Clear();
            dgvProducts.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "Code", HeaderText = "الكود", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "اسم المنتج", Width = 200, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "Category", HeaderText = "الفئة", Width = 120, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "SystemStock", HeaderText = "المخزون النظام", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "PhysicalStock", HeaderText = "المخزون الفعلي", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Variance", HeaderText = "التباين", Width = 80, ReadOnly = true },
                new DataGridViewCheckBoxColumn { Name = "IsCounted", HeaderText = "تم الجرد", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", Width = 150 }
            });

            // Set cell styles for variance column
            dgvProducts.Columns["Variance"].DefaultCellStyle.Format = "N2";
            dgvProducts.Columns["SystemStock"].DefaultCellStyle.Format = "N2";
            dgvProducts.Columns["PhysicalStock"].DefaultCellStyle.Format = "N2";
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                _products = await _productService.GetAllProductsAsync();
                
                _countItems = _products.Select(p => new PhysicalCountItem
                {
                    ProductId = p.Id,
                    ProductCode = p.Code,
                    ProductName = p.Name,
                    Category = p.Category?.Name ?? "",
                    SystemStock = p.Stock,
                    PhysicalStock = p.Stock,
                    Variance = 0,
                    IsCounted = false,
                    Notes = ""
                }).ToList();

                RefreshGrid();
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshGrid()
        {
            var displayData = _countItems.Select(item => new
            {
                Id = item.ProductId,
                Code = item.ProductCode,
                Name = item.ProductName,
                Category = item.Category,
                SystemStock = item.SystemStock,
                PhysicalStock = item.PhysicalStock,
                Variance = item.Variance,
                IsCounted = item.IsCounted,
                Notes = item.Notes
            }).ToList();

            dgvProducts.DataSource = displayData;
            
            // Color code rows based on variance
            foreach (DataGridViewRow row in dgvProducts.Rows)
            {
                var variance = Convert.ToDecimal(row.Cells["Variance"].Value);
                if (variance > 0)
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                else if (variance < 0)
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                else
                    row.DefaultCellStyle.BackColor = Color.White;
            }
        }

        private void UpdateStatistics()
        {
            var totalProducts = _countItems.Count;
            var countedProducts = _countItems.Count(i => i.IsCounted);
            var varianceProducts = _countItems.Count(i => i.Variance != 0);
            
            lblTotalProducts.Text = $"إجمالي المنتجات: {totalProducts}";
            lblCountedProducts.Text = $"تم جردها: {countedProducts}";
            lblVarianceProducts.Text = $"بها تباين: {varianceProducts}";
            
            progressBar.Maximum = totalProducts;
            progressBar.Value = countedProducts;
            
            // Enable buttons based on progress
            btnSaveCount.Enabled = countedProducts > 0;
            btnGenerateAdjustment.Enabled = varianceProducts > 0;
        }

        private void BtnSearch_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearch.Text.Trim().ToLower();
            if (string.IsNullOrEmpty(searchTerm))
            {
                RefreshGrid();
                return;
            }

            var filteredItems = _countItems.Where(i => 
                i.ProductCode.ToLower().Contains(searchTerm) ||
                i.ProductName.ToLower().Contains(searchTerm) ||
                i.Category.ToLower().Contains(searchTerm)
            ).ToList();

            var displayData = filteredItems.Select(item => new
            {
                Id = item.ProductId,
                Code = item.ProductCode,
                Name = item.ProductName,
                Category = item.Category,
                SystemStock = item.SystemStock,
                PhysicalStock = item.PhysicalStock,
                Variance = item.Variance,
                IsCounted = item.IsCounted,
                Notes = item.Notes
            }).ToList();

            dgvProducts.DataSource = displayData;
        }

        private void BtnScanBarcode_Click(object sender, EventArgs e)
        {
            var barcodeForm = new BarcodeInputForm();
            if (barcodeForm.ShowDialog() == DialogResult.OK)
            {
                var barcode = barcodeForm.Barcode;
                var item = _countItems.FirstOrDefault(i => i.ProductCode == barcode);
                
                if (item != null)
                {
                    // Find and select the row
                    foreach (DataGridViewRow row in dgvProducts.Rows)
                    {
                        if (row.Cells["Code"].Value?.ToString() == barcode)
                        {
                            dgvProducts.ClearSelection();
                            row.Selected = true;
                            dgvProducts.FirstDisplayedScrollingRowIndex = row.Index;
                            dgvProducts.Focus();
                            break;
                        }
                    }
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على منتج بهذا الباركود", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }

        private void BtnStartCount_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من بدء عملية الجرد؟\nسيتم إعادة تعيين جميع القيم المدخلة مسبقاً",
                "تأكيد بدء الجرد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // Reset all physical stock to system stock
                foreach (var item in _countItems)
                {
                    item.PhysicalStock = item.SystemStock;
                    item.Variance = 0;
                    item.IsCounted = false;
                    item.Notes = "";
                }

                RefreshGrid();
                UpdateStatistics();

                MessageBox.Show("تم بدء عملية الجرد. يمكنك الآن إدخال الكميات الفعلية", "بدء الجرد",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void BtnSaveCount_Click(object sender, EventArgs e)
        {
            try
            {
                var physicalCount = new PhysicalCount
                {
                    CountDate = dtpCountDate.Value,
                    Notes = txtNotes.Text,
                    CreatedBy = _currentUser.Username,
                    CreatedAt = DateTime.Now,
                    Items = _countItems.Where(i => i.IsCounted).ToList()
                };

                await _inventoryService.SavePhysicalCountAsync(physicalCount);
                MessageBox.Show("تم حفظ الجرد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الجرد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnGenerateAdjustment_Click(object sender, EventArgs e)
        {
            var varianceItems = _countItems.Where(i => i.Variance != 0).ToList();

            if (varianceItems.Count == 0)
            {
                MessageBox.Show("لا توجد منتجات بها تباين لإنشاء تعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show($"سيتم إنشاء تعديل مخزون لـ {varianceItems.Count} منتج\nهل تريد المتابعة؟",
                "تأكيد إنشاء التعديل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var adjustment = new StockAdjustment
                    {
                        ReferenceNumber = $"PC-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}",
                        AdjustmentDate = dtpCountDate.Value,
                        Reason = "PhysicalCount",
                        Notes = $"تعديل من الجرد الفعلي - {txtNotes.Text}",
                        Status = "Draft",
                        CreatedBy = _currentUser.Username,
                        CreatedAt = DateTime.Now,
                        Items = varianceItems.Select(v => new StockAdjustmentItem
                        {
                            ProductId = v.ProductId,
                            ProductCode = v.ProductCode,
                            ProductName = v.ProductName,
                            CurrentStock = v.SystemStock,
                            AdjustedStock = v.PhysicalStock,
                            Variance = v.Variance,
                            Reason = "PhysicalCount",
                            Notes = v.Notes
                        }).ToList()
                    };

                    await _inventoryService.CreateStockAdjustmentAsync(adjustment);
                    MessageBox.Show("تم إنشاء تعديل المخزون بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إنشاء التعديل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void DgvProducts_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.RowIndex < _countItems.Count)
            {
                var item = _countItems[e.RowIndex];
                var row = dgvProducts.Rows[e.RowIndex];

                if (e.ColumnIndex == dgvProducts.Columns["PhysicalStock"].Index)
                {
                    if (decimal.TryParse(row.Cells["PhysicalStock"].Value?.ToString(), out decimal physicalStock))
                    {
                        item.PhysicalStock = physicalStock;
                        item.Variance = physicalStock - item.SystemStock;
                        item.IsCounted = true;

                        row.Cells["Variance"].Value = item.Variance;
                        row.Cells["IsCounted"].Value = item.IsCounted;

                        UpdateStatistics();
                        RefreshGrid();
                    }
                }
                else if (e.ColumnIndex == dgvProducts.Columns["Notes"].Index)
                {
                    item.Notes = row.Cells["Notes"].Value?.ToString() ?? "";
                }
                else if (e.ColumnIndex == dgvProducts.Columns["IsCounted"].Index)
                {
                    item.IsCounted = Convert.ToBoolean(row.Cells["IsCounted"].Value);
                    UpdateStatistics();
                }
            }
        }

        private void DgvProducts_CellValidating(object sender, DataGridViewCellValidatingEventArgs e)
        {
            if (e.ColumnIndex == dgvProducts.Columns["PhysicalStock"].Index)
            {
                if (!decimal.TryParse(e.FormattedValue.ToString(), out decimal value) || value < 0)
                {
                    e.Cancel = true;
                    MessageBox.Show("يرجى إدخال رقم صحيح أكبر من أو يساوي الصفر", "خطأ في الإدخال",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }
    }
}
