using POSManager.Services;
using POSManager.Data;

namespace POSManager.Forms
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
            InitializeDatabase();
            SetupForm();
        }
        
        private void InitializeDatabase()
        {
            try
            {
                DatabaseManager.InitializeDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }
        
        private void SetupForm()
        {
            // إعداد النموذج للعربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // تركيز على حقل اسم المستخدم
            txtUsername.Focus();
            
            // إعداد أحداث لوحة المفاتيح
            txtUsername.KeyPress += OnKeyPress;
            txtPassword.KeyPress += OnKeyPress;
            
            // إعداد النص الافتراضي
            lblWelcome.Text = "مرحباً بك في نظام نقطة البيع";
            lblDefaultCredentials.Text = "المستخدم الافتراضي: admin | كلمة المرور: admin123";
        }
        
        private void OnKeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                if (sender == txtUsername)
                {
                    txtPassword.Focus();
                }
                else if (sender == txtPassword)
                {
                    btnLogin.PerformClick();
                }
            }
        }
        
        private async void btnLogin_Click(object sender, EventArgs e)
        {
            await PerformLoginAsync();
        }
        
        private async Task PerformLoginAsync()
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return;
            }
            
            // تعطيل الأزرار أثناء المعالجة
            SetControlsEnabled(false);
            
            try
            {
                var result = await AuthService.LoginAsync(txtUsername.Text.Trim(), txtPassword.Text);
                
                if (result.Success)
                {
                    // إخفاء نموذج تسجيل الدخول
                    this.Hide();
                    
                    // فتح النموذج الرئيسي
                    var mainForm = new MainForm();
                    mainForm.FormClosed += (s, e) => this.Close();
                    mainForm.Show();
                }
                else
                {
                    MessageBox.Show(result.Message, "خطأ في تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    // مسح كلمة المرور وإعادة التركيز
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ غير متوقع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }
        
        private void SetControlsEnabled(bool enabled)
        {
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            btnLogin.Enabled = enabled;
            btnExit.Enabled = enabled;
            
            // تغيير نص الزر أثناء المعالجة
            btnLogin.Text = enabled ? "تسجيل الدخول" : "جاري التحقق...";
            
            // تغيير المؤشر
            this.Cursor = enabled ? Cursors.Default : Cursors.WaitCursor;
        }
        
        private void btnExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
        
        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                if (MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
        
        private void chkShowPassword_CheckedChanged(object sender, EventArgs e)
        {
            txtPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
        }
    }
}
