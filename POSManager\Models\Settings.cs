namespace POSManager.Models
{
    #region System Settings Models

    /// <summary>
    /// إعدادات النظام العامة
    /// </summary>
    public class SystemSettings
    {
        public int Id { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string CompanyAddress { get; set; } = string.Empty;
        public string CompanyPhone { get; set; } = string.Empty;
        public string CompanyEmail { get; set; } = string.Empty;
        public string TaxNumber { get; set; } = string.Empty;
        public decimal TaxRate { get; set; } = 0.15m; // 15% default VAT
        public string Currency { get; set; } = "ريال";
        public string CurrencySymbol { get; set; } = "ر.س";
        public bool IsRTL { get; set; } = true;
        public string Language { get; set; } = "ar-SA";
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// إعدادات الطباعة
    /// </summary>
    public class PrintSettings
    {
        public int Id { get; set; }
        public string DefaultPrinterName { get; set; } = string.Empty;
        public PrinterType PrinterType { get; set; } = PrinterType.Thermal;
        public SettingsPaperSize PaperSize { get; set; } = SettingsPaperSize.Thermal80mm;
        public bool AutoPrint { get; set; } = false;
        public bool PrintLogo { get; set; } = true;
        public bool PrintHeader { get; set; } = true;
        public bool PrintFooter { get; set; } = true;
        public string HeaderText { get; set; } = string.Empty;
        public string FooterText { get; set; } = "شكراً لزيارتكم";
        public int CopiesCount { get; set; } = 1;
        public bool PrintBarcode { get; set; } = false;
        public string LogoPath { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// إعدادات الأمان
    /// </summary>
    public class SecuritySettings
    {
        public int Id { get; set; }
        public bool RequireStrongPassword { get; set; } = true;
        public int MinPasswordLength { get; set; } = 8;
        public int MaxLoginAttempts { get; set; } = 3;
        public int LockoutDurationMinutes { get; set; } = 15;
        public bool EnableActivityLog { get; set; } = true;
        public bool RequirePasswordChange { get; set; } = false;
        public int PasswordExpiryDays { get; set; } = 90;
        public bool EnableTwoFactorAuth { get; set; } = false;
        public bool AutoLogoutEnabled { get; set; } = true;
        public int AutoLogoutMinutes { get; set; } = 30;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// إعدادات النسخ الاحتياطي
    /// </summary>
    public class BackupSettings
    {
        public int Id { get; set; }
        public bool AutoBackupEnabled { get; set; } = true;
        public BackupFrequency BackupFrequency { get; set; } = BackupFrequency.Daily;
        public string BackupPath { get; set; } = string.Empty;
        public int RetentionDays { get; set; } = 30;
        public bool CompressBackup { get; set; } = true;
        public bool BackupOnExit { get; set; } = true;
        public DateTime LastBackupDate { get; set; }
        public string LastBackupFile { get; set; } = string.Empty;
        public bool EmailBackup { get; set; } = false;
        public string BackupEmail { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// إعدادات الواجهة
    /// </summary>
    public class UISettings
    {
        public int Id { get; set; }
        public string Theme { get; set; } = "Default";
        public string PrimaryColor { get; set; } = "#2196F3";
        public string SecondaryColor { get; set; } = "#FFC107";
        public int FontSize { get; set; } = 12;
        public string FontFamily { get; set; } = "Tahoma";
        public bool ShowWelcomeScreen { get; set; } = true;
        public bool ShowTips { get; set; } = true;
        public bool EnableSounds { get; set; } = true;
        public bool EnableAnimations { get; set; } = true;
        public bool ShowStatusBar { get; set; } = true;
        public bool ShowToolbar { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    #endregion

    #region Enums

    /// <summary>
    /// أنواع الطابعات
    /// </summary>
    public enum PrinterType
    {
        Thermal = 1,    // طابعة حرارية
        Inkjet = 2,     // طابعة نفث حبر
        Laser = 3,      // طابعة ليزر
        DotMatrix = 4   // طابعة نقطية
    }

    /// <summary>
    /// أحجام الورق للإعدادات
    /// </summary>
    public enum SettingsPaperSize
    {
        Thermal80mm = 1,    // 80 مم حراري
        Thermal58mm = 2,    // 58 مم حراري
        A4 = 3,             // A4
        A5 = 4,             // A5
        Custom = 5          // مخصص
    }

    /// <summary>
    /// تكرار النسخ الاحتياطي
    /// </summary>
    public enum BackupFrequency
    {
        Hourly = 1,     // كل ساعة
        Daily = 2,      // يومياً
        Weekly = 3,     // أسبوعياً
        Monthly = 4,    // شهرياً
        Manual = 5      // يدوي
    }

    #endregion

    #region Settings Container

    /// <summary>
    /// حاوي جميع الإعدادات
    /// </summary>
    public class ApplicationSettings
    {
        public SystemSettings System { get; set; } = new SystemSettings();
        public PrintSettings Print { get; set; } = new PrintSettings();
        public SecuritySettings Security { get; set; } = new SecuritySettings();
        public BackupSettings Backup { get; set; } = new BackupSettings();
        public UISettings UI { get; set; } = new UISettings();
        
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public string UpdatedBy { get; set; } = string.Empty;
    }

    #endregion
}
