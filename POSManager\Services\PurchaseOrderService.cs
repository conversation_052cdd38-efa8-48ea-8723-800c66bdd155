using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using POSManager.Models;
using POSManager.Data;

namespace POSManager.Services
{
    public class PurchaseOrderService
    {
        private readonly DatabaseManager _dbManager;
        private readonly AdvancedInventoryService _inventoryService;
        private readonly SupplierService _supplierService;
        private static PurchaseOrderService? _instance;
        private static readonly object _lock = new object();

        private PurchaseOrderService()
        {
            _dbManager = DatabaseManager.Instance;
            _inventoryService = AdvancedInventoryService.Instance;
            _supplierService = SupplierService.Instance;
        }

        public static PurchaseOrderService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new PurchaseOrderService();
                    }
                }
                return _instance;
            }
        }

        #region Purchase Order CRUD Operations

        // إنشاء أمر شراء جديد
        public async Task<int> CreatePurchaseOrderAsync(PurchaseOrder purchaseOrder)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();
                using var transaction = connection.BeginTransaction();

                try
                {
                    // إدراج أمر الشراء
                    var orderSql = @"
                        INSERT INTO PurchaseOrders 
                        (OrderNumber, SupplierId, OrderDate, ExpectedDeliveryDate, Status, 
                         Subtotal, TaxAmount, DiscountAmount, ShippingCost, Total, Notes, CreatedBy, CreatedAt)
                        VALUES 
                        (@OrderNumber, @SupplierId, @OrderDate, @ExpectedDeliveryDate, @Status,
                         @Subtotal, @TaxAmount, @DiscountAmount, @ShippingCost, @Total, @Notes, @CreatedBy, @CreatedAt);
                        SELECT last_insert_rowid();";

                    using var orderCommand = new SqliteCommand(orderSql, connection, transaction);
                    orderCommand.Parameters.AddWithValue("@OrderNumber", purchaseOrder.OrderNumber);
                    orderCommand.Parameters.AddWithValue("@SupplierId", purchaseOrder.SupplierId);
                    orderCommand.Parameters.AddWithValue("@OrderDate", purchaseOrder.OrderDate);
                    orderCommand.Parameters.AddWithValue("@ExpectedDeliveryDate", purchaseOrder.ExpectedDeliveryDate ?? (object)DBNull.Value);
                    orderCommand.Parameters.AddWithValue("@Status", (int)purchaseOrder.Status);
                    orderCommand.Parameters.AddWithValue("@Subtotal", purchaseOrder.Subtotal);
                    orderCommand.Parameters.AddWithValue("@TaxAmount", purchaseOrder.TaxAmount);
                    orderCommand.Parameters.AddWithValue("@DiscountAmount", purchaseOrder.DiscountAmount);
                    orderCommand.Parameters.AddWithValue("@ShippingCost", purchaseOrder.ShippingCost);
                    orderCommand.Parameters.AddWithValue("@Total", purchaseOrder.Total);
                    orderCommand.Parameters.AddWithValue("@Notes", purchaseOrder.Notes ?? (object)DBNull.Value);
                    orderCommand.Parameters.AddWithValue("@CreatedBy", purchaseOrder.CreatedBy);
                    orderCommand.Parameters.AddWithValue("@CreatedAt", purchaseOrder.CreatedAt);

                    var orderId = Convert.ToInt32(await orderCommand.ExecuteScalarAsync());

                    // إدراج عناصر أمر الشراء
                    foreach (var item in purchaseOrder.Items)
                    {
                        var itemSql = @"
                            INSERT INTO PurchaseOrderItems 
                            (PurchaseOrderId, ProductId, Quantity, UnitCost, DiscountAmount, Total, Notes)
                            VALUES 
                            (@PurchaseOrderId, @ProductId, @Quantity, @UnitCost, @DiscountAmount, @Total, @Notes)";

                        using var itemCommand = new SqliteCommand(itemSql, connection, transaction);
                        itemCommand.Parameters.AddWithValue("@PurchaseOrderId", orderId);
                        itemCommand.Parameters.AddWithValue("@ProductId", item.ProductId);
                        itemCommand.Parameters.AddWithValue("@Quantity", item.Quantity);
                        itemCommand.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                        itemCommand.Parameters.AddWithValue("@DiscountAmount", item.DiscountAmount);
                        itemCommand.Parameters.AddWithValue("@Total", item.Total);
                        itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? (object)DBNull.Value);

                        await itemCommand.ExecuteNonQueryAsync();
                    }

                    await transaction.CommitAsync();
                    return orderId;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return 0;
            }
        }

        // تحديث أمر شراء
        public async Task<bool> UpdatePurchaseOrderAsync(PurchaseOrder purchaseOrder)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();
                using var transaction = connection.BeginTransaction();

                try
                {
                    // تحديث أمر الشراء
                    var orderSql = @"
                        UPDATE PurchaseOrders SET
                            SupplierId = @SupplierId,
                            OrderDate = @OrderDate,
                            ExpectedDeliveryDate = @ExpectedDeliveryDate,
                            Status = @Status,
                            Subtotal = @Subtotal,
                            TaxAmount = @TaxAmount,
                            DiscountAmount = @DiscountAmount,
                            ShippingCost = @ShippingCost,
                            Total = @Total,
                            Notes = @Notes,
                            UpdatedAt = @UpdatedAt
                        WHERE Id = @Id";

                    using var orderCommand = new SqliteCommand(orderSql, connection, transaction);
                    orderCommand.Parameters.AddWithValue("@Id", purchaseOrder.Id);
                    orderCommand.Parameters.AddWithValue("@SupplierId", purchaseOrder.SupplierId);
                    orderCommand.Parameters.AddWithValue("@OrderDate", purchaseOrder.OrderDate);
                    orderCommand.Parameters.AddWithValue("@ExpectedDeliveryDate", purchaseOrder.ExpectedDeliveryDate ?? (object)DBNull.Value);
                    orderCommand.Parameters.AddWithValue("@Status", (int)purchaseOrder.Status);
                    orderCommand.Parameters.AddWithValue("@Subtotal", purchaseOrder.Subtotal);
                    orderCommand.Parameters.AddWithValue("@TaxAmount", purchaseOrder.TaxAmount);
                    orderCommand.Parameters.AddWithValue("@DiscountAmount", purchaseOrder.DiscountAmount);
                    orderCommand.Parameters.AddWithValue("@ShippingCost", purchaseOrder.ShippingCost);
                    orderCommand.Parameters.AddWithValue("@Total", purchaseOrder.Total);
                    orderCommand.Parameters.AddWithValue("@Notes", purchaseOrder.Notes ?? (object)DBNull.Value);
                    orderCommand.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                    await orderCommand.ExecuteNonQueryAsync();

                    // حذف العناصر القديمة
                    var deleteItemsSql = "DELETE FROM PurchaseOrderItems WHERE PurchaseOrderId = @PurchaseOrderId";
                    using var deleteCommand = new SqliteCommand(deleteItemsSql, connection, transaction);
                    deleteCommand.Parameters.AddWithValue("@PurchaseOrderId", purchaseOrder.Id);
                    await deleteCommand.ExecuteNonQueryAsync();

                    // إدراج العناصر الجديدة
                    foreach (var item in purchaseOrder.Items)
                    {
                        var itemSql = @"
                            INSERT INTO PurchaseOrderItems 
                            (PurchaseOrderId, ProductId, Quantity, UnitCost, DiscountAmount, Total, Notes)
                            VALUES 
                            (@PurchaseOrderId, @ProductId, @Quantity, @UnitCost, @DiscountAmount, @Total, @Notes)";

                        using var itemCommand = new SqliteCommand(itemSql, connection, transaction);
                        itemCommand.Parameters.AddWithValue("@PurchaseOrderId", purchaseOrder.Id);
                        itemCommand.Parameters.AddWithValue("@ProductId", item.ProductId);
                        itemCommand.Parameters.AddWithValue("@Quantity", item.Quantity);
                        itemCommand.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                        itemCommand.Parameters.AddWithValue("@DiscountAmount", item.DiscountAmount);
                        itemCommand.Parameters.AddWithValue("@Total", item.Total);
                        itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? (object)DBNull.Value);

                        await itemCommand.ExecuteNonQueryAsync();
                    }

                    await transaction.CommitAsync();
                    return true;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // الحصول على أمر شراء بالمعرف
        public async Task<PurchaseOrder?> GetPurchaseOrderByIdAsync(int orderId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                // جلب أمر الشراء
                var orderSql = @"
                    SELECT po.*, s.Name as SupplierName, u.FullName as CreatedByName
                    FROM PurchaseOrders po
                    LEFT JOIN AdvancedSuppliers s ON po.SupplierId = s.Id
                    LEFT JOIN Users u ON po.CreatedBy = u.Id
                    WHERE po.Id = @Id";

                using var orderCommand = new SqliteCommand(orderSql, connection);
                orderCommand.Parameters.AddWithValue("@Id", orderId);

                using var orderReader = await orderCommand.ExecuteReaderAsync();
                if (await orderReader.ReadAsync())
                {
                    var purchaseOrder = MapPurchaseOrderFromReader(orderReader);
                    orderReader.Close();

                    // جلب عناصر أمر الشراء
                    var itemsSql = @"
                        SELECT poi.*, p.Name as ProductName, p.Code as ProductCode
                        FROM PurchaseOrderItems poi
                        LEFT JOIN Products p ON poi.ProductId = p.Id
                        WHERE poi.PurchaseOrderId = @PurchaseOrderId";

                    using var itemsCommand = new SqliteCommand(itemsSql, connection);
                    itemsCommand.Parameters.AddWithValue("@PurchaseOrderId", orderId);

                    using var itemsReader = await itemsCommand.ExecuteReaderAsync();
                    while (await itemsReader.ReadAsync())
                    {
                        purchaseOrder.Items.Add(MapPurchaseOrderItemFromReader(itemsReader));
                    }

                    return purchaseOrder;
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return null;
        }

        // الحصول على قائمة أوامر الشراء
        public async Task<List<PurchaseOrder>> GetPurchaseOrdersAsync(PurchaseOrderStatus? status = null, int? supplierId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var orders = new List<PurchaseOrder>();

            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT po.*, s.Name as SupplierName, u.FullName as CreatedByName
                    FROM PurchaseOrders po
                    LEFT JOIN AdvancedSuppliers s ON po.SupplierId = s.Id
                    LEFT JOIN Users u ON po.CreatedBy = u.Id
                    WHERE 1=1";

                var parameters = new List<SqliteParameter>();

                if (status.HasValue)
                {
                    sql += " AND po.Status = @Status";
                    parameters.Add(new SqliteParameter("@Status", (int)status.Value));
                }

                if (supplierId.HasValue)
                {
                    sql += " AND po.SupplierId = @SupplierId";
                    parameters.Add(new SqliteParameter("@SupplierId", supplierId.Value));
                }

                if (fromDate.HasValue)
                {
                    sql += " AND po.OrderDate >= @FromDate";
                    parameters.Add(new SqliteParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    sql += " AND po.OrderDate <= @ToDate";
                    parameters.Add(new SqliteParameter("@ToDate", toDate.Value));
                }

                sql += " ORDER BY po.OrderDate DESC";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddRange(parameters.ToArray());

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    orders.Add(MapPurchaseOrderFromReader(reader));
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return orders;
        }

        #endregion

        #region Purchase Order Status Management

        // اعتماد أمر شراء
        public async Task<bool> ApprovePurchaseOrderAsync(int orderId, int approvedBy)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    UPDATE PurchaseOrders SET
                        Status = @Status,
                        ApprovedBy = @ApprovedBy,
                        ApprovedAt = @ApprovedAt
                    WHERE Id = @Id AND Status = @DraftStatus";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", orderId);
                command.Parameters.AddWithValue("@Status", (int)PurchaseOrderStatus.Approved);
                command.Parameters.AddWithValue("@ApprovedBy", approvedBy);
                command.Parameters.AddWithValue("@ApprovedAt", DateTime.Now);
                command.Parameters.AddWithValue("@DraftStatus", (int)PurchaseOrderStatus.Draft);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // إرسال أمر شراء
        public async Task<bool> SendPurchaseOrderAsync(int orderId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    UPDATE PurchaseOrders SET
                        Status = @Status
                    WHERE Id = @Id AND Status = @ApprovedStatus";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", orderId);
                command.Parameters.AddWithValue("@Status", (int)PurchaseOrderStatus.Sent);
                command.Parameters.AddWithValue("@ApprovedStatus", (int)PurchaseOrderStatus.Approved);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // إلغاء أمر شراء
        public async Task<bool> CancelPurchaseOrderAsync(int orderId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    UPDATE PurchaseOrders SET
                        Status = @Status
                    WHERE Id = @Id AND Status IN (@DraftStatus, @ApprovedStatus, @SentStatus)";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", orderId);
                command.Parameters.AddWithValue("@Status", (int)PurchaseOrderStatus.Cancelled);
                command.Parameters.AddWithValue("@DraftStatus", (int)PurchaseOrderStatus.Draft);
                command.Parameters.AddWithValue("@ApprovedStatus", (int)PurchaseOrderStatus.Approved);
                command.Parameters.AddWithValue("@SentStatus", (int)PurchaseOrderStatus.Sent);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        #endregion

        #region Helper Methods

        // تحويل البيانات من قارئ قاعدة البيانات إلى كائن أمر الشراء
        private PurchaseOrder MapPurchaseOrderFromReader(SqliteDataReader reader)
        {
            return new PurchaseOrder
            {
                Id = Convert.ToInt32(reader[0]),
                OrderNumber = Convert.ToString(reader[1]) ?? "",
                SupplierId = Convert.ToInt32(reader[2]),
                OrderDate = Convert.ToDateTime(reader[3]),
                ExpectedDeliveryDate = reader[4] != DBNull.Value ? Convert.ToDateTime(reader[4]) : null,
                ActualDeliveryDate = reader[5] != DBNull.Value ? Convert.ToDateTime(reader[5]) : null,
                Status = (PurchaseOrderStatus)Convert.ToInt32(reader[6]),
                Subtotal = Convert.ToDecimal(reader[7]),
                TaxAmount = Convert.ToDecimal(reader[8]),
                DiscountAmount = Convert.ToDecimal(reader[9]),
                ShippingCost = Convert.ToDecimal(reader[10]),
                Total = Convert.ToDecimal(reader[11]),
                Notes = Convert.ToString(reader[12]),
                CreatedBy = Convert.ToInt32(reader[13]),
                ApprovedBy = reader[14] != DBNull.Value ? Convert.ToInt32(reader[14]) : null,
                ApprovedAt = reader[15] != DBNull.Value ? Convert.ToDateTime(reader[15]) : null,
                CreatedAt = Convert.ToDateTime(reader[16]),
                UpdatedAt = reader[17] != DBNull.Value ? Convert.ToDateTime(reader[17]) : null,
                SupplierName = Convert.ToString(reader[18]) ?? "",
                CreatedByName = Convert.ToString(reader[19]) ?? ""
            };
        }

        // تحويل البيانات من قارئ قاعدة البيانات إلى كائن عنصر أمر الشراء
        private PurchaseOrderItem MapPurchaseOrderItemFromReader(SqliteDataReader reader)
        {
            return new PurchaseOrderItem
            {
                Id = Convert.ToInt32(reader[0]),
                PurchaseOrderId = Convert.ToInt32(reader[1]),
                ProductId = Convert.ToInt32(reader[2]),
                Quantity = Convert.ToDecimal(reader[3]),
                UnitCost = Convert.ToDecimal(reader[4]),
                DiscountAmount = Convert.ToDecimal(reader[5]),
                Total = Convert.ToDecimal(reader[6]),
                ReceivedQuantity = Convert.ToDecimal(reader[7]),
                Notes = Convert.ToString(reader[8]),
                ProductName = Convert.ToString(reader[9]) ?? "",
                ProductCode = Convert.ToString(reader[10]) ?? ""
            };
        }

        // توليد رقم أمر شراء جديد
        public async Task<string> GenerateOrderNumberAsync()
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT COUNT(*) FROM PurchaseOrders WHERE DATE(CreatedAt) = DATE('now')";
                using var command = new SqliteCommand(sql, connection);
                var todayCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                return $"PO-{DateTime.Now:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch
            {
                return $"PO-{DateTime.Now:yyyyMMdd}-0001";
            }
        }

        // توليد رقم استلام جديد
        public async Task<string> GenerateReceiptNumberAsync()
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT COUNT(*) FROM PurchaseOrderReceipts WHERE DATE(CreatedAt) = DATE('now')";
                using var command = new SqliteCommand(sql, connection);
                var todayCount = Convert.ToInt32(await command.ExecuteScalarAsync());

                return $"RC-{DateTime.Now:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch
            {
                return $"RC-{DateTime.Now:yyyyMMdd}-0001";
            }
        }

        // التحقق من صحة أمر الشراء
        public bool ValidatePurchaseOrder(PurchaseOrder purchaseOrder, out string errorMessage)
        {
            errorMessage = "";

            if (string.IsNullOrWhiteSpace(purchaseOrder.OrderNumber))
            {
                errorMessage = "رقم أمر الشراء مطلوب";
                return false;
            }

            if (purchaseOrder.SupplierId <= 0)
            {
                errorMessage = "يجب اختيار مورد";
                return false;
            }

            if (purchaseOrder.Items == null || purchaseOrder.Items.Count == 0)
            {
                errorMessage = "يجب إضافة عنصر واحد على الأقل";
                return false;
            }

            foreach (var item in purchaseOrder.Items)
            {
                if (item.Quantity <= 0)
                {
                    errorMessage = "كمية العنصر يجب أن تكون أكبر من صفر";
                    return false;
                }

                if (item.UnitCost < 0)
                {
                    errorMessage = "تكلفة العنصر لا يمكن أن تكون سالبة";
                    return false;
                }
            }

            return true;
        }

        // إنشاء استلام أمر شراء
        public async Task<int> CreateReceiptAsync(PurchaseOrderReceipt receipt)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // إدراج الاستلام
                var receiptSql = @"INSERT INTO PurchaseOrderReceipts
                    (PurchaseOrderId, ReceiptNumber, ReceiptDate, ReceivedBy, Notes, CreatedAt)
                    VALUES (@PurchaseOrderId, @ReceiptNumber, @ReceiptDate, @ReceivedBy, @Notes, @CreatedAt);
                    SELECT last_insert_rowid();";

                using var receiptCommand = new SqliteCommand(receiptSql, connection, transaction);
                receiptCommand.Parameters.AddWithValue("@PurchaseOrderId", receipt.PurchaseOrderId);
                receiptCommand.Parameters.AddWithValue("@ReceiptNumber", receipt.ReceiptNumber);
                receiptCommand.Parameters.AddWithValue("@ReceiptDate", receipt.ReceiptDate);
                receiptCommand.Parameters.AddWithValue("@ReceivedBy", receipt.ReceivedBy);
                receiptCommand.Parameters.AddWithValue("@Notes", receipt.Notes ?? "");
                receiptCommand.Parameters.AddWithValue("@CreatedAt", receipt.CreatedAt);

                var receiptId = Convert.ToInt32(await receiptCommand.ExecuteScalarAsync());

                // إدراج عناصر الاستلام وتحديث المخزون
                foreach (var item in receipt.Items)
                {
                    // إدراج عنصر الاستلام
                    var itemSql = @"INSERT INTO PurchaseOrderReceiptItems
                        (ReceiptId, ProductId, ReceivedQuantity, UnitCost, Notes)
                        VALUES (@ReceiptId, @ProductId, @ReceivedQuantity, @UnitCost, @Notes)";

                    using var itemCommand = new SqliteCommand(itemSql, connection, transaction);
                    itemCommand.Parameters.AddWithValue("@ReceiptId", receiptId);
                    itemCommand.Parameters.AddWithValue("@ProductId", item.ProductId);
                    itemCommand.Parameters.AddWithValue("@ReceivedQuantity", item.ReceivedQuantity);
                    itemCommand.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                    itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? "");

                    await itemCommand.ExecuteNonQueryAsync();

                    // تحديث الكمية المستلمة في أمر الشراء
                    var updateOrderItemSql = @"UPDATE PurchaseOrderItems
                        SET ReceivedQuantity = ReceivedQuantity + @ReceivedQuantity
                        WHERE PurchaseOrderId = @PurchaseOrderId AND ProductId = @ProductId";

                    using var updateCommand = new SqliteCommand(updateOrderItemSql, connection, transaction);
                    updateCommand.Parameters.AddWithValue("@ReceivedQuantity", item.ReceivedQuantity);
                    updateCommand.Parameters.AddWithValue("@PurchaseOrderId", receipt.PurchaseOrderId);
                    updateCommand.Parameters.AddWithValue("@ProductId", item.ProductId);

                    await updateCommand.ExecuteNonQueryAsync();

                    // تحديث المخزون
                    await UpdateInventoryAsync(connection, transaction, item.ProductId, item.ReceivedQuantity, receipt.ReceivedBy);
                }

                // تحديث حالة أمر الشراء
                await UpdatePurchaseOrderStatusAfterReceiptAsync(connection, transaction, receipt.PurchaseOrderId);

                transaction.Commit();
                return receiptId;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        // تحديث المخزون عند الاستلام
        private async Task UpdateInventoryAsync(SqliteConnection connection, SqliteTransaction transaction, int productId, decimal quantity, int userId)
        {
            // تحديث كمية المنتج
            var updateProductSql = @"UPDATE Products
                SET Quantity = Quantity + @Quantity,
                    LastUpdated = @LastUpdated
                WHERE Id = @ProductId";

            using var updateCommand = new SqliteCommand(updateProductSql, connection, transaction);
            updateCommand.Parameters.AddWithValue("@Quantity", quantity);
            updateCommand.Parameters.AddWithValue("@LastUpdated", DateTime.Now);
            updateCommand.Parameters.AddWithValue("@ProductId", productId);

            await updateCommand.ExecuteNonQueryAsync();

            // إضافة حركة مخزون
            var movementSql = @"INSERT INTO InventoryMovements
                (ProductId, MovementType, Quantity, Notes, CreatedBy, CreatedAt)
                VALUES (@ProductId, @MovementType, @Quantity, @Notes, @CreatedBy, @CreatedAt)";

            using var movementCommand = new SqliteCommand(movementSql, connection, transaction);
            movementCommand.Parameters.AddWithValue("@ProductId", productId);
            movementCommand.Parameters.AddWithValue("@MovementType", (int)InventoryMovementType.Purchase);
            movementCommand.Parameters.AddWithValue("@Quantity", quantity);
            movementCommand.Parameters.AddWithValue("@Notes", "استلام أمر شراء");
            movementCommand.Parameters.AddWithValue("@CreatedBy", userId);
            movementCommand.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

            await movementCommand.ExecuteNonQueryAsync();
        }

        // تحديث حالة أمر الشراء بعد الاستلام
        private async Task UpdatePurchaseOrderStatusAfterReceiptAsync(SqliteConnection connection, SqliteTransaction transaction, int purchaseOrderId)
        {
            // التحقق من حالة الاستلام
            var checkSql = @"SELECT
                SUM(poi.Quantity) as TotalOrdered,
                SUM(poi.ReceivedQuantity) as TotalReceived
                FROM PurchaseOrderItems poi
                WHERE poi.PurchaseOrderId = @PurchaseOrderId";

            using var checkCommand = new SqliteCommand(checkSql, connection, transaction);
            checkCommand.Parameters.AddWithValue("@PurchaseOrderId", purchaseOrderId);

            using var reader = await checkCommand.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var totalOrdered = Convert.ToDecimal(reader[0]);
                var totalReceived = Convert.ToDecimal(reader[1]);

                PurchaseOrderStatus newStatus;
                if (totalReceived >= totalOrdered)
                {
                    newStatus = PurchaseOrderStatus.Received;
                }
                else if (totalReceived > 0)
                {
                    newStatus = PurchaseOrderStatus.PartiallyReceived;
                }
                else
                {
                    return; // لا تغيير في الحالة
                }

                reader.Close();

                // تحديث حالة أمر الشراء
                var updateStatusSql = @"UPDATE PurchaseOrders
                    SET Status = @Status
                    WHERE Id = @Id";

                using var updateCommand = new SqliteCommand(updateStatusSql, connection, transaction);
                updateCommand.Parameters.AddWithValue("@Status", (int)newStatus);
                updateCommand.Parameters.AddWithValue("@Id", purchaseOrderId);

                await updateCommand.ExecuteNonQueryAsync();
            }
        }

        #endregion
    }
}
