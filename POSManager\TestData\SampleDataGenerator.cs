using POSManager.Models;
using POSManager.Services;
using System;
using System.Threading.Tasks;

namespace POSManager.TestData
{
    public static class SampleDataGenerator
    {
        public static async Task GenerateTestDataAsync()
        {
            try
            {
                Console.WriteLine("🔄 بدء إنشاء البيانات التجريبية...");

                // إنشاء الفئات
                await CreateCategoriesAsync();
                
                // إنشاء المنتجات
                await CreateProductsAsync();
                
                // إنشاء الموردين
                await CreateSuppliersAsync();
                
                // إنشاء بيانات المخزون
                await CreateInventoryDataAsync();

                Console.WriteLine("✅ تم إنشاء البيانات التجريبية بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء البيانات التجريبية: {ex.Message}");
            }
        }

        private static async Task CreateCategoriesAsync()
        {
            Console.WriteLine("📁 إنشاء الفئات...");
            
            var categories = new[]
            {
                new Category { Name = "مشروبات", Description = "جميع أنواع المشروبات", IsActive = true },
                new Category { Name = "وجبات خفيفة", Description = "الوجبات الخفيفة والحلويات", IsActive = true },
                new Category { Name = "منتجات ألبان", Description = "الحليب والجبن واللبن", IsActive = true },
                new Category { Name = "خضروات وفواكه", Description = "الخضروات والفواكه الطازجة", IsActive = true },
                new Category { Name = "مواد تنظيف", Description = "مواد التنظيف والعناية", IsActive = true }
            };

            foreach (var category in categories)
            {
                await CategoryService.AddCategoryAsync(category);
                Console.WriteLine($"  ✓ تم إضافة فئة: {category.Name}");
            }
        }

        private static async Task CreateProductsAsync()
        {
            Console.WriteLine("📦 إنشاء المنتجات...");
            
            var products = new[]
            {
                new Product 
                { 
                    Name = "كوكا كولا 330مل", 
                    Barcode = "1234567890123", 
                    CategoryId = 1, 
                    PurchasePrice = 1.5m, 
                    SalePrice = 2.5m, 
                    Stock = 100, 
                    MinStock = 10, 

                    IsActive = true 
                },
                new Product 
                { 
                    Name = "شيبس ليز", 
                    Barcode = "2345678901234", 
                    CategoryId = 2, 
                    PurchasePrice = 2.0m, 
                    SalePrice = 3.5m, 
                    Stock = 75, 
                    MinStock = 15, 

                    IsActive = true 
                },
                new Product 
                { 
                    Name = "حليب نادك 1 لتر", 
                    Barcode = "3456789012345", 
                    CategoryId = 3, 
                    PurchasePrice = 3.0m, 
                    SalePrice = 4.5m, 
                    Stock = 50, 
                    MinStock = 5, 

                    IsActive = true 
                },
                new Product 
                { 
                    Name = "تفاح أحمر كيلو", 
                    Barcode = "4567890123456", 
                    CategoryId = 4, 
                    PurchasePrice = 4.0m, 
                    SalePrice = 6.0m, 
                    Stock = 25, 
                    MinStock = 5, 

                    IsActive = true 
                },
                new Product 
                { 
                    Name = "صابون تايد", 
                    Barcode = "5678901234567", 
                    CategoryId = 5, 
                    PurchasePrice = 8.0m, 
                    SalePrice = 12.0m, 
                    Stock = 30, 
                    MinStock = 3, 

                    IsActive = true 
                }
            };

            foreach (var product in products)
            {
                await ProductService.AddProductAsync(product);
                Console.WriteLine($"  ✓ تم إضافة منتج: {product.Name}");
            }
        }

        private static async Task CreateSuppliersAsync()
        {
            Console.WriteLine("🏢 إنشاء الموردين...");
            
            var suppliers = new[]
            {
                new Supplier 
                { 
                    Name = "شركة المشروبات الوطنية", 
                    Code = "SUP001",
                    ContactPerson = "أحمد محمد", 
                    Phone = "0501234567", 
                    Email = "<EMAIL>",
                    Address = "الرياض، المملكة العربية السعودية",
                    City = "الرياض",
                    Country = "السعودية",
                    IsActive = true,

                    CreditLimit = 50000
                },
                new Supplier 
                { 
                    Name = "مؤسسة الأغذية المتميزة", 
                    Code = "SUP002",
                    ContactPerson = "فاطمة علي", 
                    Phone = "0507654321", 
                    Email = "<EMAIL>",
                    Address = "جدة، المملكة العربية السعودية",
                    City = "جدة",
                    Country = "السعودية",
                    IsActive = true,

                    CreditLimit = 30000
                },
                new Supplier 
                { 
                    Name = "شركة منتجات الألبان", 
                    Code = "SUP003",
                    ContactPerson = "محمد سالم", 
                    Phone = "0509876543", 
                    Email = "<EMAIL>",
                    Address = "الدمام، المملكة العربية السعودية",
                    City = "الدمام",
                    Country = "السعودية",
                    IsActive = true,

                    CreditLimit = 25000
                }
            };

            foreach (var supplier in suppliers)
            {
                await SupplierService.Instance.AddSupplierAsync(supplier);
                Console.WriteLine($"  ✓ تم إضافة مورد: {supplier.Name}");
            }
        }

        private static async Task CreateInventoryDataAsync()
        {
            Console.WriteLine("📊 إنشاء بيانات المخزون...");

            try
            {
                Console.WriteLine("  ✓ تم تحديث إعدادات المخزون");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ⚠️ تحذير في إنشاء بيانات المخزون: {ex.Message}");
            }
        }
    }
}
