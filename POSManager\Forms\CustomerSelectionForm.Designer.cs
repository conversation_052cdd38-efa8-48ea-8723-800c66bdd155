namespace POSManager.Forms
{
    partial class CustomerSelectionForm
    {
        private System.ComponentModel.IContainer components = null;
        private TableLayoutPanel mainLayout;
        private Panel topPanel;
        private Panel bottomPanel;
        private Label lblSearch;
        private TextBox txtSearch;
        private DataGridView dgvCustomers;
        private Button btnSelect;
        private Button btnCancel;
        private Button btnAddNew;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.mainLayout = new TableLayoutPanel();
            this.topPanel = new Panel();
            this.bottomPanel = new Panel();
            this.lblSearch = new Label();
            this.txtSearch = new TextBox();
            this.dgvCustomers = new DataGridView();
            this.btnSelect = new Button();
            this.btnCancel = new Button();
            this.btnAddNew = new Button();

            this.mainLayout.SuspendLayout();
            this.topPanel.SuspendLayout();
            this.bottomPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCustomers)).BeginInit();
            this.SuspendLayout();

            // 
            // CustomerSelectionForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.mainLayout);
            this.Font = new Font("Segoe UI", 9F);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CustomerSelectionForm";
            this.RightToLeft = RightToLeft.Yes;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "اختيار عميل";

            // 
            // mainLayout
            // 
            this.mainLayout.ColumnCount = 1;
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            this.mainLayout.Controls.Add(this.topPanel, 0, 0);
            this.mainLayout.Controls.Add(this.dgvCustomers, 0, 1);
            this.mainLayout.Controls.Add(this.bottomPanel, 0, 2);
            this.mainLayout.Dock = DockStyle.Fill;
            this.mainLayout.Location = new Point(0, 0);
            this.mainLayout.Margin = new Padding(4);
            this.mainLayout.Name = "mainLayout";
            this.mainLayout.RowCount = 3;
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            this.mainLayout.Size = new Size(800, 600);
            this.mainLayout.TabIndex = 0;

            // 
            // topPanel
            // 
            this.topPanel.Controls.Add(this.lblSearch);
            this.topPanel.Controls.Add(this.txtSearch);
            this.topPanel.Dock = DockStyle.Fill;
            this.topPanel.Location = new Point(4, 4);
            this.topPanel.Margin = new Padding(4);
            this.topPanel.Name = "topPanel";
            this.topPanel.Size = new Size(792, 52);
            this.topPanel.TabIndex = 0;

            // 
            // lblSearch
            // 
            this.lblSearch.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.lblSearch.AutoSize = true;
            this.lblSearch.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblSearch.Location = new Point(720, 15);
            this.lblSearch.Margin = new Padding(4, 0, 4, 0);
            this.lblSearch.Name = "lblSearch";
            this.lblSearch.Size = new Size(68, 23);
            this.lblSearch.TabIndex = 0;
            this.lblSearch.Text = "البحث:";

            // 
            // txtSearch
            // 
            this.txtSearch.Anchor = ((AnchorStyles)(((AnchorStyles.Top | AnchorStyles.Left) | AnchorStyles.Right)));
            this.txtSearch.Font = new Font("Segoe UI", 11F);
            this.txtSearch.Location = new Point(8, 12);
            this.txtSearch.Margin = new Padding(4);
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.PlaceholderText = "ابحث بالاسم أو الهاتف أو البريد الإلكتروني...";
            this.txtSearch.Size = new Size(704, 32);
            this.txtSearch.TabIndex = 1;
            this.txtSearch.TextChanged += new EventHandler(this.txtSearch_TextChanged);
            this.txtSearch.KeyDown += new KeyEventHandler(this.txtSearch_KeyDown);

            // 
            // dgvCustomers
            // 
            this.dgvCustomers.AllowUserToAddRows = false;
            this.dgvCustomers.AllowUserToDeleteRows = false;
            this.dgvCustomers.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvCustomers.BackgroundColor = SystemColors.Window;
            this.dgvCustomers.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCustomers.Dock = DockStyle.Fill;
            this.dgvCustomers.Location = new Point(4, 64);
            this.dgvCustomers.Margin = new Padding(4);
            this.dgvCustomers.MultiSelect = false;
            this.dgvCustomers.Name = "dgvCustomers";
            this.dgvCustomers.ReadOnly = true;
            this.dgvCustomers.RowHeadersVisible = false;
            this.dgvCustomers.RowHeadersWidth = 51;
            this.dgvCustomers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvCustomers.Size = new Size(792, 472);
            this.dgvCustomers.TabIndex = 1;
            this.dgvCustomers.CellDoubleClick += new DataGridViewCellEventHandler(this.dgvCustomers_CellDoubleClick);
            this.dgvCustomers.KeyDown += new KeyEventHandler(this.dgvCustomers_KeyDown);

            // 
            // bottomPanel
            // 
            this.bottomPanel.Controls.Add(this.btnAddNew);
            this.bottomPanel.Controls.Add(this.btnCancel);
            this.bottomPanel.Controls.Add(this.btnSelect);
            this.bottomPanel.Dock = DockStyle.Fill;
            this.bottomPanel.Location = new Point(4, 544);
            this.bottomPanel.Margin = new Padding(4);
            this.bottomPanel.Name = "bottomPanel";
            this.bottomPanel.Size = new Size(792, 52);
            this.bottomPanel.TabIndex = 2;

            // 
            // btnAddNew
            // 
            this.btnAddNew.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Left)));
            this.btnAddNew.BackColor = Color.DodgerBlue;
            this.btnAddNew.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnAddNew.ForeColor = Color.White;
            this.btnAddNew.Location = new Point(8, 8);
            this.btnAddNew.Margin = new Padding(4);
            this.btnAddNew.Name = "btnAddNew";
            this.btnAddNew.Size = new Size(120, 40);
            this.btnAddNew.TabIndex = 2;
            this.btnAddNew.Text = "إضافة عميل جديد";
            this.btnAddNew.UseVisualStyleBackColor = false;
            this.btnAddNew.Click += new EventHandler(this.btnAddNew_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.Location = new Point(560, 8);
            this.btnCancel.Margin = new Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(100, 40);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // 
            // btnSelect
            // 
            this.btnSelect.Anchor = ((AnchorStyles)((AnchorStyles.Top | AnchorStyles.Right)));
            this.btnSelect.BackColor = Color.Green;
            this.btnSelect.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSelect.ForeColor = Color.White;
            this.btnSelect.Location = new Point(668, 8);
            this.btnSelect.Margin = new Padding(4);
            this.btnSelect.Name = "btnSelect";
            this.btnSelect.Size = new Size(120, 40);
            this.btnSelect.TabIndex = 0;
            this.btnSelect.Text = "اختيار";
            this.btnSelect.UseVisualStyleBackColor = false;
            this.btnSelect.Click += new EventHandler(this.btnSelect_Click);

            this.mainLayout.ResumeLayout(false);
            this.topPanel.ResumeLayout(false);
            this.topPanel.PerformLayout();
            this.bottomPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCustomers)).EndInit();
            this.ResumeLayout(false);
        }
    }
}
