using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;

namespace POSManager.Services
{
    public class ProductService
    {
        public static async Task<List<Product>> GetAllProductsAsync()
        {
            var products = new List<Product>();
            
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT p.*, c.Name as CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    ORDER BY p.Name";
                
                using var command = new SqliteCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    products.Add(new Product
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Barcode = reader["Barcode"].ToString(),
                        CategoryId = reader["CategoryId"] == DBNull.Value ? null : Convert.ToInt32(reader["CategoryId"]),
                        CategoryName = reader["CategoryName"].ToString(),
                        PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                        SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                        Stock = Convert.ToInt32(reader["Stock"]),
                        MinStock = Convert.ToInt32(reader["MinStock"]),
                        Unit = reader["Unit"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString())
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المنتجات: {ex.Message}");
            }
            
            return products;
        }
        
        public static async Task<Product?> GetProductByIdAsync(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT p.*, c.Name as CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.Id = @id";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    return new Product
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Barcode = reader["Barcode"].ToString(),
                        CategoryId = reader["CategoryId"] == DBNull.Value ? null : Convert.ToInt32(reader["CategoryId"]),
                        CategoryName = reader["CategoryName"].ToString(),
                        PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                        SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                        Stock = Convert.ToInt32(reader["Stock"]),
                        MinStock = Convert.ToInt32(reader["MinStock"]),
                        Unit = reader["Unit"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString())
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المنتج: {ex.Message}");
            }
            
            return null;
        }
        
        public static async Task<Product?> GetProductByBarcodeAsync(string barcode)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT p.*, c.Name as CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.Barcode = @barcode AND p.IsActive = 1";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@barcode", barcode);
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    return new Product
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Barcode = reader["Barcode"].ToString(),
                        CategoryId = reader["CategoryId"] == DBNull.Value ? null : Convert.ToInt32(reader["CategoryId"]),
                        CategoryName = reader["CategoryName"].ToString(),
                        PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                        SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                        Stock = Convert.ToInt32(reader["Stock"]),
                        MinStock = Convert.ToInt32(reader["MinStock"]),
                        Unit = reader["Unit"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString())
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث بالباركود: {ex.Message}");
            }
            
            return null;
        }
        
        public static async Task<List<Product>> SearchProductsAsync(string searchTerm)
        {
            var products = new List<Product>();
            
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT p.*, c.Name as CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE (p.Name LIKE @search OR p.Barcode LIKE @search OR c.Name LIKE @search)
                    AND p.IsActive = 1
                    ORDER BY p.Name";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    products.Add(new Product
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Barcode = reader["Barcode"].ToString(),
                        CategoryId = reader["CategoryId"] == DBNull.Value ? null : Convert.ToInt32(reader["CategoryId"]),
                        CategoryName = reader["CategoryName"].ToString(),
                        PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                        SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                        Stock = Convert.ToInt32(reader["Stock"]),
                        MinStock = Convert.ToInt32(reader["MinStock"]),
                        Unit = reader["Unit"].ToString() ?? "",
                        Description = reader["Description"].ToString(),
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString())
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث: {ex.Message}");
            }
            
            return products;
        }
        
        public static async Task<bool> AddProductAsync(Product product)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    INSERT INTO Products (Name, Barcode, CategoryId, PurchasePrice, SalePrice, Stock, MinStock, Unit, Description, IsActive, CreatedAt)
                    VALUES (@name, @barcode, @categoryId, @purchasePrice, @salePrice, @stock, @minStock, @unit, @description, @isActive, @createdAt)";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@name", product.Name);
                command.Parameters.AddWithValue("@barcode", product.Barcode ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@categoryId", product.CategoryId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@purchasePrice", product.PurchasePrice);
                command.Parameters.AddWithValue("@salePrice", product.SalePrice);
                command.Parameters.AddWithValue("@stock", product.Stock);
                command.Parameters.AddWithValue("@minStock", product.MinStock);
                command.Parameters.AddWithValue("@unit", product.Unit ?? "قطعة");
                command.Parameters.AddWithValue("@description", product.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isActive", product.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                
                var result = await command.ExecuteNonQueryAsync();

                if (result > 0)
                {
                    // الحصول على ID المنتج الجديد
                    var getIdQuery = "SELECT last_insert_rowid()";
                    using var getIdCommand = new SqliteCommand(getIdQuery, connection);
                    var newProductId = Convert.ToInt32(await getIdCommand.ExecuteScalarAsync());

                    // تسجيل تغيير المخزون إذا كان هناك مستخدم مسجل دخول
                    if (AuthService.CurrentUser != null)
                    {
                        await LogInventoryChangeAsync(newProductId, "إضافة منتج", product.Stock, 0, product.Stock, "إضافة منتج جديد");
                    }
                }

                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة المنتج: {ex.Message}");
            }
        }
        
        public static async Task<bool> UpdateProductAsync(Product product)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                // جلب المخزون القديم لتسجيل التغيير
                var oldProduct = await GetProductByIdAsync(product.Id);
                
                var query = @"
                    UPDATE Products 
                    SET Name = @name, Barcode = @barcode, CategoryId = @categoryId, 
                        PurchasePrice = @purchasePrice, SalePrice = @salePrice, 
                        Stock = @stock, MinStock = @minStock, Unit = @unit, 
                        Description = @description, IsActive = @isActive
                    WHERE Id = @id";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@id", product.Id);
                command.Parameters.AddWithValue("@name", product.Name);
                command.Parameters.AddWithValue("@barcode", product.Barcode ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@categoryId", product.CategoryId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@purchasePrice", product.PurchasePrice);
                command.Parameters.AddWithValue("@salePrice", product.SalePrice);
                command.Parameters.AddWithValue("@stock", product.Stock);
                command.Parameters.AddWithValue("@minStock", product.MinStock);
                command.Parameters.AddWithValue("@unit", product.Unit ?? "قطعة");
                command.Parameters.AddWithValue("@description", product.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isActive", product.IsActive ? 1 : 0);
                
                var result = await command.ExecuteNonQueryAsync();
                
                // تسجيل تغيير المخزون إذا تغير
                if (result > 0 && oldProduct != null && oldProduct.Stock != product.Stock && AuthService.CurrentUser != null)
                {
                    await LogInventoryChangeAsync(product.Id, "تعديل مخزون", product.Stock - oldProduct.Stock, oldProduct.Stock, product.Stock, "تعديل المنتج");
                }
                
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المنتج: {ex.Message}");
            }
        }
        
        public static async Task<bool> DeleteProductAsync(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                // التحقق من وجود مبيعات للمنتج
                var checkQuery = "SELECT COUNT(*) FROM SaleDetails WHERE ProductId = @id";
                using var checkCommand = new SqliteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@id", id);
                var salesCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());
                
                if (salesCount > 0)
                {
                    // إلغاء تفعيل المنتج بدلاً من حذفه
                    var deactivateQuery = "UPDATE Products SET IsActive = 0 WHERE Id = @id";
                    using var deactivateCommand = new SqliteCommand(deactivateQuery, connection);
                    deactivateCommand.Parameters.AddWithValue("@id", id);
                    return await deactivateCommand.ExecuteNonQueryAsync() > 0;
                }
                else
                {
                    // حذف المنتج نهائياً
                    var deleteQuery = "DELETE FROM Products WHERE Id = @id";
                    using var deleteCommand = new SqliteCommand(deleteQuery, connection);
                    deleteCommand.Parameters.AddWithValue("@id", id);
                    return await deleteCommand.ExecuteNonQueryAsync() > 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المنتج: {ex.Message}");
            }
        }
        
        private static async Task LogInventoryChangeAsync(int productId, string changeType, int quantity, int oldStock, int newStock, string notes)
        {
            try
            {
                if (AuthService.CurrentUser == null) return;
                
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    INSERT INTO InventoryLog (ProductId, ChangeType, Quantity, OldStock, NewStock, UserId, Notes, CreatedAt)
                    VALUES (@productId, @changeType, @quantity, @oldStock, @newStock, @userId, @notes, @createdAt)";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@productId", productId);
                command.Parameters.AddWithValue("@changeType", changeType);
                command.Parameters.AddWithValue("@quantity", quantity);
                command.Parameters.AddWithValue("@oldStock", oldStock);
                command.Parameters.AddWithValue("@newStock", newStock);
                command.Parameters.AddWithValue("@userId", AuthService.CurrentUser.Id);
                command.Parameters.AddWithValue("@notes", notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                
                await command.ExecuteNonQueryAsync();
            }
            catch
            {
                // تجاهل أخطاء تسجيل المخزون
            }
        }
    }
}
