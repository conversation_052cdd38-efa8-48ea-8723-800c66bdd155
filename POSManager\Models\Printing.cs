using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;

namespace POSManager.Models
{
    // أنواع الطباعة المدعومة
    public enum PrintType
    {
        Invoice = 1,        // فاتورة
        Receipt = 2,        // إيصال
        Report = 3,         // تقرير
        Label = 4,          // ملصق
        Barcode = 5         // باركود
    }

    // أحجام الورق المدعومة
    public enum PaperSize
    {
        Thermal80mm = 1,    // حراري 80 مم
        Thermal58mm = 2,    // حراري 58 مم
        A4 = 3,             // A4
        A5 = 4,             // A5
        Letter = 5,         // Letter
        Custom = 6          // مخصص
    }

    // اتجاه الطباعة
    public enum PrintOrientation
    {
        Portrait = 1,       // عمودي
        Landscape = 2       // أفقي
    }

    // جودة الطباعة
    public enum PrintQuality
    {
        Draft = 1,          // مسودة
        Normal = 2,         // عادي
        High = 3            // عالي
    }

    // نموذج مستند الطباعة
    public class PrintDocument
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public PrintType Type { get; set; }
        public string Content { get; set; } = "";
        public string TemplateId { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public bool IsPrinted { get; set; }
        public DateTime? PrintedDate { get; set; }
        public string PrinterName { get; set; } = "";
        public int Copies { get; set; } = 1;
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    // نموذج قالب الطباعة
    public class PrintTemplate
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public PrintType Type { get; set; }
        public PaperSize PaperSize { get; set; }
        public PrintOrientation Orientation { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public int MarginTop { get; set; }
        public int MarginBottom { get; set; }
        public int MarginLeft { get; set; }
        public int MarginRight { get; set; }
        public string HeaderTemplate { get; set; } = "";
        public string BodyTemplate { get; set; } = "";
        public string FooterTemplate { get; set; } = "";
        public bool ShowLogo { get; set; }
        public string LogoPath { get; set; } = "";
        public int LogoWidth { get; set; }
        public int LogoHeight { get; set; }
        public string FontFamily { get; set; } = "Tahoma";
        public int FontSize { get; set; } = 10;
        public bool IsDefault { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    // إعدادات الطابعة
    public class PrinterSettings
    {
        public string Name { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public PrintType[] SupportedTypes { get; set; } = Array.Empty<PrintType>();
        public PaperSize DefaultPaperSize { get; set; }
        public PrintOrientation DefaultOrientation { get; set; }
        public PrintQuality DefaultQuality { get; set; }
        public bool IsDefault { get; set; }
        public bool IsOnline { get; set; }
        public string Status { get; set; } = "";
        public int MaxCopies { get; set; } = 99;
        public bool SupportsDuplex { get; set; }
        public bool SupportsColor { get; set; }
        public string[] SupportedPaperSizes { get; set; } = Array.Empty<string>();
        public Dictionary<string, object> CustomSettings { get; set; } = new Dictionary<string, object>();
    }

    // إعدادات مهمة الطباعة
    public class PrintJobSettings
    {
        public string PrinterName { get; set; } = "";
        public PaperSize PaperSize { get; set; }
        public PrintOrientation Orientation { get; set; }
        public PrintQuality Quality { get; set; }
        public int Copies { get; set; } = 1;
        public bool Collate { get; set; }
        public bool PrintToFile { get; set; }
        public string OutputFileName { get; set; } = "";
        public PrintRange PrintRange { get; set; }
        public int FromPage { get; set; } = 1;
        public int ToPage { get; set; } = 1;
        public bool PrintSelection { get; set; }
        public Dictionary<string, object> CustomSettings { get; set; } = new Dictionary<string, object>();
    }

    // نتيجة عملية الطباعة
    public class PrintResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public string ErrorCode { get; set; } = "";
        public DateTime PrintTime { get; set; }
        public int PagesPrinted { get; set; }
        public string PrinterUsed { get; set; } = "";
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Details { get; set; } = new Dictionary<string, object>();
    }

    // بيانات الفاتورة للطباعة
    public class InvoicePrintData
    {
        public int InvoiceId { get; set; }
        public string InvoiceNumber { get; set; } = "";
        public DateTime Date { get; set; }
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public string CustomerAddress { get; set; } = "";
        public List<InvoiceItemPrintData> Items { get; set; } = new List<InvoiceItemPrintData>();
        public decimal Subtotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal Total { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal ChangeAmount { get; set; }
        public string PaymentMethod { get; set; } = "";
        public string CashierName { get; set; } = "";
        public string Notes { get; set; } = "";
        public string CompanyName { get; set; } = "";
        public string CompanyAddress { get; set; } = "";
        public string CompanyPhone { get; set; } = "";
        public string CompanyEmail { get; set; } = "";
        public string TaxNumber { get; set; } = "";
    }

    // بيانات عنصر الفاتورة للطباعة
    public class InvoiceItemPrintData
    {
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public decimal Quantity { get; set; }
        public string Unit { get; set; } = "";
        public decimal UnitPrice { get; set; }
        public decimal Discount { get; set; }
        public decimal Total { get; set; }
        public string Notes { get; set; } = "";
    }

    // بيانات التقرير للطباعة
    public class ReportPrintData
    {
        public string Title { get; set; } = "";
        public string Subtitle { get; set; } = "";
        public DateTime GeneratedDate { get; set; }
        public string GeneratedBy { get; set; } = "";
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
        public List<Dictionary<string, object>> Data { get; set; } = new List<Dictionary<string, object>>();
        public Dictionary<string, object> Summary { get; set; } = new Dictionary<string, object>();
        public List<string> Headers { get; set; } = new List<string>();
        public string CompanyName { get; set; } = "";
        public string CompanyAddress { get; set; } = "";
        public string CompanyPhone { get; set; } = "";
    }

    // حالة الطابعة
    public class PrinterStatus
    {
        public string Name { get; set; } = "";
        public bool IsOnline { get; set; }
        public bool IsReady { get; set; }
        public bool HasError { get; set; }
        public string ErrorMessage { get; set; } = "";
        public bool IsPaperOut { get; set; }
        public bool IsTonerLow { get; set; }
        public int QueueCount { get; set; }
        public DateTime LastChecked { get; set; }
    }

    // إحصائيات الطباعة
    public class PrintStatistics
    {
        public int TotalPrintJobs { get; set; }
        public int SuccessfulJobs { get; set; }
        public int FailedJobs { get; set; }
        public int TotalPages { get; set; }
        public TimeSpan TotalPrintTime { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public Dictionary<string, int> PrinterUsage { get; set; } = new Dictionary<string, int>();
        public Dictionary<PrintType, int> TypeUsage { get; set; } = new Dictionary<PrintType, int>();
    }
}
