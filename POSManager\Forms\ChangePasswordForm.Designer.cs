namespace POSManager.Forms
{
    partial class ChangePasswordForm
    {
        private System.ComponentModel.IContainer components = null;
        private TableLayoutPanel mainLayout;
        private Panel headerPanel;
        private Panel formPanel;
        private Panel buttonPanel;
        private Label lblTitle;
        private Label lblUserInfo;
        private Label lblNewPassword;
        private TextBox txtNewPassword;
        private Label lblConfirmPassword;
        private TextBox txtConfirmPassword;
        private Button btnShowPassword;
        private Button btnSave;
        private Button btnCancel;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.mainLayout = new TableLayoutPanel();
            this.headerPanel = new Panel();
            this.formPanel = new Panel();
            this.buttonPanel = new Panel();
            this.lblTitle = new Label();
            this.lblUserInfo = new Label();
            this.lblNewPassword = new Label();
            this.txtNewPassword = new TextBox();
            this.lblConfirmPassword = new Label();
            this.txtConfirmPassword = new TextBox();
            this.btnShowPassword = new Button();
            this.btnSave = new Button();
            this.btnCancel = new Button();

            this.mainLayout.SuspendLayout();
            this.headerPanel.SuspendLayout();
            this.formPanel.SuspendLayout();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();

            // 
            // ChangePasswordForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 350);
            this.Controls.Add(this.mainLayout);
            this.Font = new Font("Segoe UI", 9F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ChangePasswordForm";
            this.RightToLeft = RightToLeft.Yes;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "تغيير كلمة المرور";
            this.KeyDown += new KeyEventHandler(this.ChangePasswordForm_KeyDown);

            // 
            // mainLayout
            // 
            this.mainLayout.ColumnCount = 1;
            this.mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            this.mainLayout.Controls.Add(this.headerPanel, 0, 0);
            this.mainLayout.Controls.Add(this.formPanel, 0, 1);
            this.mainLayout.Controls.Add(this.buttonPanel, 0, 2);
            this.mainLayout.Dock = DockStyle.Fill;
            this.mainLayout.Location = new Point(0, 0);
            this.mainLayout.Margin = new Padding(4);
            this.mainLayout.Name = "mainLayout";
            this.mainLayout.RowCount = 3;
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 100F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 70F));
            this.mainLayout.Size = new Size(500, 350);
            this.mainLayout.TabIndex = 0;

            // 
            // headerPanel
            // 
            this.headerPanel.BackColor = Color.FromArgb(240, 240, 240);
            this.headerPanel.Controls.Add(this.lblUserInfo);
            this.headerPanel.Controls.Add(this.lblTitle);
            this.headerPanel.Dock = DockStyle.Fill;
            this.headerPanel.Location = new Point(4, 4);
            this.headerPanel.Margin = new Padding(4);
            this.headerPanel.Name = "headerPanel";
            this.headerPanel.Padding = new Padding(20);
            this.headerPanel.Size = new Size(492, 92);
            this.headerPanel.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.DarkBlue;
            this.lblTitle.Location = new Point(350, 20);
            this.lblTitle.Margin = new Padding(4, 0, 4, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(122, 32);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "تغيير كلمة المرور";

            // 
            // lblUserInfo
            // 
            this.lblUserInfo.AutoSize = true;
            this.lblUserInfo.Font = new Font("Segoe UI", 10F);
            this.lblUserInfo.ForeColor = Color.DarkGreen;
            this.lblUserInfo.Location = new Point(20, 55);
            this.lblUserInfo.Margin = new Padding(4, 0, 4, 0);
            this.lblUserInfo.Name = "lblUserInfo";
            this.lblUserInfo.Size = new Size(89, 23);
            this.lblUserInfo.TabIndex = 1;
            this.lblUserInfo.Text = "معلومات المستخدم";

            // 
            // formPanel
            // 
            this.formPanel.Controls.Add(this.btnShowPassword);
            this.formPanel.Controls.Add(this.lblConfirmPassword);
            this.formPanel.Controls.Add(this.txtConfirmPassword);
            this.formPanel.Controls.Add(this.lblNewPassword);
            this.formPanel.Controls.Add(this.txtNewPassword);
            this.formPanel.Dock = DockStyle.Fill;
            this.formPanel.Location = new Point(4, 104);
            this.formPanel.Margin = new Padding(4);
            this.formPanel.Name = "formPanel";
            this.formPanel.Padding = new Padding(30);
            this.formPanel.Size = new Size(492, 172);
            this.formPanel.TabIndex = 1;

            // 
            // lblNewPassword
            // 
            this.lblNewPassword.AutoSize = true;
            this.lblNewPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblNewPassword.Location = new Point(370, 35);
            this.lblNewPassword.Margin = new Padding(4, 0, 4, 0);
            this.lblNewPassword.Name = "lblNewPassword";
            this.lblNewPassword.Size = new Size(92, 23);
            this.lblNewPassword.TabIndex = 0;
            this.lblNewPassword.Text = "كلمة المرور الجديدة";

            // 
            // txtNewPassword
            // 
            this.txtNewPassword.Font = new Font("Segoe UI", 11F);
            this.txtNewPassword.Location = new Point(35, 32);
            this.txtNewPassword.Margin = new Padding(4);
            this.txtNewPassword.Name = "txtNewPassword";
            this.txtNewPassword.PasswordChar = '*';
            this.txtNewPassword.Size = new Size(327, 32);
            this.txtNewPassword.TabIndex = 1;
            this.txtNewPassword.KeyDown += new KeyEventHandler(this.txtNewPassword_KeyDown);

            // 
            // lblConfirmPassword
            // 
            this.lblConfirmPassword.AutoSize = true;
            this.lblConfirmPassword.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblConfirmPassword.Location = new Point(340, 85);
            this.lblConfirmPassword.Margin = new Padding(4, 0, 4, 0);
            this.lblConfirmPassword.Name = "lblConfirmPassword";
            this.lblConfirmPassword.Size = new Size(122, 23);
            this.lblConfirmPassword.TabIndex = 2;
            this.lblConfirmPassword.Text = "تأكيد كلمة المرور الجديدة";

            // 
            // txtConfirmPassword
            // 
            this.txtConfirmPassword.Font = new Font("Segoe UI", 11F);
            this.txtConfirmPassword.Location = new Point(35, 82);
            this.txtConfirmPassword.Margin = new Padding(4);
            this.txtConfirmPassword.Name = "txtConfirmPassword";
            this.txtConfirmPassword.PasswordChar = '*';
            this.txtConfirmPassword.Size = new Size(327, 32);
            this.txtConfirmPassword.TabIndex = 3;
            this.txtConfirmPassword.KeyDown += new KeyEventHandler(this.txtConfirmPassword_KeyDown);

            // 
            // btnShowPassword
            // 
            this.btnShowPassword.Font = new Font("Segoe UI", 9F);
            this.btnShowPassword.Location = new Point(35, 125);
            this.btnShowPassword.Margin = new Padding(4);
            this.btnShowPassword.Name = "btnShowPassword";
            this.btnShowPassword.Size = new Size(100, 30);
            this.btnShowPassword.TabIndex = 4;
            this.btnShowPassword.Text = "إظهار";
            this.btnShowPassword.UseVisualStyleBackColor = true;
            this.btnShowPassword.Click += new EventHandler(this.btnShowPassword_Click);

            // 
            // buttonPanel
            // 
            this.buttonPanel.BackColor = Color.FromArgb(240, 240, 240);
            this.buttonPanel.Controls.Add(this.btnCancel);
            this.buttonPanel.Controls.Add(this.btnSave);
            this.buttonPanel.Dock = DockStyle.Fill;
            this.buttonPanel.Location = new Point(4, 284);
            this.buttonPanel.Margin = new Padding(4);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new Size(492, 62);
            this.buttonPanel.TabIndex = 2;

            // 
            // btnSave
            // 
            this.btnSave.BackColor = Color.Green;
            this.btnSave.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(350, 15);
            this.btnSave.Margin = new Padding(4);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(120, 35);
            this.btnSave.TabIndex = 0;
            this.btnSave.Text = "حفظ (F12)";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.btnCancel.Location = new Point(220, 15);
            this.btnCancel.Margin = new Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(120, 35);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء (Esc)";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.mainLayout.ResumeLayout(false);
            this.headerPanel.ResumeLayout(false);
            this.headerPanel.PerformLayout();
            this.formPanel.ResumeLayout(false);
            this.formPanel.PerformLayout();
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);
        }
    }
}
