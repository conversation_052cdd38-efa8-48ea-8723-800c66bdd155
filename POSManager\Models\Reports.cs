namespace POSManager.Models
{
    #region Sales Report Models

    /// <summary>
    /// تقرير المبيعات اليومية
    /// </summary>
    public class DailySalesReport
    {
        public DateTime Date { get; set; }
        public int TotalTransactions { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalTax { get; set; }
        public decimal NetSales => TotalSales - TotalDiscounts;
        public List<SalesSummary> SalesData { get; set; } = new List<SalesSummary>();
    }

    /// <summary>
    /// تقرير المبيعات لفترة محددة
    /// </summary>
    public class PeriodSalesReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public int TotalTransactions { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal AverageDailySales { get; set; }
        public List<DailySalesSummary> DailySales { get; set; } = new List<DailySalesSummary>();
        public List<ProductSalesSummary> TopProducts { get; set; } = new List<ProductSalesSummary>();
        public List<UserSalesSummary> UserPerformance { get; set; } = new List<UserSalesSummary>();
    }

    /// <summary>
    /// ملخص المبيعات حسب المنتج
    /// </summary>
    public class SalesSummary
    {
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public int TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AveragePrice { get; set; }
    }

    /// <summary>
    /// ملخص المبيعات اليومية
    /// </summary>
    public class DailySalesSummary
    {
        public DateTime Date { get; set; }
        public int TransactionCount { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal NetSales => TotalSales - TotalDiscounts;
        public string DateFormatted => Date.ToString("yyyy-MM-dd");
    }

    /// <summary>
    /// ملخص مبيعات المنتج
    /// </summary>
    public class ProductSalesSummary
    {
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public int TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageQuantityPerTransaction => TransactionCount > 0 ? (decimal)TotalQuantity / TransactionCount : 0;
    }

    /// <summary>
    /// ملخص أداء المستخدم
    /// </summary>
    public class UserSalesSummary
    {
        public string UserName { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public int TransactionCount { get; set; }
        public decimal TotalSales { get; set; }
        public decimal AverageSale { get; set; }
    }

    #endregion

    #region Inventory Report Models

    /// <summary>
    /// تقرير حالة المخزون
    /// </summary>
    public class InventoryStatusReport
    {
        public DateTime GeneratedAt { get; set; }
        public int TotalProducts { get; set; }
        public int InStockCount { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public decimal TotalStockValue { get; set; }
        public List<ProductInventoryStatus> Products { get; set; } = new List<ProductInventoryStatus>();
    }

    /// <summary>
    /// حالة مخزون المنتج
    /// </summary>
    public class ProductInventoryStatus
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public string CategoryName { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinStock { get; set; }
        public decimal SalePrice { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal StockValue { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool IsLowStock => CurrentStock <= MinStock && CurrentStock > 0;
        public bool IsOutOfStock => CurrentStock <= 0;
        public decimal ProfitMargin => SalePrice > 0 && PurchasePrice > 0 ? ((SalePrice - PurchasePrice) / SalePrice) * 100 : 0;
    }

    #endregion

    #region User Performance Report Models

    /// <summary>
    /// تقرير أداء المستخدمين
    /// </summary>
    public class UserPerformanceReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<UserPerformanceDetail> UserDetails { get; set; } = new List<UserPerformanceDetail>();
    }

    /// <summary>
    /// تفاصيل أداء المستخدم
    /// </summary>
    public class UserPerformanceDetail
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public int TotalTransactions { get; set; }
        public decimal TotalSales { get; set; }
        public decimal AverageSaleAmount { get; set; }
        public int TotalItemsSold { get; set; }
        public decimal TotalDiscountsGiven { get; set; }
        public DateTime? LastSaleDate { get; set; }
        public int ActiveDays { get; set; }
        public decimal DailyAverageSales => ActiveDays > 0 ? TotalSales / ActiveDays : 0;
    }

    #endregion

    #region Financial Summary Models

    /// <summary>
    /// الملخص المالي
    /// </summary>
    public class FinancialSummary
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit => TotalRevenue - TotalExpenses;
        public decimal TotalDiscounts { get; set; }
        public decimal TotalTax { get; set; }
        public int TotalTransactions { get; set; }
        public decimal AverageTransactionValue => TotalTransactions > 0 ? TotalRevenue / TotalTransactions : 0;
        public List<ExpenseSummary> ExpensesByCategory { get; set; } = new List<ExpenseSummary>();
        public List<DailyFinancialSummary> DailyBreakdown { get; set; } = new List<DailyFinancialSummary>();
    }

    /// <summary>
    /// ملخص المصروفات حسب الفئة
    /// </summary>
    public class ExpenseSummary
    {
        public string Category { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageAmount => TransactionCount > 0 ? TotalAmount / TransactionCount : 0;
    }

    /// <summary>
    /// الملخص المالي اليومي
    /// </summary>
    public class DailyFinancialSummary
    {
        public DateTime Date { get; set; }
        public decimal Revenue { get; set; }
        public decimal Expenses { get; set; }
        public decimal NetProfit => Revenue - Expenses;
        public int TransactionCount { get; set; }
        public string DateFormatted => Date.ToString("yyyy-MM-dd");
    }

    #endregion

    #region Report Filter Models

    /// <summary>
    /// فلتر التقارير
    /// </summary>
    public class ReportFilter
    {
        public DateTime StartDate { get; set; } = DateTime.Today.AddDays(-30);
        public DateTime EndDate { get; set; } = DateTime.Today;
        public int? UserId { get; set; }
        public int? CategoryId { get; set; }
        public int? ProductId { get; set; }
        public string? PaymentMethod { get; set; }
        public ReportType ReportType { get; set; }
        public ReportFormat ExportFormat { get; set; } = ReportFormat.PDF;
    }

    /// <summary>
    /// أنواع التقارير
    /// </summary>
    public enum ReportType
    {
        DailySales,
        PeriodSales,
        InventoryStatus,
        UserPerformance,
        FinancialSummary,
        TopProducts,
        LowStockAlert
    }

    /// <summary>
    /// تنسيقات التصدير
    /// </summary>
    public enum ReportFormat
    {
        PDF,
        Excel,
        CSV
    }

    #endregion
}
