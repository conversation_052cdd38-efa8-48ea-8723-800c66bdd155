using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class PrinterSettingsForm : Form
    {
        private ComboBox cmbPrinters;
        private ComboBox cmbPaperSize;
        private ComboBox cmbOrientation;
        private ComboBox cmbQuality;
        private NumericUpDown numCopies;
        private CheckBox chkCollate;
        private CheckBox chkSetAsDefault;
        private Button btnTestPrint;
        private Button btnRefreshPrinters;
        private Button btnSave;
        private Button btnCancel;
        private Label lblStatus;
        private GroupBox grpPrinterInfo;
        private GroupBox grpPrintSettings;
        private GroupBox grpTestPrint;

        private PrintService _printService;
        private Models.PrinterSettings? _selectedPrinter;

        public PrinterSettingsForm()
        {
            _printService = PrintService.Instance;
            InitializeComponent();
            LoadPrinters();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة
            this.Text = "إعدادات الطابعة";
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // مجموعة معلومات الطابعة
            grpPrinterInfo = new GroupBox
            {
                Text = "معلومات الطابعة",
                Location = new Point(12, 12),
                Size = new Size(460, 120),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var lblPrinter = new Label
            {
                Text = "الطابعة:",
                Location = new Point(350, 25),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbPrinters = new ComboBox
            {
                Location = new Point(15, 25),
                Size = new Size(320, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };
            cmbPrinters.SelectedIndexChanged += CmbPrinters_SelectedIndexChanged;

            btnRefreshPrinters = new Button
            {
                Text = "تحديث",
                Location = new Point(350, 55),
                Size = new Size(80, 25)
            };
            btnRefreshPrinters.Click += BtnRefreshPrinters_Click;

            lblStatus = new Label
            {
                Text = "الحالة: غير محدد",
                Location = new Point(15, 55),
                Size = new Size(320, 23),
                ForeColor = Color.Gray
            };

            chkSetAsDefault = new CheckBox
            {
                Text = "تعيين كطابعة افتراضية",
                Location = new Point(15, 85),
                Size = new Size(200, 23)
            };

            grpPrinterInfo.Controls.AddRange(new Control[]
            {
                lblPrinter, cmbPrinters, btnRefreshPrinters, lblStatus, chkSetAsDefault
            });

            // مجموعة إعدادات الطباعة
            grpPrintSettings = new GroupBox
            {
                Text = "إعدادات الطباعة",
                Location = new Point(12, 140),
                Size = new Size(460, 160),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var lblPaperSize = new Label
            {
                Text = "حجم الورق:",
                Location = new Point(350, 25),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbPaperSize = new ComboBox
            {
                Location = new Point(200, 25),
                Size = new Size(140, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            var lblOrientation = new Label
            {
                Text = "الاتجاه:",
                Location = new Point(150, 25),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbOrientation = new ComboBox
            {
                Location = new Point(15, 25),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            var lblQuality = new Label
            {
                Text = "الجودة:",
                Location = new Point(350, 55),
                Size = new Size(80, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbQuality = new ComboBox
            {
                Location = new Point(200, 55),
                Size = new Size(140, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            var lblCopies = new Label
            {
                Text = "النسخ:",
                Location = new Point(150, 55),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };

            numCopies = new NumericUpDown
            {
                Location = new Point(15, 55),
                Size = new Size(120, 23),
                Minimum = 1,
                Maximum = 99,
                Value = 1
            };

            chkCollate = new CheckBox
            {
                Text = "ترتيب الصفحات",
                Location = new Point(15, 85),
                Size = new Size(150, 23)
            };

            grpPrintSettings.Controls.AddRange(new Control[]
            {
                lblPaperSize, cmbPaperSize, lblOrientation, cmbOrientation,
                lblQuality, cmbQuality, lblCopies, numCopies, chkCollate
            });

            // مجموعة اختبار الطباعة
            grpTestPrint = new GroupBox
            {
                Text = "اختبار الطباعة",
                Location = new Point(12, 310),
                Size = new Size(460, 60),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            btnTestPrint = new Button
            {
                Text = "طباعة صفحة اختبار",
                Location = new Point(15, 25),
                Size = new Size(150, 25)
            };
            btnTestPrint.Click += BtnTestPrint_Click;

            grpTestPrint.Controls.Add(btnTestPrint);

            // أزرار التحكم
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(397, 380),
                Size = new Size(75, 25),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(316, 380),
                Size = new Size(75, 25),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right
            };
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر للنافذة
            this.Controls.AddRange(new Control[]
            {
                grpPrinterInfo, grpPrintSettings, grpTestPrint, btnSave, btnCancel
            });

            this.ResumeLayout(false);
            this.PerformLayout();

            // تحميل البيانات الافتراضية
            LoadComboBoxData();
        }

        private void LoadComboBoxData()
        {
            // تحميل أحجام الورق
            cmbPaperSize.Items.AddRange(new object[]
            {
                new { Text = "حراري 80mm", Value = PaperSize.Thermal80mm },
                new { Text = "حراري 58mm", Value = PaperSize.Thermal58mm },
                new { Text = "A4", Value = PaperSize.A4 },
                new { Text = "A5", Value = PaperSize.A5 },
                new { Text = "Letter", Value = PaperSize.Letter }
            });
            cmbPaperSize.DisplayMember = "Text";
            cmbPaperSize.ValueMember = "Value";
            cmbPaperSize.SelectedIndex = 2; // A4

            // تحميل الاتجاهات
            cmbOrientation.Items.AddRange(new object[]
            {
                new { Text = "عمودي", Value = PrintOrientation.Portrait },
                new { Text = "أفقي", Value = PrintOrientation.Landscape }
            });
            cmbOrientation.DisplayMember = "Text";
            cmbOrientation.ValueMember = "Value";
            cmbOrientation.SelectedIndex = 0; // Portrait

            // تحميل مستويات الجودة
            cmbQuality.Items.AddRange(new object[]
            {
                new { Text = "مسودة", Value = PrintQuality.Draft },
                new { Text = "عادي", Value = PrintQuality.Normal },
                new { Text = "عالي", Value = PrintQuality.High }
            });
            cmbQuality.DisplayMember = "Text";
            cmbQuality.ValueMember = "Value";
            cmbQuality.SelectedIndex = 1; // Normal
        }

        private void LoadPrinters()
        {
            try
            {
                cmbPrinters.Items.Clear();
                var printers = _printService.GetAvailablePrinters();

                foreach (var printer in printers)
                {
                    cmbPrinters.Items.Add(printer);
                }

                cmbPrinters.DisplayMember = "DisplayName";

                // تحديد الطابعة الافتراضية
                var defaultPrinter = _printService.GetDefaultPrinter();
                if (defaultPrinter != null)
                {
                    cmbPrinters.SelectedItem = defaultPrinter;
                }
                else if (cmbPrinters.Items.Count > 0)
                {
                    cmbPrinters.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطابعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdatePrinterStatus()
        {
            if (_selectedPrinter != null)
            {
                var status = _printService.GetPrinterStatus(_selectedPrinter.Name);
                
                if (status.IsReady)
                {
                    lblStatus.Text = "الحالة: جاهز";
                    lblStatus.ForeColor = Color.Green;
                }
                else if (status.HasError)
                {
                    lblStatus.Text = $"الحالة: خطأ - {status.ErrorMessage}";
                    lblStatus.ForeColor = Color.Red;
                }
                else
                {
                    lblStatus.Text = "الحالة: غير متاح";
                    lblStatus.ForeColor = Color.Orange;
                }

                chkSetAsDefault.Checked = _selectedPrinter.IsDefault;
            }
        }

        #region معالجات الأحداث

        private void CmbPrinters_SelectedIndexChanged(object sender, EventArgs e)
        {
            _selectedPrinter = cmbPrinters.SelectedItem as Models.PrinterSettings;
            UpdatePrinterStatus();
        }

        private void BtnRefreshPrinters_Click(object sender, EventArgs e)
        {
            _printService.LoadPrinters();
            LoadPrinters();
        }

        private async void BtnTestPrint_Click(object sender, EventArgs e)
        {
            if (_selectedPrinter == null)
            {
                MessageBox.Show("يرجى اختيار طابعة أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnTestPrint.Enabled = false;
                btnTestPrint.Text = "جاري الطباعة...";

                // إنشاء مستند اختبار
                var testDocument = new Models.PrintDocument
                {
                    Title = "صفحة اختبار",
                    Type = PrintType.Receipt,
                    Content = "هذه صفحة اختبار للطابعة\nتم إنشاؤها في: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm")
                };

                var settings = new PrintJobSettings
                {
                    PrinterName = _selectedPrinter.Name,
                    Copies = 1
                };

                var result = await _printService.PrintDocument(testDocument, settings);

                if (result.Success)
                {
                    MessageBox.Show("تمت طباعة صفحة الاختبار بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"فشل في طباعة صفحة الاختبار: {result.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة صفحة الاختبار: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnTestPrint.Enabled = true;
                btnTestPrint.Text = "طباعة صفحة اختبار";
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedPrinter != null && chkSetAsDefault.Checked)
                {
                    await _printService.SetDefaultPrinter(_selectedPrinter.Name);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }
}
