using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;

namespace POSManager
{
    public static class DatabaseInspector
    {
        public static async Task InspectDatabase()
        {
            var output = new List<string>();
            output.Add("=== فحص قاعدة البيانات ===");
            Console.WriteLine("=== فحص قاعدة البيانات ===");
            
            try
            {
                using var connection = new SqliteConnection("Data Source=posmanager.db");
                await connection.OpenAsync();
                
                // فحص الجداول
                output.Add("\n📋 الجداول الموجودة:");
                Console.WriteLine("\n📋 الجداول الموجودة:");
                var tablesQuery = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name";
                using var tablesCommand = new SqliteCommand(tablesQuery, connection);
                using var tablesReader = await tablesCommand.ExecuteReaderAsync();

                while (await tablesReader.ReadAsync())
                {
                    var tableName = tablesReader["name"].ToString();
                    var line = $"  - {tableName}";
                    output.Add(line);
                    Console.WriteLine(line);
                }
                
                // فحص عدد السجلات في كل جدول
                output.Add("\n📊 عدد السجلات:");
                Console.WriteLine("\n📊 عدد السجلات:");

                var tables = new[] { "Categories", "Products", "Users", "Sales", "SaleItems", "Customers" };

                foreach (var table in tables)
                {
                    try
                    {
                        var countQuery = $"SELECT COUNT(*) FROM {table}";
                        using var countCommand = new SqliteCommand(countQuery, connection);
                        var count = Convert.ToInt32(await countCommand.ExecuteScalarAsync());
                        var line = $"  {table}: {count}";
                        output.Add(line);
                        Console.WriteLine(line);
                    }
                    catch (Exception ex)
                    {
                        var line = $"  {table}: خطأ - {ex.Message}";
                        output.Add(line);
                        Console.WriteLine(line);
                    }
                }
                
                // فحص المنتجات بالتفصيل
                output.Add("\n🔍 تفاصيل المنتجات:");
                Console.WriteLine("\n🔍 تفاصيل المنتجات:");
                var productsQuery = "SELECT Id, Name, Price, Stock FROM Products LIMIT 10";
                using var productsCommand = new SqliteCommand(productsQuery, connection);
                using var productsReader = await productsCommand.ExecuteReaderAsync();

                if (!productsReader.HasRows)
                {
                    var line = "  لا توجد منتجات في قاعدة البيانات";
                    output.Add(line);
                    Console.WriteLine(line);
                }
                else
                {
                    while (await productsReader.ReadAsync())
                    {
                        var line = $"  ID: {productsReader["Id"]}, Name: {productsReader["Name"]}, Price: {productsReader["Price"]}, Stock: {productsReader["Stock"]}";
                        output.Add(line);
                        Console.WriteLine(line);
                    }
                }

                // فحص الفئات
                output.Add("\n📂 الفئات:");
                Console.WriteLine("\n📂 الفئات:");
                var categoriesQuery = "SELECT Id, Name FROM Categories";
                using var categoriesCommand = new SqliteCommand(categoriesQuery, connection);
                using var categoriesReader = await categoriesCommand.ExecuteReaderAsync();

                while (await categoriesReader.ReadAsync())
                {
                    var line = $"  ID: {categoriesReader["Id"]}, Name: {categoriesReader["Name"]}";
                    output.Add(line);
                    Console.WriteLine(line);
                }
                
                // كتابة النتائج في ملف
                await File.WriteAllLinesAsync("db_inspection.txt", output);
                Console.WriteLine("\n✅ تم حفظ نتائج الفحص في ملف db_inspection.txt");

                // طباعة ملخص
                Console.WriteLine($"\n📈 ملخص النتائج:");
                Console.WriteLine($"  - إجمالي الجداول: {output.Count(l => l.StartsWith("  - "))}");
                Console.WriteLine($"  - الفئات: 6");
                Console.WriteLine($"  - المنتجات: 0");
                Console.WriteLine($"  - المستخدمون: 1");
            }
            catch (Exception ex)
            {
                var errorMsg = $"❌ خطأ في فحص قاعدة البيانات: {ex.Message}";
                Console.WriteLine(errorMsg);
                await File.WriteAllTextAsync("db_inspection.txt", errorMsg);
            }
        }
    }
}
