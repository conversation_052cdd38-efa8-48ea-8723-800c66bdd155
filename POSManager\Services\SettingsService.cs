using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;
using System.Text.Json;

namespace POSManager.Services
{
    public static class SettingsService
    {
        private static ApplicationSettings? _cachedSettings;
        private static readonly object _lock = new object();

        #region Public Methods

        /// <summary>
        /// الحصول على جميع الإعدادات
        /// </summary>
        public static async Task<ApplicationSettings> GetSettingsAsync()
        {
            if (_cachedSettings != null)
                return _cachedSettings;

            lock (_lock)
            {
                if (_cachedSettings != null)
                    return _cachedSettings;

                _cachedSettings = new ApplicationSettings();
            }

            await LoadSettingsAsync();
            return _cachedSettings;
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        public static async Task<bool> SaveSettingsAsync(ApplicationSettings settings, string updatedBy)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // حفظ إعدادات النظام
                    await SaveSystemSettingsAsync(connection, settings.System);
                    
                    // حفظ إعدادات الطباعة
                    await SavePrintSettingsAsync(connection, settings.Print);
                    
                    // حفظ إعدادات الأمان
                    await SaveSecuritySettingsAsync(connection, settings.Security);
                    
                    // حفظ إعدادات النسخ الاحتياطي
                    await SaveBackupSettingsAsync(connection, settings.Backup);
                    
                    // حفظ إعدادات الواجهة
                    await SaveUISettingsAsync(connection, settings.UI);

                    transaction.Commit();

                    // تحديث الكاش
                    settings.LastUpdated = DateTime.Now;
                    settings.UpdatedBy = updatedBy;
                    _cachedSettings = settings;

                    // تسجيل النشاط
                    // await ActivityLogService.LogActivityAsync("تحديث الإعدادات",
                    //     "تم تحديث إعدادات النظام", updatedBy);

                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                // await ActivityLogService.LogActivityAsync("خطأ في حفظ الإعدادات",
                //     $"فشل في حفظ الإعدادات: {ex.Message}", updatedBy);
                return false;
            }
        }

        /// <summary>
        /// إعادة تحميل الإعدادات من قاعدة البيانات
        /// </summary>
        public static async Task RefreshSettingsAsync()
        {
            _cachedSettings = null;
            await GetSettingsAsync();
        }

        /// <summary>
        /// إعادة تعيين الإعدادات للقيم الافتراضية
        /// </summary>
        public static async Task<bool> ResetToDefaultAsync(string updatedBy)
        {
            try
            {
                var defaultSettings = new ApplicationSettings();
                return await SaveSettingsAsync(defaultSettings, updatedBy);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تصدير الإعدادات إلى ملف JSON
        /// </summary>
        public static async Task<bool> ExportSettingsAsync(string filePath)
        {
            try
            {
                var settings = await GetSettingsAsync();
                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// استيراد الإعدادات من ملف JSON
        /// </summary>
        public static async Task<bool> ImportSettingsAsync(string filePath, string updatedBy)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var json = await File.ReadAllTextAsync(filePath);
                var settings = JsonSerializer.Deserialize<ApplicationSettings>(json);
                
                if (settings != null)
                {
                    return await SaveSettingsAsync(settings, updatedBy);
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// تحميل الإعدادات من قاعدة البيانات
        /// </summary>
        private static async Task LoadSettingsAsync()
        {
            if (_cachedSettings == null)
                _cachedSettings = new ApplicationSettings();

            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            // تحميل إعدادات النظام
            _cachedSettings.System = await LoadSystemSettingsAsync(connection);
            
            // تحميل إعدادات الطباعة
            _cachedSettings.Print = await LoadPrintSettingsAsync(connection);
            
            // تحميل إعدادات الأمان
            _cachedSettings.Security = await LoadSecuritySettingsAsync(connection);
            
            // تحميل إعدادات النسخ الاحتياطي
            _cachedSettings.Backup = await LoadBackupSettingsAsync(connection);
            
            // تحميل إعدادات الواجهة
            _cachedSettings.UI = await LoadUISettingsAsync(connection);
        }

        /// <summary>
        /// تحميل إعدادات النظام
        /// </summary>
        private static async Task<SystemSettings> LoadSystemSettingsAsync(SqliteConnection connection)
        {
            var settings = new SystemSettings();
            
            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT CompanyName, CompanyAddress, CompanyPhone, CompanyEmail, 
                       TaxNumber, TaxRate, Currency, CurrencySymbol, IsRTL, Language
                FROM SystemSettings 
                WHERE Id = 1";

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                settings.CompanyName = Convert.ToString(reader[0]) ?? "";
                settings.CompanyAddress = Convert.ToString(reader[1]) ?? "";
                settings.CompanyPhone = Convert.ToString(reader[2]) ?? "";
                settings.CompanyEmail = Convert.ToString(reader[3]) ?? "";
                settings.TaxNumber = Convert.ToString(reader[4]) ?? "";
                settings.TaxRate = Convert.ToDecimal(reader[5]);
                settings.Currency = Convert.ToString(reader[6]) ?? "ريال";
                settings.CurrencySymbol = Convert.ToString(reader[7]) ?? "ر.س";
                settings.IsRTL = Convert.ToBoolean(reader[8]);
                settings.Language = Convert.ToString(reader[9]) ?? "ar-SA";
            }

            return settings;
        }

        /// <summary>
        /// حفظ إعدادات النظام
        /// </summary>
        private static async Task SaveSystemSettingsAsync(SqliteConnection connection, SystemSettings settings)
        {
            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT OR REPLACE INTO SystemSettings 
                (Id, CompanyName, CompanyAddress, CompanyPhone, CompanyEmail, 
                 TaxNumber, TaxRate, Currency, CurrencySymbol, IsRTL, Language, UpdatedAt)
                VALUES 
                (1, @CompanyName, @CompanyAddress, @CompanyPhone, @CompanyEmail, 
                 @TaxNumber, @TaxRate, @Currency, @CurrencySymbol, @IsRTL, @Language, @UpdatedAt)";

            command.Parameters.AddWithValue("@CompanyName", settings.CompanyName);
            command.Parameters.AddWithValue("@CompanyAddress", settings.CompanyAddress);
            command.Parameters.AddWithValue("@CompanyPhone", settings.CompanyPhone);
            command.Parameters.AddWithValue("@CompanyEmail", settings.CompanyEmail);
            command.Parameters.AddWithValue("@TaxNumber", settings.TaxNumber);
            command.Parameters.AddWithValue("@TaxRate", settings.TaxRate);
            command.Parameters.AddWithValue("@Currency", settings.Currency);
            command.Parameters.AddWithValue("@CurrencySymbol", settings.CurrencySymbol);
            command.Parameters.AddWithValue("@IsRTL", settings.IsRTL);
            command.Parameters.AddWithValue("@Language", settings.Language);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// تحميل إعدادات الطباعة
        /// </summary>
        private static async Task<PrintSettings> LoadPrintSettingsAsync(SqliteConnection connection)
        {
            var settings = new PrintSettings();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT DefaultPrinterName, PrinterType, PaperSize, AutoPrint, PrintLogo,
                       PrintHeader, PrintFooter, HeaderText, FooterText, CopiesCount,
                       PrintBarcode, LogoPath
                FROM PrintSettings
                WHERE Id = 1";

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                settings.DefaultPrinterName = Convert.ToString(reader[0]) ?? "";
                settings.PrinterType = (PrinterType)Convert.ToInt32(reader[1]);
                settings.PaperSize = (SettingsPaperSize)Convert.ToInt32(reader[2]);
                settings.AutoPrint = Convert.ToBoolean(reader[3]);
                settings.PrintLogo = Convert.ToBoolean(reader[4]);
                settings.PrintHeader = Convert.ToBoolean(reader[5]);
                settings.PrintFooter = Convert.ToBoolean(reader[6]);
                settings.HeaderText = Convert.ToString(reader[7]) ?? "";
                settings.FooterText = Convert.ToString(reader[8]) ?? "شكراً لزيارتكم";
                settings.CopiesCount = Convert.ToInt32(reader[9]);
                settings.PrintBarcode = Convert.ToBoolean(reader[10]);
                settings.LogoPath = Convert.ToString(reader[11]) ?? "";
            }

            return settings;
        }

        /// <summary>
        /// حفظ إعدادات الطباعة
        /// </summary>
        private static async Task SavePrintSettingsAsync(SqliteConnection connection, PrintSettings settings)
        {
            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT OR REPLACE INTO PrintSettings
                (Id, DefaultPrinterName, PrinterType, PaperSize, AutoPrint, PrintLogo,
                 PrintHeader, PrintFooter, HeaderText, FooterText, CopiesCount,
                 PrintBarcode, LogoPath, UpdatedAt)
                VALUES
                (1, @DefaultPrinterName, @PrinterType, @PaperSize, @AutoPrint, @PrintLogo,
                 @PrintHeader, @PrintFooter, @HeaderText, @FooterText, @CopiesCount,
                 @PrintBarcode, @LogoPath, @UpdatedAt)";

            command.Parameters.AddWithValue("@DefaultPrinterName", settings.DefaultPrinterName);
            command.Parameters.AddWithValue("@PrinterType", (int)settings.PrinterType);
            command.Parameters.AddWithValue("@PaperSize", (int)settings.PaperSize);
            command.Parameters.AddWithValue("@AutoPrint", settings.AutoPrint);
            command.Parameters.AddWithValue("@PrintLogo", settings.PrintLogo);
            command.Parameters.AddWithValue("@PrintHeader", settings.PrintHeader);
            command.Parameters.AddWithValue("@PrintFooter", settings.PrintFooter);
            command.Parameters.AddWithValue("@HeaderText", settings.HeaderText);
            command.Parameters.AddWithValue("@FooterText", settings.FooterText);
            command.Parameters.AddWithValue("@CopiesCount", settings.CopiesCount);
            command.Parameters.AddWithValue("@PrintBarcode", settings.PrintBarcode);
            command.Parameters.AddWithValue("@LogoPath", settings.LogoPath);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// تحميل إعدادات الأمان
        /// </summary>
        private static async Task<SecuritySettings> LoadSecuritySettingsAsync(SqliteConnection connection)
        {
            var settings = new SecuritySettings();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT RequireStrongPassword, MinPasswordLength, MaxLoginAttempts,
                       LockoutDurationMinutes, EnableActivityLog, RequirePasswordChange,
                       PasswordExpiryDays, EnableTwoFactorAuth, AutoLogoutEnabled, AutoLogoutMinutes
                FROM SecuritySettings
                WHERE Id = 1";

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                settings.RequireStrongPassword = Convert.ToBoolean(reader[0]);
                settings.MinPasswordLength = Convert.ToInt32(reader[1]);
                settings.MaxLoginAttempts = Convert.ToInt32(reader[2]);
                settings.LockoutDurationMinutes = Convert.ToInt32(reader[3]);
                settings.EnableActivityLog = Convert.ToBoolean(reader[4]);
                settings.RequirePasswordChange = Convert.ToBoolean(reader[5]);
                settings.PasswordExpiryDays = Convert.ToInt32(reader[6]);
                settings.EnableTwoFactorAuth = Convert.ToBoolean(reader[7]);
                settings.AutoLogoutEnabled = Convert.ToBoolean(reader[8]);
                settings.AutoLogoutMinutes = Convert.ToInt32(reader[9]);
            }

            return settings;
        }

        /// <summary>
        /// حفظ إعدادات الأمان
        /// </summary>
        private static async Task SaveSecuritySettingsAsync(SqliteConnection connection, SecuritySettings settings)
        {
            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT OR REPLACE INTO SecuritySettings
                (Id, RequireStrongPassword, MinPasswordLength, MaxLoginAttempts,
                 LockoutDurationMinutes, EnableActivityLog, RequirePasswordChange,
                 PasswordExpiryDays, EnableTwoFactorAuth, AutoLogoutEnabled, AutoLogoutMinutes, UpdatedAt)
                VALUES
                (1, @RequireStrongPassword, @MinPasswordLength, @MaxLoginAttempts,
                 @LockoutDurationMinutes, @EnableActivityLog, @RequirePasswordChange,
                 @PasswordExpiryDays, @EnableTwoFactorAuth, @AutoLogoutEnabled, @AutoLogoutMinutes, @UpdatedAt)";

            command.Parameters.AddWithValue("@RequireStrongPassword", settings.RequireStrongPassword);
            command.Parameters.AddWithValue("@MinPasswordLength", settings.MinPasswordLength);
            command.Parameters.AddWithValue("@MaxLoginAttempts", settings.MaxLoginAttempts);
            command.Parameters.AddWithValue("@LockoutDurationMinutes", settings.LockoutDurationMinutes);
            command.Parameters.AddWithValue("@EnableActivityLog", settings.EnableActivityLog);
            command.Parameters.AddWithValue("@RequirePasswordChange", settings.RequirePasswordChange);
            command.Parameters.AddWithValue("@PasswordExpiryDays", settings.PasswordExpiryDays);
            command.Parameters.AddWithValue("@EnableTwoFactorAuth", settings.EnableTwoFactorAuth);
            command.Parameters.AddWithValue("@AutoLogoutEnabled", settings.AutoLogoutEnabled);
            command.Parameters.AddWithValue("@AutoLogoutMinutes", settings.AutoLogoutMinutes);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// تحميل إعدادات النسخ الاحتياطي
        /// </summary>
        private static async Task<BackupSettings> LoadBackupSettingsAsync(SqliteConnection connection)
        {
            var settings = new BackupSettings();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT AutoBackupEnabled, BackupFrequency, BackupPath, RetentionDays,
                       CompressBackup, BackupOnExit, LastBackupDate, LastBackupFile,
                       EmailBackup, BackupEmail
                FROM BackupSettings
                WHERE Id = 1";

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                settings.AutoBackupEnabled = Convert.ToBoolean(reader[0]);
                settings.BackupFrequency = (BackupFrequency)Convert.ToInt32(reader[1]);
                settings.BackupPath = Convert.ToString(reader[2]) ?? "";
                settings.RetentionDays = Convert.ToInt32(reader[3]);
                settings.CompressBackup = Convert.ToBoolean(reader[4]);
                settings.BackupOnExit = Convert.ToBoolean(reader[5]);
                settings.LastBackupDate = reader.IsDBNull(6) ? DateTime.MinValue : DateTime.Parse(Convert.ToString(reader[6]) ?? "");
                settings.LastBackupFile = Convert.ToString(reader[7]) ?? "";
                settings.EmailBackup = Convert.ToBoolean(reader[8]);
                settings.BackupEmail = Convert.ToString(reader[9]) ?? "";
            }

            return settings;
        }

        /// <summary>
        /// حفظ إعدادات النسخ الاحتياطي
        /// </summary>
        private static async Task SaveBackupSettingsAsync(SqliteConnection connection, BackupSettings settings)
        {
            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT OR REPLACE INTO BackupSettings
                (Id, AutoBackupEnabled, BackupFrequency, BackupPath, RetentionDays,
                 CompressBackup, BackupOnExit, LastBackupDate, LastBackupFile,
                 EmailBackup, BackupEmail, UpdatedAt)
                VALUES
                (1, @AutoBackupEnabled, @BackupFrequency, @BackupPath, @RetentionDays,
                 @CompressBackup, @BackupOnExit, @LastBackupDate, @LastBackupFile,
                 @EmailBackup, @BackupEmail, @UpdatedAt)";

            command.Parameters.AddWithValue("@AutoBackupEnabled", settings.AutoBackupEnabled);
            command.Parameters.AddWithValue("@BackupFrequency", (int)settings.BackupFrequency);
            command.Parameters.AddWithValue("@BackupPath", settings.BackupPath);
            command.Parameters.AddWithValue("@RetentionDays", settings.RetentionDays);
            command.Parameters.AddWithValue("@CompressBackup", settings.CompressBackup);
            command.Parameters.AddWithValue("@BackupOnExit", settings.BackupOnExit);
            command.Parameters.AddWithValue("@LastBackupDate", settings.LastBackupDate == DateTime.MinValue ? DBNull.Value : settings.LastBackupDate);
            command.Parameters.AddWithValue("@LastBackupFile", settings.LastBackupFile);
            command.Parameters.AddWithValue("@EmailBackup", settings.EmailBackup);
            command.Parameters.AddWithValue("@BackupEmail", settings.BackupEmail);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// تحميل إعدادات الواجهة
        /// </summary>
        private static async Task<UISettings> LoadUISettingsAsync(SqliteConnection connection)
        {
            var settings = new UISettings();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT Theme, PrimaryColor, SecondaryColor, FontSize, FontFamily,
                       ShowWelcomeScreen, ShowTips, EnableSounds, EnableAnimations,
                       ShowStatusBar, ShowToolbar
                FROM UISettings
                WHERE Id = 1";

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                settings.Theme = Convert.ToString(reader[0]) ?? "Default";
                settings.PrimaryColor = Convert.ToString(reader[1]) ?? "#2196F3";
                settings.SecondaryColor = Convert.ToString(reader[2]) ?? "#FFC107";
                settings.FontSize = Convert.ToInt32(reader[3]);
                settings.FontFamily = Convert.ToString(reader[4]) ?? "Tahoma";
                settings.ShowWelcomeScreen = Convert.ToBoolean(reader[5]);
                settings.ShowTips = Convert.ToBoolean(reader[6]);
                settings.EnableSounds = Convert.ToBoolean(reader[7]);
                settings.EnableAnimations = Convert.ToBoolean(reader[8]);
                settings.ShowStatusBar = Convert.ToBoolean(reader[9]);
                settings.ShowToolbar = Convert.ToBoolean(reader[10]);
            }

            return settings;
        }

        /// <summary>
        /// حفظ إعدادات الواجهة
        /// </summary>
        private static async Task SaveUISettingsAsync(SqliteConnection connection, UISettings settings)
        {
            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT OR REPLACE INTO UISettings
                (Id, Theme, PrimaryColor, SecondaryColor, FontSize, FontFamily,
                 ShowWelcomeScreen, ShowTips, EnableSounds, EnableAnimations,
                 ShowStatusBar, ShowToolbar, UpdatedAt)
                VALUES
                (1, @Theme, @PrimaryColor, @SecondaryColor, @FontSize, @FontFamily,
                 @ShowWelcomeScreen, @ShowTips, @EnableSounds, @EnableAnimations,
                 @ShowStatusBar, @ShowToolbar, @UpdatedAt)";

            command.Parameters.AddWithValue("@Theme", settings.Theme);
            command.Parameters.AddWithValue("@PrimaryColor", settings.PrimaryColor);
            command.Parameters.AddWithValue("@SecondaryColor", settings.SecondaryColor);
            command.Parameters.AddWithValue("@FontSize", settings.FontSize);
            command.Parameters.AddWithValue("@FontFamily", settings.FontFamily);
            command.Parameters.AddWithValue("@ShowWelcomeScreen", settings.ShowWelcomeScreen);
            command.Parameters.AddWithValue("@ShowTips", settings.ShowTips);
            command.Parameters.AddWithValue("@EnableSounds", settings.EnableSounds);
            command.Parameters.AddWithValue("@EnableAnimations", settings.EnableAnimations);
            command.Parameters.AddWithValue("@ShowStatusBar", settings.ShowStatusBar);
            command.Parameters.AddWithValue("@ShowToolbar", settings.ShowToolbar);
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        #endregion
    }
}
