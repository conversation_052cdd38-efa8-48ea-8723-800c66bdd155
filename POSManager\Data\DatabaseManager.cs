using Microsoft.Data.Sqlite;
using System.Security.Cryptography;
using System.Text;

namespace POSManager.Data
{
    public class DatabaseManager
    {
        private static readonly string ConnectionString = "Data Source=posmanager.db";
        
        public static void InitializeDatabase()
        {
            using var connection = new SqliteConnection(ConnectionString);
            connection.Open();
            
            CreateTables(connection);
            CreateDefaultAdmin(connection);
        }
        
        public static string GetConnectionString()
        {
            return ConnectionString;
        }
        
        private static void CreateTables(SqliteConnection connection)
        {
            // جدول المستخدمين
            var createUsersTable = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT UNIQUE NOT NULL,
                    PasswordHash TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Role TEXT NOT NULL CHECK(Role IN ('Admin', 'Cashier')),
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastLogin DATETIME
                )";
            
            // جدول التصنيفات
            var createCategoriesTable = @"
                CREATE TABLE IF NOT EXISTS Categories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول المنتجات
            var createProductsTable = @"
                CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Code TEXT UNIQUE,
                    Barcode TEXT UNIQUE,
                    CategoryId INTEGER,
                    Price DECIMAL(10,2) NOT NULL,
                    Cost DECIMAL(10,2) DEFAULT 0,
                    Quantity INTEGER NOT NULL DEFAULT 0,
                    MinimumQuantity INTEGER DEFAULT 5,
                    Description TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                )";
            
            // جدول العملاء
            var createCustomersTable = @"
                CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول الموردين
            var createSuppliersTable = @"
                CREATE TABLE IF NOT EXISTS Suppliers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول الفواتير
            var createSalesTable = @"
                CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT UNIQUE NOT NULL,
                    CustomerId INTEGER,
                    UserId INTEGER NOT NULL,
                    SubTotal DECIMAL(10,2) NOT NULL,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    DiscountPercentage DECIMAL(5,2) DEFAULT 0,
                    TaxAmount DECIMAL(10,2) DEFAULT 0,
                    TaxPercentage DECIMAL(5,2) DEFAULT 0,
                    TotalAmount DECIMAL(10,2) NOT NULL,
                    PaidAmount DECIMAL(10,2) NOT NULL,
                    ChangeAmount DECIMAL(10,2) DEFAULT 0,
                    PaymentMethod TEXT DEFAULT 'Cash',
                    Notes TEXT,
                    SaleDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // جدول تفاصيل الفواتير
            var createSaleDetailsTable = @"
                CREATE TABLE IF NOT EXISTS SaleDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SaleId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    ProductName TEXT NOT NULL,
                    ProductCode TEXT,
                    Quantity INTEGER NOT NULL,
                    UnitPrice DECIMAL(10,2) NOT NULL,
                    TotalPrice DECIMAL(10,2) NOT NULL,
                    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";
            
            // جدول المصروفات
            var createExpensesTable = @"
                CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Description TEXT NOT NULL,
                    Amount DECIMAL(10,2) NOT NULL,
                    Category TEXT,
                    UserId INTEGER NOT NULL,
                    ExpenseDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    Notes TEXT,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // جدول الإعدادات
            var createSettingsTable = @"
                CREATE TABLE IF NOT EXISTS Settings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Key TEXT UNIQUE NOT NULL,
                    Value TEXT,
                    Description TEXT,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول سجل المخزون
            var createInventoryLogTable = @"
                CREATE TABLE IF NOT EXISTS InventoryLog (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    ProductName TEXT NOT NULL,
                    ChangeType TEXT NOT NULL CHECK(ChangeType IN ('Sale', 'Purchase', 'Adjustment', 'Return')),
                    OldQuantity INTEGER NOT NULL,
                    NewQuantity INTEGER NOT NULL,
                    QuantityChanged INTEGER NOT NULL,
                    UserId INTEGER NOT NULL,
                    Reason TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // جدول سجل النشاطات
            var createActivityLogTable = @"
                CREATE TABLE IF NOT EXISTS ActivityLog (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    UserName TEXT NOT NULL,
                    Action TEXT NOT NULL,
                    TableName TEXT,
                    RecordId INTEGER,
                    Details TEXT,
                    IPAddress TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // تنفيذ إنشاء الجداول
            ExecuteCommand(connection, createUsersTable);
            ExecuteCommand(connection, createCategoriesTable);
            ExecuteCommand(connection, createProductsTable);
            ExecuteCommand(connection, createCustomersTable);
            ExecuteCommand(connection, createSuppliersTable);
            ExecuteCommand(connection, createSalesTable);
            ExecuteCommand(connection, createSaleDetailsTable);
            ExecuteCommand(connection, createExpensesTable);
            ExecuteCommand(connection, createSettingsTable);
            ExecuteCommand(connection, createInventoryLogTable);
            ExecuteCommand(connection, createActivityLogTable);
            
            // إنشاء الفهارس
            CreateIndexes(connection);
        }
        
        private static void CreateIndexes(SqliteConnection connection)
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_products_code ON Products(Code)",
                "CREATE INDEX IF NOT EXISTS idx_products_barcode ON Products(Barcode)",
                "CREATE INDEX IF NOT EXISTS idx_products_category ON Products(CategoryId)",
                "CREATE INDEX IF NOT EXISTS idx_sales_date ON Sales(SaleDate)",
                "CREATE INDEX IF NOT EXISTS idx_sales_invoice ON Sales(InvoiceNumber)",
                "CREATE INDEX IF NOT EXISTS idx_sales_user ON Sales(UserId)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_product ON InventoryLog(ProductId)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_date ON InventoryLog(CreatedAt)",
                "CREATE INDEX IF NOT EXISTS idx_activity_user ON ActivityLog(UserId)",
                "CREATE INDEX IF NOT EXISTS idx_activity_date ON ActivityLog(CreatedAt)"
            };
            
            foreach (var index in indexes)
            {
                ExecuteCommand(connection, index);
            }
        }
        
        private static void CreateDefaultAdmin(SqliteConnection connection)
        {
            // التحقق من وجود مستخدم مدير
            var checkAdminQuery = "SELECT COUNT(*) FROM Users WHERE Role = 'Admin'";
            using var checkCommand = new SqliteCommand(checkAdminQuery, connection);
            var adminCount = Convert.ToInt32(checkCommand.ExecuteScalar());
            
            if (adminCount == 0)
            {
                // إنشاء مستخدم مدير افتراضي
                var defaultPassword = "admin123";
                var hashedPassword = HashPassword(defaultPassword);
                
                var insertAdminQuery = @"
                    INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive)
                    VALUES (@username, @password, @fullname, @role, 1)";
                
                using var insertCommand = new SqliteCommand(insertAdminQuery, connection);
                insertCommand.Parameters.AddWithValue("@username", "admin");
                insertCommand.Parameters.AddWithValue("@password", hashedPassword);
                insertCommand.Parameters.AddWithValue("@fullname", "مدير النظام");
                insertCommand.Parameters.AddWithValue("@role", "Admin");
                insertCommand.ExecuteNonQuery();
                
                // إضافة الإعدادات الافتراضية
                InsertDefaultSettings(connection);
            }
        }
        
        private static void InsertDefaultSettings(SqliteConnection connection)
        {
            var defaultSettings = new Dictionary<string, string>
            {
                {"StoreName", "متجر نقطة البيع"},
                {"StoreAddress", "العنوان"},
                {"StorePhone", "الهاتف"},
                {"TaxPercentage", "15"},
                {"Currency", "ريال"},
                {"PrinterType", "A4"},
                {"AutoLockMinutes", "30"},
                {"BackupEnabled", "true"},
                {"InvoicePrefix", "INV"},
                {"ReceiptFooter", "شكراً لزيارتكم"}
            };
            
            foreach (var setting in defaultSettings)
            {
                var insertSettingQuery = @"
                    INSERT OR IGNORE INTO Settings (Key, Value, Description)
                    VALUES (@key, @value, @description)";
                
                using var command = new SqliteCommand(insertSettingQuery, connection);
                command.Parameters.AddWithValue("@key", setting.Key);
                command.Parameters.AddWithValue("@value", setting.Value);
                command.Parameters.AddWithValue("@description", GetSettingDescription(setting.Key));
                command.ExecuteNonQuery();
            }
        }
        
        private static string GetSettingDescription(string key)
        {
            return key switch
            {
                "StoreName" => "اسم المتجر",
                "StoreAddress" => "عنوان المتجر",
                "StorePhone" => "هاتف المتجر",
                "TaxPercentage" => "نسبة الضريبة",
                "Currency" => "العملة",
                "PrinterType" => "نوع الطابعة",
                "AutoLockMinutes" => "دقائق القفل التلقائي",
                "BackupEnabled" => "تفعيل النسخ الاحتياطي",
                "InvoicePrefix" => "بادئة رقم الفاتورة",
                "ReceiptFooter" => "تذييل الفاتورة",
                _ => ""
            };
        }
        
        private static void ExecuteCommand(SqliteConnection connection, string commandText)
        {
            using var command = new SqliteCommand(commandText, connection);
            command.ExecuteNonQuery();
        }
        
        public static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
        
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            var hashedInput = HashPassword(password);
            return hashedInput == hashedPassword;
        }
    }
}
