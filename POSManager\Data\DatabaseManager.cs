using Microsoft.Data.Sqlite;
using System.Security.Cryptography;
using System.Text;

namespace POSManager.Data
{
    public class DatabaseManager
    {
        private static readonly string ConnectionString = "Data Source=posmanager.db";
        private static DatabaseManager? _instance;
        private static readonly object _lock = new object();

        public static DatabaseManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new DatabaseManager();
                    }
                }
                return _instance;
            }
        }

        public static void InitializeDatabase()
        {
            using var connection = new SqliteConnection(ConnectionString);
            connection.Open();

            CreateTables(connection);
            CreateAdvancedInventoryTables(connection);
            CreateDefaultAdmin(connection);
        }

        public static string GetConnectionString()
        {
            return ConnectionString;
        }

        public SqliteConnection GetConnection()
        {
            return new SqliteConnection(ConnectionString);
        }
        
        private static void CreateTables(SqliteConnection connection)
        {
            // جدول المستخدمين
            var createUsersTable = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT UNIQUE NOT NULL,
                    PasswordHash TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Role TEXT NOT NULL CHECK(Role IN ('Admin', 'Cashier')),
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastLogin DATETIME
                )";
            
            // جدول التصنيفات
            var createCategoriesTable = @"
                CREATE TABLE IF NOT EXISTS Categories (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Description TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول المنتجات
            var createProductsTable = @"
                CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Code TEXT UNIQUE,
                    Barcode TEXT UNIQUE,
                    CategoryId INTEGER,
                    PurchasePrice DECIMAL(10,2) DEFAULT 0,
                    SalePrice DECIMAL(10,2) NOT NULL,
                    Stock INTEGER NOT NULL DEFAULT 0,
                    MinStock INTEGER DEFAULT 5,
                    Unit TEXT DEFAULT 'قطعة',
                    Description TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                )";
            
            // جدول العملاء
            var createCustomersTable = @"
                CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول الموردين
            var createSuppliersTable = @"
                CREATE TABLE IF NOT EXISTS Suppliers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول الفواتير
            var createSalesTable = @"
                CREATE TABLE IF NOT EXISTS Sales (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT UNIQUE NOT NULL,
                    CustomerId INTEGER,
                    UserId INTEGER NOT NULL,
                    SubTotal DECIMAL(10,2) NOT NULL,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    DiscountPercentage DECIMAL(5,2) DEFAULT 0,
                    TaxAmount DECIMAL(10,2) DEFAULT 0,
                    TaxPercentage DECIMAL(5,2) DEFAULT 0,
                    TotalAmount DECIMAL(10,2) NOT NULL,
                    PaidAmount DECIMAL(10,2) NOT NULL,
                    ChangeAmount DECIMAL(10,2) DEFAULT 0,
                    PaymentMethod TEXT DEFAULT 'Cash',
                    Notes TEXT,
                    SaleDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // جدول تفاصيل الفواتير
            var createSaleDetailsTable = @"
                CREATE TABLE IF NOT EXISTS SaleDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SaleId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    ProductName TEXT NOT NULL,
                    ProductCode TEXT,
                    Quantity INTEGER NOT NULL,
                    UnitPrice DECIMAL(10,2) NOT NULL,
                    TotalPrice DECIMAL(10,2) NOT NULL,
                    FOREIGN KEY (SaleId) REFERENCES Sales(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";
            
            // جدول المصروفات
            var createExpensesTable = @"
                CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Description TEXT NOT NULL,
                    Amount DECIMAL(10,2) NOT NULL,
                    Category TEXT,
                    UserId INTEGER NOT NULL,
                    ExpenseDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    Notes TEXT,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // جدول الإعدادات
            var createSettingsTable = @"
                CREATE TABLE IF NOT EXISTS Settings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Key TEXT UNIQUE NOT NULL,
                    Value TEXT,
                    Description TEXT,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";
            
            // جدول سجل المخزون
            var createInventoryLogTable = @"
                CREATE TABLE IF NOT EXISTS InventoryLog (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    ProductName TEXT NOT NULL,
                    ChangeType TEXT NOT NULL CHECK(ChangeType IN ('Sale', 'Purchase', 'Adjustment', 'Return')),
                    OldQuantity INTEGER NOT NULL,
                    NewQuantity INTEGER NOT NULL,
                    QuantityChanged INTEGER NOT NULL,
                    UserId INTEGER NOT NULL,
                    Reason TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // جدول سجل النشاطات
            var createActivityLogTable = @"
                CREATE TABLE IF NOT EXISTS ActivityLog (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    UserName TEXT NOT NULL,
                    Action TEXT NOT NULL,
                    TableName TEXT,
                    RecordId INTEGER,
                    Details TEXT,
                    IPAddress TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";
            
            // تنفيذ إنشاء الجداول
            ExecuteCommand(connection, createUsersTable);
            ExecuteCommand(connection, createCategoriesTable);
            ExecuteCommand(connection, createProductsTable);
            ExecuteCommand(connection, createCustomersTable);
            ExecuteCommand(connection, createSuppliersTable);
            ExecuteCommand(connection, createSalesTable);
            ExecuteCommand(connection, createSaleDetailsTable);
            ExecuteCommand(connection, createExpensesTable);
            ExecuteCommand(connection, createSettingsTable);
            ExecuteCommand(connection, createInventoryLogTable);
            ExecuteCommand(connection, createActivityLogTable);

            // إنشاء جداول الإعدادات الجديدة
            CreateSettingsTables(connection);

            // إنشاء الفهارس
            CreateIndexes(connection);

            // تحديث الجداول الموجودة
            UpdateExistingTables(connection);
        }

        private static void CreateSettingsTables(SqliteConnection connection)
        {
            // جدول إعدادات النظام
            var createSystemSettingsTable = @"
                CREATE TABLE IF NOT EXISTS SystemSettings (
                    Id INTEGER PRIMARY KEY DEFAULT 1,
                    CompanyName TEXT NOT NULL DEFAULT '',
                    CompanyAddress TEXT NOT NULL DEFAULT '',
                    CompanyPhone TEXT NOT NULL DEFAULT '',
                    CompanyEmail TEXT NOT NULL DEFAULT '',
                    TaxNumber TEXT NOT NULL DEFAULT '',
                    TaxRate DECIMAL NOT NULL DEFAULT 0.15,
                    Currency TEXT NOT NULL DEFAULT 'ريال',
                    CurrencySymbol TEXT NOT NULL DEFAULT 'ر.س',
                    IsRTL INTEGER NOT NULL DEFAULT 1,
                    Language TEXT NOT NULL DEFAULT 'ar-SA',
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            // جدول إعدادات الطباعة
            var createPrintSettingsTable = @"
                CREATE TABLE IF NOT EXISTS PrintSettings (
                    Id INTEGER PRIMARY KEY DEFAULT 1,
                    DefaultPrinterName TEXT NOT NULL DEFAULT '',
                    PrinterType INTEGER NOT NULL DEFAULT 1,
                    PaperSize INTEGER NOT NULL DEFAULT 1,
                    AutoPrint INTEGER NOT NULL DEFAULT 0,
                    PrintLogo INTEGER NOT NULL DEFAULT 1,
                    PrintHeader INTEGER NOT NULL DEFAULT 1,
                    PrintFooter INTEGER NOT NULL DEFAULT 1,
                    HeaderText TEXT NOT NULL DEFAULT '',
                    FooterText TEXT NOT NULL DEFAULT 'شكراً لزيارتكم',
                    CopiesCount INTEGER NOT NULL DEFAULT 1,
                    PrintBarcode INTEGER NOT NULL DEFAULT 0,
                    LogoPath TEXT NOT NULL DEFAULT '',
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            // جدول إعدادات الأمان
            var createSecuritySettingsTable = @"
                CREATE TABLE IF NOT EXISTS SecuritySettings (
                    Id INTEGER PRIMARY KEY DEFAULT 1,
                    RequireStrongPassword INTEGER NOT NULL DEFAULT 1,
                    MinPasswordLength INTEGER NOT NULL DEFAULT 8,
                    MaxLoginAttempts INTEGER NOT NULL DEFAULT 3,
                    LockoutDurationMinutes INTEGER NOT NULL DEFAULT 15,
                    EnableActivityLog INTEGER NOT NULL DEFAULT 1,
                    RequirePasswordChange INTEGER NOT NULL DEFAULT 0,
                    PasswordExpiryDays INTEGER NOT NULL DEFAULT 90,
                    EnableTwoFactorAuth INTEGER NOT NULL DEFAULT 0,
                    AutoLogoutEnabled INTEGER NOT NULL DEFAULT 1,
                    AutoLogoutMinutes INTEGER NOT NULL DEFAULT 30,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            // جدول إعدادات النسخ الاحتياطي
            var createBackupSettingsTable = @"
                CREATE TABLE IF NOT EXISTS BackupSettings (
                    Id INTEGER PRIMARY KEY DEFAULT 1,
                    AutoBackupEnabled INTEGER NOT NULL DEFAULT 1,
                    BackupFrequency INTEGER NOT NULL DEFAULT 2,
                    BackupPath TEXT NOT NULL DEFAULT '',
                    RetentionDays INTEGER NOT NULL DEFAULT 30,
                    CompressBackup INTEGER NOT NULL DEFAULT 1,
                    BackupOnExit INTEGER NOT NULL DEFAULT 1,
                    LastBackupDate DATETIME,
                    LastBackupFile TEXT NOT NULL DEFAULT '',
                    EmailBackup INTEGER NOT NULL DEFAULT 0,
                    BackupEmail TEXT NOT NULL DEFAULT '',
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            // جدول إعدادات الواجهة
            var createUISettingsTable = @"
                CREATE TABLE IF NOT EXISTS UISettings (
                    Id INTEGER PRIMARY KEY DEFAULT 1,
                    Theme TEXT NOT NULL DEFAULT 'Default',
                    PrimaryColor TEXT NOT NULL DEFAULT '#2196F3',
                    SecondaryColor TEXT NOT NULL DEFAULT '#FFC107',
                    FontSize INTEGER NOT NULL DEFAULT 12,
                    FontFamily TEXT NOT NULL DEFAULT 'Tahoma',
                    ShowWelcomeScreen INTEGER NOT NULL DEFAULT 1,
                    ShowTips INTEGER NOT NULL DEFAULT 1,
                    EnableSounds INTEGER NOT NULL DEFAULT 1,
                    EnableAnimations INTEGER NOT NULL DEFAULT 1,
                    ShowStatusBar INTEGER NOT NULL DEFAULT 1,
                    ShowToolbar INTEGER NOT NULL DEFAULT 1,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            // تنفيذ إنشاء الجداول
            ExecuteCommand(connection, createSystemSettingsTable);
            ExecuteCommand(connection, createPrintSettingsTable);
            ExecuteCommand(connection, createSecuritySettingsTable);
            ExecuteCommand(connection, createBackupSettingsTable);
            ExecuteCommand(connection, createUISettingsTable);

            // إدراج القيم الافتراضية
            InsertDefaultSettings(connection);
        }

        private static void InsertDefaultSettings(SqliteConnection connection)
        {
            // إدراج الإعدادات الافتراضية إذا لم تكن موجودة
            var insertDefaultSystemSettings = @"
                INSERT OR IGNORE INTO SystemSettings (Id) VALUES (1)";

            var insertDefaultPrintSettings = @"
                INSERT OR IGNORE INTO PrintSettings (Id) VALUES (1)";

            var insertDefaultSecuritySettings = @"
                INSERT OR IGNORE INTO SecuritySettings (Id) VALUES (1)";

            var insertDefaultBackupSettings = @"
                INSERT OR IGNORE INTO BackupSettings (Id) VALUES (1)";

            var insertDefaultUISettings = @"
                INSERT OR IGNORE INTO UISettings (Id) VALUES (1)";

            ExecuteCommand(connection, insertDefaultSystemSettings);
            ExecuteCommand(connection, insertDefaultPrintSettings);
            ExecuteCommand(connection, insertDefaultSecuritySettings);
            ExecuteCommand(connection, insertDefaultBackupSettings);
            ExecuteCommand(connection, insertDefaultUISettings);
        }

        private static void CreateIndexes(SqliteConnection connection)
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_products_code ON Products(Code)",
                "CREATE INDEX IF NOT EXISTS idx_products_barcode ON Products(Barcode)",
                "CREATE INDEX IF NOT EXISTS idx_products_category ON Products(CategoryId)",
                "CREATE INDEX IF NOT EXISTS idx_sales_date ON Sales(SaleDate)",
                "CREATE INDEX IF NOT EXISTS idx_sales_invoice ON Sales(InvoiceNumber)",
                "CREATE INDEX IF NOT EXISTS idx_sales_user ON Sales(UserId)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_product ON InventoryLog(ProductId)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_date ON InventoryLog(CreatedAt)",
                "CREATE INDEX IF NOT EXISTS idx_activity_user ON ActivityLog(UserId)",
                "CREATE INDEX IF NOT EXISTS idx_activity_date ON ActivityLog(CreatedAt)"
            };
            
            foreach (var index in indexes)
            {
                ExecuteCommand(connection, index);
            }
        }
        
        private static void CreateDefaultAdmin(SqliteConnection connection)
        {
            // التحقق من وجود مستخدم مدير
            var checkAdminQuery = "SELECT COUNT(*) FROM Users WHERE Role = 'Admin'";
            using var checkCommand = new SqliteCommand(checkAdminQuery, connection);
            var adminCount = Convert.ToInt32(checkCommand.ExecuteScalar());
            
            if (adminCount == 0)
            {
                // إنشاء مستخدم مدير افتراضي
                var defaultPassword = "admin123";
                var hashedPassword = HashPassword(defaultPassword);
                
                var insertAdminQuery = @"
                    INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive)
                    VALUES (@username, @password, @fullname, @role, 1)";
                
                using var insertCommand = new SqliteCommand(insertAdminQuery, connection);
                insertCommand.Parameters.AddWithValue("@username", "admin");
                insertCommand.Parameters.AddWithValue("@password", hashedPassword);
                insertCommand.Parameters.AddWithValue("@fullname", "مدير النظام");
                insertCommand.Parameters.AddWithValue("@role", "Admin");
                insertCommand.ExecuteNonQuery();
                
            }
        }
        

        
        private static void ExecuteCommand(SqliteConnection connection, string commandText)
        {
            using var command = new SqliteCommand(commandText, connection);
            command.ExecuteNonQuery();
        }

        private static void UpdateExistingTables(SqliteConnection connection)
        {
            try
            {
                // إضافة عمود PurchasePrice إلى جدول Products إذا لم يكن موجوداً
                var checkColumnQuery = @"
                    SELECT COUNT(*)
                    FROM pragma_table_info('Products')
                    WHERE name = 'PurchasePrice'";

                using var checkCommand = connection.CreateCommand();
                checkCommand.CommandText = checkColumnQuery;
                var columnExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;

                if (!columnExists)
                {
                    var addColumnQuery = "ALTER TABLE Products ADD COLUMN PurchasePrice DECIMAL(10,2) DEFAULT 0";
                    ExecuteCommand(connection, addColumnQuery);

                    // تحديث القيم الموجودة لتكون PurchasePrice = Cost
                    var updateQuery = "UPDATE Products SET PurchasePrice = Cost WHERE PurchasePrice IS NULL OR PurchasePrice = 0";
                    ExecuteCommand(connection, updateQuery);
                }

                // إضافة عمود Stock إلى جدول Products إذا لم يكن موجوداً (للتوافق مع الكود)
                var checkStockColumnQuery = @"
                    SELECT COUNT(*)
                    FROM pragma_table_info('Products')
                    WHERE name = 'Stock'";

                checkCommand.CommandText = checkStockColumnQuery;
                var stockColumnExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;

                if (!stockColumnExists)
                {
                    var addStockColumnQuery = "ALTER TABLE Products ADD COLUMN Stock INTEGER DEFAULT 0";
                    ExecuteCommand(connection, addStockColumnQuery);

                    // تحديث القيم الموجودة لتكون Stock = Quantity
                    var updateStockQuery = "UPDATE Products SET Stock = Quantity WHERE Stock IS NULL OR Stock = 0";
                    ExecuteCommand(connection, updateStockQuery);
                }

                // إضافة عمود MinStock إلى جدول Products إذا لم يكن موجوداً (للتوافق مع الكود)
                var checkMinStockColumnQuery = @"
                    SELECT COUNT(*)
                    FROM pragma_table_info('Products')
                    WHERE name = 'MinStock'";

                checkCommand.CommandText = checkMinStockColumnQuery;
                var minStockColumnExists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;

                if (!minStockColumnExists)
                {
                    var addMinStockColumnQuery = "ALTER TABLE Products ADD COLUMN MinStock INTEGER DEFAULT 5";
                    ExecuteCommand(connection, addMinStockColumnQuery);

                    // تحديث القيم الموجودة لتكون MinStock = MinimumQuantity
                    var updateMinStockQuery = "UPDATE Products SET MinStock = MinimumQuantity WHERE MinStock IS NULL OR MinStock = 0";
                    ExecuteCommand(connection, updateMinStockQuery);
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ ولكن لا نوقف التطبيق
                Console.WriteLine($"خطأ في تحديث الجداول: {ex.Message}");
            }
        }
        
        public static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
        
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            var hashedInput = HashPassword(password);
            return hashedInput == hashedPassword;
        }

        // إنشاء جداول المخزون المتقدم
        public static void CreateAdvancedInventoryTables(SqliteConnection connection)
        {
            // جدول حركات المخزون
            var createInventoryMovementsTable = @"
                CREATE TABLE IF NOT EXISTS InventoryMovements (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    MovementType INTEGER NOT NULL,
                    Quantity DECIMAL(10,3) NOT NULL,
                    UnitCost DECIMAL(10,2) NOT NULL,
                    BalanceAfter DECIMAL(10,3) NOT NULL,
                    Notes TEXT,
                    ReferenceNumber TEXT,
                    ReferenceId INTEGER,
                    MovementDate DATETIME NOT NULL,
                    UserId INTEGER NOT NULL,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id),
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            // جدول الموردين المتقدم
            var createAdvancedSuppliersTable = @"
                CREATE TABLE IF NOT EXISTS AdvancedSuppliers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Code TEXT UNIQUE,
                    ContactPerson TEXT,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    City TEXT,
                    PostalCode TEXT,
                    Country TEXT,
                    TaxNumber TEXT,
                    PaymentTermsDays INTEGER DEFAULT 30,
                    CreditLimit DECIMAL(10,2) DEFAULT 0,
                    CurrentBalance DECIMAL(10,2) DEFAULT 0,
                    IsActive INTEGER NOT NULL DEFAULT 1,
                    Notes TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastOrderDate DATETIME,
                    TotalOrders DECIMAL(10,2) DEFAULT 0,
                    OrderCount INTEGER DEFAULT 0,
                    PerformanceRating DECIMAL(3,2) DEFAULT 0,
                    OnTimeDeliveries INTEGER DEFAULT 0,
                    TotalDeliveries INTEGER DEFAULT 0
                )";

            // جدول أوامر الشراء
            var createPurchaseOrdersTable = @"
                CREATE TABLE IF NOT EXISTS PurchaseOrders (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    OrderNumber TEXT UNIQUE NOT NULL,
                    SupplierId INTEGER NOT NULL,
                    OrderDate DATETIME NOT NULL,
                    ExpectedDeliveryDate DATETIME,
                    ActualDeliveryDate DATETIME,
                    Status INTEGER NOT NULL DEFAULT 1,
                    Subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
                    TaxAmount DECIMAL(10,2) DEFAULT 0,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    ShippingCost DECIMAL(10,2) DEFAULT 0,
                    Total DECIMAL(10,2) NOT NULL DEFAULT 0,
                    Notes TEXT,
                    CreatedBy INTEGER NOT NULL,
                    ApprovedBy INTEGER,
                    ApprovedAt DATETIME,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME,
                    FOREIGN KEY (SupplierId) REFERENCES AdvancedSuppliers(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
                    FOREIGN KEY (ApprovedBy) REFERENCES Users(Id)
                )";

            // جدول عناصر أوامر الشراء
            var createPurchaseOrderItemsTable = @"
                CREATE TABLE IF NOT EXISTS PurchaseOrderItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    PurchaseOrderId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity DECIMAL(10,3) NOT NULL,
                    UnitCost DECIMAL(10,2) NOT NULL,
                    DiscountAmount DECIMAL(10,2) DEFAULT 0,
                    Total DECIMAL(10,2) NOT NULL,
                    ReceivedQuantity DECIMAL(10,3) DEFAULT 0,
                    Notes TEXT,
                    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrders(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            // جدول استلام أوامر الشراء
            var createPurchaseOrderReceiptsTable = @"
                CREATE TABLE IF NOT EXISTS PurchaseOrderReceipts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    PurchaseOrderId INTEGER NOT NULL,
                    ReceiptNumber TEXT UNIQUE NOT NULL,
                    ReceiptDate DATETIME NOT NULL,
                    Notes TEXT,
                    ReceivedBy INTEGER NOT NULL,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (PurchaseOrderId) REFERENCES PurchaseOrders(Id),
                    FOREIGN KEY (ReceivedBy) REFERENCES Users(Id)
                )";

            // جدول عناصر استلام أوامر الشراء
            var createPurchaseOrderReceiptItemsTable = @"
                CREATE TABLE IF NOT EXISTS PurchaseOrderReceiptItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ReceiptId INTEGER NOT NULL,
                    PurchaseOrderItemId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    ReceivedQuantity DECIMAL(10,3) NOT NULL,
                    UnitCost DECIMAL(10,2) NOT NULL,
                    Total DECIMAL(10,2) NOT NULL,
                    Notes TEXT,
                    FOREIGN KEY (ReceiptId) REFERENCES PurchaseOrderReceipts(Id),
                    FOREIGN KEY (PurchaseOrderItemId) REFERENCES PurchaseOrderItems(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            // جدول تسويات المخزون
            var createStockAdjustmentsTable = @"
                CREATE TABLE IF NOT EXISTS StockAdjustments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    AdjustmentNumber TEXT UNIQUE NOT NULL,
                    AdjustmentDate DATETIME NOT NULL,
                    Reason INTEGER NOT NULL,
                    Notes TEXT,
                    CreatedBy INTEGER NOT NULL,
                    ApprovedBy INTEGER,
                    ApprovedAt DATETIME,
                    IsApproved INTEGER NOT NULL DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    TotalAdjustmentValue DECIMAL(10,2) DEFAULT 0,
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
                    FOREIGN KEY (ApprovedBy) REFERENCES Users(Id)
                )";

            // جدول عناصر تسويات المخزون
            var createStockAdjustmentItemsTable = @"
                CREATE TABLE IF NOT EXISTS StockAdjustmentItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    AdjustmentId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    SystemQuantity DECIMAL(10,3) NOT NULL,
                    ActualQuantity DECIMAL(10,3) NOT NULL,
                    UnitCost DECIMAL(10,2) NOT NULL,
                    Notes TEXT,
                    FOREIGN KEY (AdjustmentId) REFERENCES StockAdjustments(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            // جدول الجرد الفعلي
            var createPhysicalCountsTable = @"
                CREATE TABLE IF NOT EXISTS PhysicalCounts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CountDate DATETIME NOT NULL,
                    Notes TEXT,
                    CreatedBy TEXT NOT NULL,
                    CreatedAt DATETIME NOT NULL
                )";

            // جدول عناصر الجرد الفعلي
            var createPhysicalCountItemsTable = @"
                CREATE TABLE IF NOT EXISTS PhysicalCountItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CountId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    SystemStock DECIMAL(10,2) NOT NULL,
                    PhysicalStock DECIMAL(10,2) NOT NULL,
                    Variance DECIMAL(10,2) NOT NULL,
                    Notes TEXT,
                    FOREIGN KEY (CountId) REFERENCES PhysicalCounts(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            // جدول تنبيهات المخزون
            var createInventoryAlertsTable = @"
                CREATE TABLE IF NOT EXISTS InventoryAlerts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER NOT NULL,
                    Level INTEGER NOT NULL,
                    Title TEXT NOT NULL,
                    Message TEXT NOT NULL,
                    CurrentStock DECIMAL(10,3) NOT NULL,
                    MinimumStock DECIMAL(10,3) NOT NULL,
                    ReorderLevel DECIMAL(10,3) NOT NULL,
                    IsRead INTEGER NOT NULL DEFAULT 0,
                    IsResolved INTEGER NOT NULL DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ReadAt DATETIME,
                    ResolvedAt DATETIME,
                    ResolvedBy INTEGER,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id),
                    FOREIGN KEY (ResolvedBy) REFERENCES Users(Id)
                )";

            // جدول إعدادات المخزون
            var createInventorySettingsTable = @"
                CREATE TABLE IF NOT EXISTS InventorySettings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductId INTEGER UNIQUE NOT NULL,
                    MinimumStock DECIMAL(10,3) DEFAULT 0,
                    ReorderLevel DECIMAL(10,3) DEFAULT 0,
                    MaximumStock DECIMAL(10,3) DEFAULT 0,
                    ReorderQuantity DECIMAL(10,3) DEFAULT 0,
                    PreferredSupplierId INTEGER,
                    LeadTimeDays DECIMAL(5,2) DEFAULT 0,
                    AutoReorder INTEGER NOT NULL DEFAULT 0,
                    EnableAlerts INTEGER NOT NULL DEFAULT 1,
                    LastUpdated DATETIME,
                    UpdatedBy INTEGER,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id),
                    FOREIGN KEY (PreferredSupplierId) REFERENCES AdvancedSuppliers(Id),
                    FOREIGN KEY (UpdatedBy) REFERENCES Users(Id)
                )";

            // تنفيذ إنشاء الجداول
            using var command = connection.CreateCommand();

            command.CommandText = createInventoryMovementsTable;
            command.ExecuteNonQuery();

            command.CommandText = createAdvancedSuppliersTable;
            command.ExecuteNonQuery();

            command.CommandText = createPurchaseOrdersTable;
            command.ExecuteNonQuery();

            command.CommandText = createPurchaseOrderItemsTable;
            command.ExecuteNonQuery();

            command.CommandText = createPurchaseOrderReceiptsTable;
            command.ExecuteNonQuery();

            command.CommandText = createPurchaseOrderReceiptItemsTable;
            command.ExecuteNonQuery();

            command.CommandText = createStockAdjustmentsTable;
            command.ExecuteNonQuery();

            command.CommandText = createStockAdjustmentItemsTable;
            command.ExecuteNonQuery();

            command.CommandText = createPhysicalCountsTable;
            command.ExecuteNonQuery();

            command.CommandText = createPhysicalCountItemsTable;
            command.ExecuteNonQuery();

            command.CommandText = createInventoryAlertsTable;
            command.ExecuteNonQuery();

            command.CommandText = createInventorySettingsTable;
            command.ExecuteNonQuery();

            // إنشاء الفهارس
            CreateAdvancedInventoryIndexes(connection);
        }

        // إنشاء فهارس المخزون المتقدم
        private static void CreateAdvancedInventoryIndexes(SqliteConnection connection)
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_inventory_movements_product ON InventoryMovements(ProductId)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_movements_date ON InventoryMovements(MovementDate)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_movements_type ON InventoryMovements(MovementType)",
                "CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON PurchaseOrders(SupplierId)",
                "CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON PurchaseOrders(Status)",
                "CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON PurchaseOrders(OrderDate)",
                "CREATE INDEX IF NOT EXISTS idx_purchase_order_items_order ON PurchaseOrderItems(PurchaseOrderId)",
                "CREATE INDEX IF NOT EXISTS idx_purchase_order_items_product ON PurchaseOrderItems(ProductId)",
                "CREATE INDEX IF NOT EXISTS idx_stock_adjustments_date ON StockAdjustments(AdjustmentDate)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_alerts_product ON InventoryAlerts(ProductId)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_alerts_resolved ON InventoryAlerts(IsResolved)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_settings_product ON InventorySettings(ProductId)"
            };

            using var command = connection.CreateCommand();
            foreach (var index in indexes)
            {
                command.CommandText = index;
                command.ExecuteNonQuery();
            }
        }
    }
}
