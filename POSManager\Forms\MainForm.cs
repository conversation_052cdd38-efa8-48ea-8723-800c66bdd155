using POSManager.Services;
using POSManager.Models;

namespace POSManager.Forms
{
    public partial class MainForm : Form
    {
        private System.Windows.Forms.Timer? sessionTimer;
        
        public MainForm()
        {
            InitializeComponent();
            SetupForm();
            SetupSessionTimer();
            UpdateUserInterface();
        }
        
        private void SetupForm()
        {
            // إعداد النموذج للعربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // إعداد شريط الحالة
            UpdateStatusBar();
        }
        
        private void SetupSessionTimer()
        {
            sessionTimer = new System.Windows.Forms.Timer();
            sessionTimer.Interval = 60000; // فحص كل دقيقة
            sessionTimer.Tick += SessionTimer_Tick;
            sessionTimer.Start();
        }
        
        private async void SessionTimer_Tick(object? sender, EventArgs e)
        {
            if (AuthService.IsSessionExpired())
            {
                sessionTimer?.Stop();
                
                MessageBox.Show("انتهت صلاحية الجلسة. سيتم تسجيل الخروج تلقائياً.", "انتهاء الجلسة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                await LogoutAsync();
            }
            else
            {
                UpdateStatusBar();
            }
        }
        
        private void UpdateUserInterface()
        {
            var user = AuthService.CurrentUser;
            var permissions = AuthService.CurrentPermissions;
            
            if (user == null || permissions == null)
            {
                this.Close();
                return;
            }
            
            // تحديث عنوان النافذة
            this.Text = $"نظام نقطة البيع - {user.FullName} ({GetRoleDisplayName(user.Role)})";
            
            // تحديث الأزرار حسب الصلاحيات
            btnManageUsers.Enabled = permissions.CanManageUsers;
            btnManageProducts.Enabled = permissions.CanManageProducts;
            btnPOS.Enabled = permissions.CanProcessSales;
            btnReports.Enabled = permissions.CanViewReports;
            btnSettings.Enabled = permissions.CanManageSettings;
            btnInventory.Enabled = permissions.CanManageInventory;
            
            // تحديث معلومات المستخدم
            lblUserName.Text = $"المستخدم: {user.FullName}";
            lblUserRole.Text = $"الصلاحية: {GetRoleDisplayName(user.Role)}";
            lblLastLogin.Text = user.LastLogin.HasValue ? 
                $"آخر دخول: {user.LastLogin.Value:yyyy/MM/dd HH:mm}" : "آخر دخول: غير محدد";
        }
        
        private void UpdateStatusBar()
        {
            lblCurrentTime.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
            
            var user = AuthService.CurrentUser;
            if (user != null)
            {
                lblCurrentUser.Text = $"المستخدم الحالي: {user.FullName}";
            }
        }
        
        private string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.Admin => "مدير",
                UserRole.Cashier => "كاشير",
                _ => "غير محدد"
            };
        }
        
        // أحداث الأزرار
        private async void btnPOS_Click(object sender, EventArgs e)
        {
            if (await AuthService.CheckPermissionAsync("ProcessSales"))
            {
                try
                {
                    var currentUser = AuthService.CurrentUser;
                    if (currentUser == null)
                    {
                        MessageBox.Show("خطأ في الحصول على بيانات المستخدم", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    var posForm = new POSForm(currentUser);
                    posForm.ShowDialog();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح شاشة نقطة البيع: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                ShowPermissionDeniedMessage();
            }
        }
        
        private async void btnManageProducts_Click(object sender, EventArgs e)
        {
            if (await AuthService.CheckPermissionAsync("ManageProducts"))
            {
                var productsForm = new ProductsForm();
                productsForm.ShowDialog();
            }
            else
            {
                ShowPermissionDeniedMessage();
            }
        }
        
        private async void btnManageUsers_Click(object sender, EventArgs e)
        {
            if (await AuthService.CheckPermissionAsync("ManageUsers"))
            {
                // فتح شاشة إدارة المستخدمين
                MessageBox.Show("سيتم فتح شاشة إدارة المستخدمين", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowPermissionDeniedMessage();
            }
        }
        
        private async void btnReports_Click(object sender, EventArgs e)
        {
            if (await AuthService.CheckPermissionAsync("ViewReports"))
            {
                // فتح شاشة التقارير
                MessageBox.Show("سيتم فتح شاشة التقارير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowPermissionDeniedMessage();
            }
        }
        
        private async void btnInventory_Click(object sender, EventArgs e)
        {
            if (await AuthService.CheckPermissionAsync("ManageInventory"))
            {
                // فتح شاشة إدارة المخزون
                MessageBox.Show("سيتم فتح شاشة إدارة المخزون", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowPermissionDeniedMessage();
            }
        }
        
        private async void btnSettings_Click(object sender, EventArgs e)
        {
            if (await AuthService.CheckPermissionAsync("ManageSettings"))
            {
                // فتح شاشة الإعدادات
                MessageBox.Show("سيتم فتح شاشة الإعدادات", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                ShowPermissionDeniedMessage();
            }
        }
        
        private void btnAbout_Click(object sender, EventArgs e)
        {
            var aboutMessage = @"نظام إدارة نقطة البيع
الإصدار: 1.0
تم التطوير باستخدام: C# و Windows Forms
قاعدة البيانات: SQLite

جميع الحقوق محفوظة © 2025";
            
            MessageBox.Show(aboutMessage, "حول النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        
        private async void btnLogout_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                await LogoutAsync();
            }
        }
        
        private async Task LogoutAsync()
        {
            sessionTimer?.Stop();
            await AuthService.LogoutAsync();
            
            // إظهار نموذج تسجيل الدخول
            var loginForm = new LoginForm();
            loginForm.Show();
            
            this.Close();
        }
        
        private void ShowPermissionDeniedMessage()
        {
            MessageBox.Show("ليس لديك صلاحية للوصول إلى هذه الميزة", "رفض الوصول", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
        
        private async void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                if (MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }
            
            sessionTimer?.Stop();
            await AuthService.LogoutAsync();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                sessionTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
