using Microsoft.Data.Sqlite;
using ShopManagementSystem.Data;
using ShopManagementSystem.Models;

namespace ShopManagementSystem.Services
{
    public class ProductService
    {
        public List<Product> GetAllProducts()
        {
            var products = new List<Product>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT p.Id, p.Name, p.Code, p.Barcode, p.Price, p.Quantity, p.MinimumQuantity,
                           p.CategoryId, p.Description, p.CreatedAt, p.UpdatedAt, p.IsActive,
                           c.Name as CategoryName
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.IsActive = 1
                    ORDER BY p.Name";

                using var command = connection.CreateCommand();
                command.CommandText = query;

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    products.Add(new Product
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Code = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        Barcode = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                        Price = reader.GetDecimal(4),
                        Quantity = reader.GetInt32(5),
                        MinimumQuantity = reader.GetInt32(6),
                        CategoryId = reader.GetInt32(7),
                        Description = reader.IsDBNull(8) ? string.Empty : reader.GetString(8),
                        CreatedAt = reader.GetDateTime(9),
                        UpdatedAt = reader.GetDateTime(10),
                        IsActive = reader.GetBoolean(11),
                        Category = reader.IsDBNull(12) ? null : new Category
                        {
                            Id = reader.GetInt32(7),
                            Name = reader.GetString(12)
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المنتجات: {ex.Message}");
            }

            return products;
        }

        public Product? GetProductById(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT p.Id, p.Name, p.Code, p.Barcode, p.Price, p.Quantity, p.MinimumQuantity,
                           p.CategoryId, p.Description, p.CreatedAt, p.UpdatedAt, p.IsActive,
                           c.Name as CategoryName
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.Id = @id AND p.IsActive = 1";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", id);

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return new Product
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Code = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        Barcode = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                        Price = reader.GetDecimal(4),
                        Quantity = reader.GetInt32(5),
                        MinimumQuantity = reader.GetInt32(6),
                        CategoryId = reader.GetInt32(7),
                        Description = reader.IsDBNull(8) ? string.Empty : reader.GetString(8),
                        CreatedAt = reader.GetDateTime(9),
                        UpdatedAt = reader.GetDateTime(10),
                        IsActive = reader.GetBoolean(11),
                        Category = reader.IsDBNull(12) ? null : new Category
                        {
                            Id = reader.GetInt32(7),
                            Name = reader.GetString(12)
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المنتج: {ex.Message}");
            }

            return null;
        }

        public bool AddProduct(Product product)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    INSERT INTO Products (Name, Code, Barcode, Price, Quantity, MinimumQuantity, CategoryId, Description)
                    VALUES (@name, @code, @barcode, @price, @quantity, @minimumQuantity, @categoryId, @description)";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@name", product.Name);
                command.Parameters.AddWithValue("@code", product.Code ?? string.Empty);
                command.Parameters.AddWithValue("@barcode", product.Barcode ?? string.Empty);
                command.Parameters.AddWithValue("@price", product.Price);
                command.Parameters.AddWithValue("@quantity", product.Quantity);
                command.Parameters.AddWithValue("@minimumQuantity", product.MinimumQuantity);
                command.Parameters.AddWithValue("@categoryId", product.CategoryId);
                command.Parameters.AddWithValue("@description", product.Description ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة المنتج: {ex.Message}");
            }
        }

        public bool UpdateProduct(Product product)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    UPDATE Products
                    SET Name = @name, Code = @code, Barcode = @barcode, Price = @price,
                        Quantity = @quantity, MinimumQuantity = @minimumQuantity, CategoryId = @categoryId, Description = @description,
                        UpdatedAt = @updatedAt
                    WHERE Id = @id";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", product.Id);
                command.Parameters.AddWithValue("@name", product.Name);
                command.Parameters.AddWithValue("@code", product.Code ?? string.Empty);
                command.Parameters.AddWithValue("@barcode", product.Barcode ?? string.Empty);
                command.Parameters.AddWithValue("@price", product.Price);
                command.Parameters.AddWithValue("@quantity", product.Quantity);
                command.Parameters.AddWithValue("@minimumQuantity", product.MinimumQuantity);
                command.Parameters.AddWithValue("@categoryId", product.CategoryId);
                command.Parameters.AddWithValue("@description", product.Description ?? string.Empty);
                command.Parameters.AddWithValue("@updatedAt", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المنتج: {ex.Message}");
            }
        }

        public bool DeleteProduct(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                // حذف منطقي - تغيير IsActive إلى false
                var query = "UPDATE Products SET IsActive = 0, UpdatedAt = @updatedAt WHERE Id = @id";
                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", id);
                command.Parameters.AddWithValue("@updatedAt", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المنتج: {ex.Message}");
            }
        }

        public List<Product> SearchProducts(string searchTerm)
        {
            var products = new List<Product>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT p.Id, p.Name, p.Code, p.Barcode, p.Price, p.Quantity, p.MinimumQuantity,
                           p.CategoryId, p.Description, p.CreatedAt, p.UpdatedAt, p.IsActive,
                           c.Name as CategoryName
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.IsActive = 1
                    AND (p.Name LIKE @searchTerm OR p.Code LIKE @searchTerm OR p.Barcode LIKE @searchTerm OR p.Description LIKE @searchTerm)
                    ORDER BY p.Name";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@searchTerm", $"%{searchTerm}%");

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    products.Add(new Product
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Code = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        Barcode = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                        Price = reader.GetDecimal(4),
                        Quantity = reader.GetInt32(5),
                        MinimumQuantity = reader.GetInt32(6),
                        CategoryId = reader.GetInt32(7),
                        Description = reader.IsDBNull(8) ? string.Empty : reader.GetString(8),
                        CreatedAt = reader.GetDateTime(9),
                        UpdatedAt = reader.GetDateTime(10),
                        IsActive = reader.GetBoolean(11),
                        Category = reader.IsDBNull(12) ? null : new Category
                        {
                            Id = reader.GetInt32(7),
                            Name = reader.GetString(12)
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن المنتجات: {ex.Message}");
            }

            return products;
        }

        public List<Product> GetProductsByCategory(int categoryId)
        {
            var products = new List<Product>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT p.Id, p.Name, p.Code, p.Barcode, p.Price, p.Quantity, p.MinimumQuantity,
                           p.CategoryId, p.Description, p.CreatedAt, p.UpdatedAt, p.IsActive,
                           c.Name as CategoryName
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.IsActive = 1 AND p.CategoryId = @categoryId
                    ORDER BY p.Name";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@categoryId", categoryId);

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    products.Add(new Product
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Code = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        Barcode = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                        Price = reader.GetDecimal(4),
                        Quantity = reader.GetInt32(5),
                        MinimumQuantity = reader.GetInt32(6),
                        CategoryId = reader.GetInt32(7),
                        Description = reader.IsDBNull(8) ? string.Empty : reader.GetString(8),
                        CreatedAt = reader.GetDateTime(9),
                        UpdatedAt = reader.GetDateTime(10),
                        IsActive = reader.GetBoolean(11),
                        Category = reader.IsDBNull(12) ? null : new Category
                        {
                            Id = reader.GetInt32(7),
                            Name = reader.GetString(12)
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب منتجات الفئة: {ex.Message}");
            }

            return products;
        }
    }
}