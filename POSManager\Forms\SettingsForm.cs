using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class SettingsForm : Form
    {
        private ApplicationSettings _settings = new ApplicationSettings();
        private readonly User _currentUser;
        private bool _isLoading = false;

        public SettingsForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            this.Load += SettingsForm_Load;
            this.FormClosing += SettingsForm_FormClosing;
        }

        private async void SettingsForm_Load(object sender, EventArgs e)
        {
            try
            {
                _isLoading = true;
                await LoadSettingsAsync();
                SetupPermissions();
                _isLoading = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadSettingsAsync()
        {
            _settings = await SettingsService.GetSettingsAsync();
            PopulateControls();
        }

        private void PopulateControls()
        {
            // إعدادات النظام
            txtCompanyName.Text = _settings.System.CompanyName;
            txtCompanyAddress.Text = _settings.System.CompanyAddress;
            txtCompanyPhone.Text = _settings.System.CompanyPhone;
            txtCompanyEmail.Text = _settings.System.CompanyEmail;
            txtTaxNumber.Text = _settings.System.TaxNumber;
            numTaxRate.Value = _settings.System.TaxRate * 100; // تحويل إلى نسبة مئوية
            txtCurrency.Text = _settings.System.Currency;
            txtCurrencySymbol.Text = _settings.System.CurrencySymbol;
            chkRTL.Checked = _settings.System.IsRTL;

            // إعدادات الطباعة
            txtDefaultPrinter.Text = _settings.Print.DefaultPrinterName;
            cmbPrinterType.SelectedIndex = (int)_settings.Print.PrinterType - 1;
            cmbPaperSize.SelectedIndex = (int)_settings.Print.PaperSize - 1;
            chkAutoPrint.Checked = _settings.Print.AutoPrint;
            chkPrintLogo.Checked = _settings.Print.PrintLogo;
            chkPrintHeader.Checked = _settings.Print.PrintHeader;
            chkPrintFooter.Checked = _settings.Print.PrintFooter;
            txtHeaderText.Text = _settings.Print.HeaderText;
            txtFooterText.Text = _settings.Print.FooterText;
            numCopiesCount.Value = _settings.Print.CopiesCount;
            chkPrintBarcode.Checked = _settings.Print.PrintBarcode;
            txtLogoPath.Text = _settings.Print.LogoPath;

            // إعدادات الأمان
            chkRequireStrongPassword.Checked = _settings.Security.RequireStrongPassword;
            numMinPasswordLength.Value = _settings.Security.MinPasswordLength;
            numMaxLoginAttempts.Value = _settings.Security.MaxLoginAttempts;
            numAutoLogoutMinutes.Value = _settings.Security.AutoLogoutMinutes;
            chkEnableActivityLog.Checked = _settings.Security.EnableActivityLog;

            // إعدادات النسخ الاحتياطي
            chkAutoBackup.Checked = _settings.Backup.AutoBackupEnabled;
            cmbBackupFrequency.SelectedIndex = (int)_settings.Backup.BackupFrequency - 1;
            txtBackupPath.Text = _settings.Backup.BackupPath;
            chkEmailBackup.Checked = _settings.Backup.EmailBackup;
            txtBackupEmail.Text = _settings.Backup.BackupEmail;

            // إعدادات الواجهة
            cmbTheme.Text = _settings.UI.Theme;
            numFontSize.Value = _settings.UI.FontSize;
            cmbFontFamily.Text = _settings.UI.FontFamily;
            chkShowToolbar.Checked = _settings.UI.ShowToolbar;
        }

        private void SetupPermissions()
        {
            // فقط المدير يمكنه تعديل إعدادات الأمان والنسخ الاحتياطي
            if (_currentUser.Role != UserRole.Admin)
            {
                tabSecurity.Enabled = false;
                tabBackup.Enabled = false;
                btnResetToDefault.Enabled = false;
                btnImportSettings.Enabled = false;
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (!ValidateSettings())
                    return;

                UpdateSettingsFromControls();

                var success = await SettingsService.SaveSettingsAsync(_settings, _currentUser.Username);
                
                if (success)
                {
                    MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الإعدادات", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateSettings()
        {
            // التحقق من صحة البريد الإلكتروني للشركة
            if (!string.IsNullOrEmpty(txtCompanyEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtCompanyEmail.Text);
                }
                catch
                {
                    MessageBox.Show("عنوان البريد الإلكتروني للشركة غير صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabSystem.Focus();
                    txtCompanyEmail.Focus();
                    return false;
                }
            }

            // التحقق من صحة البريد الإلكتروني للنسخ الاحتياطي
            if (chkEmailBackup.Checked && !string.IsNullOrEmpty(txtBackupEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtBackupEmail.Text);
                }
                catch
                {
                    MessageBox.Show("عنوان البريد الإلكتروني للنسخ الاحتياطي غير صحيح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    tabBackup.Focus();
                    txtBackupEmail.Focus();
                    return false;
                }
            }

            // التحقق من مسار النسخ الاحتياطي
            if (chkAutoBackup.Checked && string.IsNullOrEmpty(txtBackupPath.Text))
            {
                MessageBox.Show("يجب تحديد مسار النسخ الاحتياطي", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabBackup.Focus();
                txtBackupPath.Focus();
                return false;
            }

            return true;
        }

        private void UpdateSettingsFromControls()
        {
            // إعدادات النظام
            _settings.System.CompanyName = txtCompanyName.Text.Trim();
            _settings.System.CompanyAddress = txtCompanyAddress.Text.Trim();
            _settings.System.CompanyPhone = txtCompanyPhone.Text.Trim();
            _settings.System.CompanyEmail = txtCompanyEmail.Text.Trim();
            _settings.System.TaxNumber = txtTaxNumber.Text.Trim();
            _settings.System.TaxRate = numTaxRate.Value / 100; // تحويل من نسبة مئوية
            _settings.System.Currency = txtCurrency.Text.Trim();
            _settings.System.CurrencySymbol = txtCurrencySymbol.Text.Trim();
            _settings.System.IsRTL = chkRTL.Checked;

            // إعدادات الطباعة
            _settings.Print.DefaultPrinterName = txtDefaultPrinter.Text.Trim();
            _settings.Print.PrinterType = (PrinterType)(cmbPrinterType.SelectedIndex + 1);
            _settings.Print.PaperSize = (SettingsPaperSize)(cmbPaperSize.SelectedIndex + 1);
            _settings.Print.AutoPrint = chkAutoPrint.Checked;
            _settings.Print.PrintLogo = chkPrintLogo.Checked;
            _settings.Print.PrintHeader = chkPrintHeader.Checked;
            _settings.Print.PrintFooter = chkPrintFooter.Checked;
            _settings.Print.HeaderText = txtHeaderText.Text.Trim();
            _settings.Print.FooterText = txtFooterText.Text.Trim();
            _settings.Print.CopiesCount = (int)numCopiesCount.Value;
            _settings.Print.PrintBarcode = chkPrintBarcode.Checked;
            _settings.Print.LogoPath = txtLogoPath.Text.Trim();

            // إعدادات الأمان (فقط للمدير)
            if (_currentUser.Role == UserRole.Admin)
            {
                _settings.Security.RequireStrongPassword = chkRequireStrongPassword.Checked;
                _settings.Security.MinPasswordLength = (int)numMinPasswordLength.Value;
                _settings.Security.MaxLoginAttempts = (int)numMaxLoginAttempts.Value;
                _settings.Security.AutoLogoutMinutes = (int)numAutoLogoutMinutes.Value;
                _settings.Security.EnableActivityLog = chkEnableActivityLog.Checked;

                // إعدادات النسخ الاحتياطي
                _settings.Backup.AutoBackupEnabled = chkAutoBackup.Checked;
                _settings.Backup.BackupFrequency = (BackupFrequency)(cmbBackupFrequency.SelectedIndex + 1);
                _settings.Backup.BackupPath = txtBackupPath.Text.Trim();
                _settings.Backup.EmailBackup = chkEmailBackup.Checked;
                _settings.Backup.BackupEmail = txtBackupEmail.Text.Trim();
            }

            // إعدادات الواجهة
            _settings.UI.Theme = cmbTheme.Text;
            _settings.UI.FontSize = (int)numFontSize.Value;
            _settings.UI.FontFamily = cmbFontFamily.Text;
            _settings.UI.ShowToolbar = chkShowToolbar.Checked;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private async void SettingsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (this.DialogResult == DialogResult.None)
            {
                var result = MessageBox.Show("هل تريد حفظ التغييرات؟", "تأكيد",
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    e.Cancel = true;
                    btnSave_Click(sender, e);
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }

        private async void btnResetToDefault_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
                "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var success = await SettingsService.ResetToDefaultAsync(_currentUser.Username);
                    if (success)
                    {
                        await LoadSettingsAsync();
                        MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إعادة تعيين الإعدادات", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnExportSettings_Click(object sender, EventArgs e)
        {
            using var saveDialog = new SaveFileDialog
            {
                Filter = "JSON Files (*.json)|*.json",
                Title = "تصدير الإعدادات",
                FileName = $"POSSettings_{DateTime.Now:yyyyMMdd_HHmmss}.json"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var success = await SettingsService.ExportSettingsAsync(saveDialog.FileName);
                    if (success)
                    {
                        MessageBox.Show("تم تصدير الإعدادات بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في تصدير الإعدادات", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تصدير الإعدادات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnImportSettings_Click(object sender, EventArgs e)
        {
            using var openDialog = new OpenFileDialog
            {
                Filter = "JSON Files (*.json)|*.json",
                Title = "استيراد الإعدادات"
            };

            if (openDialog.ShowDialog() == DialogResult.OK)
            {
                var result = MessageBox.Show("هل أنت متأكد من استيراد الإعدادات؟ سيتم استبدال الإعدادات الحالية.",
                    "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        var success = await SettingsService.ImportSettingsAsync(openDialog.FileName, _currentUser.Username);
                        if (success)
                        {
                            await LoadSettingsAsync();
                            MessageBox.Show("تم استيراد الإعدادات بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في استيراد الإعدادات", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد الإعدادات: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
    }
}
