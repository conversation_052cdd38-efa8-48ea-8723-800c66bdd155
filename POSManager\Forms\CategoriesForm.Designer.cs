namespace POSManager.Forms
{
    partial class CategoriesForm
    {
        private System.ComponentModel.IContainer components = null;
        private DataGridView dgvCategories;
        private TextBox txtName;
        private TextBox txtDescription;
        private CheckBox chkIsActive;
        private Button btnAdd;
        private Button btnEdit;
        private Button btnSave;
        private Button btnCancel;
        private Button btnDelete;
        private Button btnRefresh;
        private Button btnViewProducts;
        private TextBox txtSearch;
        private Label lblTotalCategories;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // Form
            this.Text = "إدارة الفئات";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // Main Panel
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 1,
                Padding = new Padding(10)
            };
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 65F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 35F));
            
            // Left Panel (Categories List)
            var leftPanel = new Panel { Dock = DockStyle.Fill };
            
            // Search Panel
            var searchPanel = new Panel { Height = 60, Dock = DockStyle.Top };
            
            var lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 15),
                Size = new Size(50, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            
            txtSearch = new TextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 23),
                RightToLeft = RightToLeft.Yes
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            
            btnRefresh = new Button
            {
                Text = "تحديث",
                Location = new Point(290, 10),
                Size = new Size(80, 27),
                UseVisualStyleBackColor = true
            };
            btnRefresh.Click += BtnRefresh_Click;
            
            lblTotalCategories = new Label
            {
                Text = "إجمالي الفئات: 0",
                Location = new Point(10, 35),
                Size = new Size(150, 20),
                ForeColor = Color.Blue
            };
            
            searchPanel.Controls.AddRange(new Control[] { lblSearch, txtSearch, btnRefresh, lblTotalCategories });
            
            // DataGridView
            dgvCategories = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RightToLeft = RightToLeft.Yes
            };
            
            leftPanel.Controls.Add(dgvCategories);
            leftPanel.Controls.Add(searchPanel);
            
            // Right Panel (Category Form)
            var rightPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            
            var formTitle = new Label
            {
                Text = "بيانات الفئة",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // Form Fields
            int yPos = 50;
            int fieldHeight = 35;
            int labelWidth = 80;
            int controlWidth = 200;
            
            var lblName = new Label
            {
                Text = "اسم الفئة:",
                Location = new Point(10, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            txtName = new TextBox
            {
                Location = new Point(100, yPos),
                Size = new Size(controlWidth, 23),
                RightToLeft = RightToLeft.Yes
            };
            yPos += fieldHeight;
            
            var lblDescription = new Label
            {
                Text = "الوصف:",
                Location = new Point(10, yPos),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleRight
            };
            txtDescription = new TextBox
            {
                Location = new Point(100, yPos),
                Size = new Size(controlWidth, 80),
                Multiline = true,
                RightToLeft = RightToLeft.Yes
            };
            yPos += 90;
            
            chkIsActive = new CheckBox
            {
                Text = "نشط",
                Location = new Point(100, yPos),
                Size = new Size(100, 23),
                RightToLeft = RightToLeft.Yes,
                Checked = true
            };
            yPos += fieldHeight;
            
            // Buttons Panel
            var buttonsPanel = new Panel
            {
                Location = new Point(10, yPos + 20),
                Size = new Size(320, 150)
            };
            
            btnAdd = new Button
            {
                Text = "جديد",
                Location = new Point(10, 10),
                Size = new Size(80, 35),
                UseVisualStyleBackColor = true
            };
            btnAdd.Click += BtnAdd_Click;
            
            btnEdit = new Button
            {
                Text = "تعديل",
                Location = new Point(100, 10),
                Size = new Size(80, 35),
                UseVisualStyleBackColor = true,
                Enabled = false
            };
            btnEdit.Click += BtnEdit_Click;
            
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(190, 10),
                Size = new Size(80, 35),
                UseVisualStyleBackColor = true,
                BackColor = Color.LightGreen
            };
            btnSave.Click += BtnSave_Click;
            
            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(10, 55),
                Size = new Size(80, 35),
                UseVisualStyleBackColor = true
            };
            btnCancel.Click += BtnCancel_Click;
            
            btnDelete = new Button
            {
                Text = "حذف",
                Location = new Point(100, 55),
                Size = new Size(80, 35),
                UseVisualStyleBackColor = true,
                Enabled = false,
                BackColor = Color.LightCoral
            };
            btnDelete.Click += BtnDelete_Click;
            
            btnViewProducts = new Button
            {
                Text = "عرض المنتجات",
                Location = new Point(190, 55),
                Size = new Size(100, 35),
                UseVisualStyleBackColor = true,
                Enabled = false,
                BackColor = Color.LightBlue
            };
            btnViewProducts.Click += BtnViewProducts_Click;
            
            buttonsPanel.Controls.AddRange(new Control[] {
                btnAdd, btnEdit, btnSave, btnCancel, btnDelete, btnViewProducts
            });
            
            rightPanel.Controls.AddRange(new Control[] {
                formTitle, lblName, txtName, lblDescription, txtDescription,
                chkIsActive, buttonsPanel
            });
            
            mainPanel.Controls.Add(leftPanel, 0, 0);
            mainPanel.Controls.Add(rightPanel, 1, 0);
            
            this.Controls.Add(mainPanel);
        }
    }
}
