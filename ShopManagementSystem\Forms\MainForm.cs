using ShopManagementSystem.Models;

namespace ShopManagementSystem.Forms
{
    public partial class MainForm : Form
    {
        private readonly User _currentUser;

        public MainForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            InitializeForm();
        }

        private void InitializeForm()
        {
            // تحديث معلومات المستخدم
            lblUserName.Text = $"مرحباً، {_currentUser.FullName}";
            lblUserRole.Text = _currentUser.Role == UserRole.Admin ? "مدير" : "كاشير";
            
            // إعداد الصلاحيات
            SetupPermissions();
        }

        private void SetupPermissions()
        {
            // إذا كان المستخدم كاشير، إخفاء بعض الأزرار
            if (_currentUser.Role == UserRole.Cashier)
            {
                btnUsers.Visible = false;
                btnReports.Enabled = false; // يمكن رؤية التقارير لكن بصلاحيات محدودة
            }
        }

        private void btnProducts_Click(object sender, EventArgs e)
        {
            var productsForm = new ProductsForm(_currentUser);
            productsForm.ShowDialog();
        }

        private void btnPOS_Click(object sender, EventArgs e)
        {
            var posForm = new POSForm(_currentUser);
            posForm.ShowDialog();
        }

        private void btnInventory_Click(object sender, EventArgs e)
        {
            var inventoryForm = new InventoryForm(_currentUser);
            inventoryForm.ShowDialog();
        }

        private void btnSales_Click(object sender, EventArgs e)
        {
            var salesForm = new SalesForm(_currentUser);
            salesForm.ShowDialog();
        }

        private void btnReports_Click(object sender, EventArgs e)
        {
            var reportsForm = new ReportsForm(_currentUser);
            reportsForm.ShowDialog();
        }

        private void btnUsers_Click(object sender, EventArgs e)
        {
            if (_currentUser.Role == UserRole.Admin)
            {
                var usersForm = new UsersForm(_currentUser);
                usersForm.ShowDialog();
            }
            else
            {
                MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                this.Close();
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        private void timer_Tick(object sender, EventArgs e)
        {
            lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
        }
    }
}
