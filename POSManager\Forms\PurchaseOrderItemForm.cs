using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;
using POSManager.Data;

namespace POSManager.Forms
{
    public partial class PurchaseOrderItemForm : Form
    {
        private readonly DatabaseManager _dbManager;
        private ComboBox cmbProduct;
        private TextBox txtProductCode, txtProductName, txtNotes;
        private NumericUpDown nudQuantity, nudUnitCost, nudDiscountAmount, nudTotal;
        private Button btnSave, btnCancel, btnSelectProduct;
        private Label lblTotal;
        private PurchaseOrderItem? _currentItem;
        private bool _isEditing;

        public PurchaseOrderItem? OrderItem { get; private set; }

        public PurchaseOrderItemForm(PurchaseOrderItem? item = null)
        {
            _dbManager = DatabaseManager.Instance;
            _currentItem = item;
            _isEditing = item != null;
            InitializeComponent();
            SetupForm();
            LoadProductsAsync();
            
            if (_isEditing && _currentItem != null)
            {
                LoadItemToForm(_currentItem);
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النافذة الأساسية
            this.Text = _isEditing ? "تعديل عنصر أمر الشراء" : "إضافة عنصر أمر الشراء";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // اختيار المنتج
            var lblProduct = new Label 
            { 
                Text = "المنتج:", 
                Location = new Point(10, 20), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            cmbProduct = new ComboBox 
            { 
                Location = new Point(100, 20), 
                Size = new Size(200, 23), 
                DropDownStyle = ComboBoxStyle.DropDownList 
            };

            btnSelectProduct = new Button 
            { 
                Text = "...", 
                Location = new Point(310, 20), 
                Size = new Size(30, 23) 
            };

            // كود المنتج
            var lblProductCode = new Label 
            { 
                Text = "كود المنتج:", 
                Location = new Point(10, 55), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            txtProductCode = new TextBox 
            { 
                Location = new Point(100, 55), 
                Size = new Size(150, 23), 
                ReadOnly = true 
            };

            // اسم المنتج
            var lblProductName = new Label 
            { 
                Text = "اسم المنتج:", 
                Location = new Point(10, 90), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            txtProductName = new TextBox 
            { 
                Location = new Point(100, 90), 
                Size = new Size(240, 23), 
                ReadOnly = true 
            };

            // الكمية
            var lblQuantity = new Label 
            { 
                Text = "الكمية:", 
                Location = new Point(10, 125), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            nudQuantity = new NumericUpDown 
            { 
                Location = new Point(100, 125), 
                Size = new Size(100, 23), 
                Minimum = 0.01m, 
                Maximum = 999999, 
                DecimalPlaces = 2, 
                Value = 1 
            };

            // سعر الوحدة
            var lblUnitCost = new Label 
            { 
                Text = "سعر الوحدة:", 
                Location = new Point(10, 160), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            nudUnitCost = new NumericUpDown 
            { 
                Location = new Point(100, 160), 
                Size = new Size(100, 23), 
                Minimum = 0, 
                Maximum = 999999, 
                DecimalPlaces = 2 
            };

            // مبلغ الخصم
            var lblDiscountAmount = new Label 
            { 
                Text = "مبلغ الخصم:", 
                Location = new Point(10, 195), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            nudDiscountAmount = new NumericUpDown 
            { 
                Location = new Point(100, 195), 
                Size = new Size(100, 23), 
                Minimum = 0, 
                Maximum = 999999, 
                DecimalPlaces = 2 
            };

            // المجموع
            var lblTotalLabel = new Label 
            { 
                Text = "المجموع:", 
                Location = new Point(10, 230), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            lblTotal = new Label 
            { 
                Text = "0.00", 
                Location = new Point(100, 230), 
                Size = new Size(100, 23), 
                BorderStyle = BorderStyle.FixedSingle, 
                TextAlign = ContentAlignment.MiddleRight,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            // ملاحظات
            var lblNotes = new Label 
            { 
                Text = "ملاحظات:", 
                Location = new Point(10, 265), 
                Size = new Size(80, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };

            txtNotes = new TextBox 
            { 
                Location = new Point(100, 265), 
                Size = new Size(240, 50), 
                Multiline = true 
            };

            // أزرار التحكم
            btnSave = new Button 
            { 
                Text = "حفظ", 
                Location = new Point(100, 330), 
                Size = new Size(80, 30), 
                BackColor = Color.Green, 
                ForeColor = Color.White 
            };

            btnCancel = new Button 
            { 
                Text = "إلغاء", 
                Location = new Point(190, 330), 
                Size = new Size(80, 30), 
                BackColor = Color.Gray, 
                ForeColor = Color.White 
            };

            // إضافة العناصر للوحة
            mainPanel.Controls.AddRange(new Control[] 
            {
                lblProduct, cmbProduct, btnSelectProduct,
                lblProductCode, txtProductCode,
                lblProductName, txtProductName,
                lblQuantity, nudQuantity,
                lblUnitCost, nudUnitCost,
                lblDiscountAmount, nudDiscountAmount,
                lblTotalLabel, lblTotal,
                lblNotes, txtNotes,
                btnSave, btnCancel
            });

            this.Controls.Add(mainPanel);

            // ربط الأحداث
            cmbProduct.SelectedIndexChanged += CmbProduct_SelectedIndexChanged;
            nudQuantity.ValueChanged += CalculateTotal;
            nudUnitCost.ValueChanged += CalculateTotal;
            nudDiscountAmount.ValueChanged += CalculateTotal;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnSelectProduct.Click += BtnSelectProduct_Click;
        }

        private async void LoadProductsAsync()
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT Id, Code, Name, PurchasePrice FROM Products WHERE IsActive = 1 ORDER BY Name";
                using var command = new Microsoft.Data.Sqlite.SqliteCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                var products = new List<dynamic>();
                while (await reader.ReadAsync())
                {
                    products.Add(new 
                    { 
                        Id = Convert.ToInt32(reader[0]), 
                        Code = Convert.ToString(reader[1]) ?? "", 
                        Name = Convert.ToString(reader[2]) ?? "",
                        PurchasePrice = Convert.ToDecimal(reader[3])
                    });
                }

                cmbProduct.Items.Clear();
                cmbProduct.Items.Add(new { Id = 0, Code = "", Name = "اختر منتج...", PurchasePrice = 0m });
                
                foreach (var product in products)
                {
                    cmbProduct.Items.Add(product);
                }

                cmbProduct.DisplayMember = "Name";
                cmbProduct.ValueMember = "Id";
                cmbProduct.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadItemToForm(PurchaseOrderItem item)
        {
            // البحث عن المنتج في القائمة
            for (int i = 0; i < cmbProduct.Items.Count; i++)
            {
                dynamic product = cmbProduct.Items[i];
                if (product.Id == item.ProductId)
                {
                    cmbProduct.SelectedIndex = i;
                    break;
                }
            }

            nudQuantity.Value = item.Quantity;
            nudUnitCost.Value = item.UnitCost;
            nudDiscountAmount.Value = item.DiscountAmount;
            txtNotes.Text = item.Notes ?? "";
            
            CalculateTotal(null, null);
        }

        private void CmbProduct_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbProduct.SelectedItem != null)
            {
                dynamic selectedProduct = cmbProduct.SelectedItem;
                txtProductCode.Text = selectedProduct.Code;
                txtProductName.Text = selectedProduct.Name;
                
                if (selectedProduct.Id > 0)
                {
                    nudUnitCost.Value = selectedProduct.PurchasePrice;
                }
                else
                {
                    nudUnitCost.Value = 0;
                }
                
                CalculateTotal(null, null);
            }
        }

        private void CalculateTotal(object sender, EventArgs e)
        {
            var total = (nudQuantity.Value * nudUnitCost.Value) - nudDiscountAmount.Value;
            lblTotal.Text = total.ToString("F2");
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateForm())
            {
                dynamic selectedProduct = cmbProduct.SelectedItem;
                
                OrderItem = new PurchaseOrderItem
                {
                    Id = _currentItem?.Id ?? 0,
                    ProductId = selectedProduct.Id,
                    ProductCode = selectedProduct.Code,
                    ProductName = selectedProduct.Name,
                    Quantity = nudQuantity.Value,
                    UnitCost = nudUnitCost.Value,
                    DiscountAmount = nudDiscountAmount.Value,
                    Total = decimal.Parse(lblTotal.Text),
                    Notes = txtNotes.Text
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnSelectProduct_Click(object sender, EventArgs e)
        {
            // يمكن فتح نافذة بحث متقدمة للمنتجات
            MessageBox.Show("نافذة البحث المتقدمة قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private bool ValidateForm()
        {
            if (cmbProduct.SelectedItem == null)
            {
                MessageBox.Show("يجب اختيار منتج", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbProduct.Focus();
                return false;
            }

            dynamic selectedProduct = cmbProduct.SelectedItem;
            if (selectedProduct.Id == 0)
            {
                MessageBox.Show("يجب اختيار منتج صحيح", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbProduct.Focus();
                return false;
            }

            if (nudQuantity.Value <= 0)
            {
                MessageBox.Show("يجب أن تكون الكمية أكبر من صفر", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudQuantity.Focus();
                return false;
            }

            if (nudUnitCost.Value < 0)
            {
                MessageBox.Show("سعر الوحدة لا يمكن أن يكون سالباً", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudUnitCost.Focus();
                return false;
            }

            if (nudDiscountAmount.Value < 0)
            {
                MessageBox.Show("مبلغ الخصم لا يمكن أن يكون سالباً", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudDiscountAmount.Focus();
                return false;
            }

            var total = (nudQuantity.Value * nudUnitCost.Value) - nudDiscountAmount.Value;
            if (total < 0)
            {
                MessageBox.Show("مبلغ الخصم لا يمكن أن يكون أكبر من إجمالي السعر", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudDiscountAmount.Focus();
                return false;
            }

            return true;
        }
    }
}
