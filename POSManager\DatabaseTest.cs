using POSManager.Data;
using POSManager.Services;
using System;
using System.Threading.Tasks;

namespace POSManager
{
    public static class DatabaseTest
    {
        public static async Task TestDatabaseAsync()
        {
            try
            {
                Console.WriteLine("🔍 فحص قاعدة البيانات...");
                
                // إنشاء قاعدة البيانات
                DatabaseManager.InitializeDatabase();
                
                // فحص الفئات
                var categories = await CategoryService.GetAllCategoriesAsync();
                Console.WriteLine($"عدد الفئات: {categories.Count}");
                
                // فحص المنتجات
                var products = await ProductService.GetAllProductsAsync();
                Console.WriteLine($"عدد المنتجات: {products.Count}");
                
                // إذا لم تكن هناك منتجات، أنشئها
                if (products.Count == 0)
                {
                    Console.WriteLine("إنشاء منتجات تجريبية...");
                    await TestData.SampleDataGenerator.CreateProductsAsync();
                    
                    // فحص مرة أخرى
                    products = await ProductService.GetAllProductsAsync();
                    Console.WriteLine($"عدد المنتجات بعد الإنشاء: {products.Count}");
                }
                
                Console.WriteLine("✅ انتهى فحص قاعدة البيانات");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في فحص قاعدة البيانات: {ex.Message}");
                Console.WriteLine($"تفاصيل: {ex.StackTrace}");
            }
        }
    }
}
