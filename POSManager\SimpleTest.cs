using POSManager.Services;
using System;
using System.Threading.Tasks;

namespace POSManager
{
    public static class SimpleTest
    {
        public static async Task RunSimpleTestAsync()
        {
            Console.WriteLine("=== اختبار بسيط لنظام نقطة البيع ===");
            
            try
            {
                // اختبار قاعدة البيانات
                Console.WriteLine("1. اختبار قاعدة البيانات...");
                var categories = await CategoryService.GetAllCategoriesAsync();
                Console.WriteLine($"   ✓ تم العثور على {categories.Count} فئة");
                
                // اختبار المنتجات
                Console.WriteLine("2. اختبار المنتجات...");
                var products = await ProductService.GetAllProductsAsync();
                Console.WriteLine($"   ✓ تم العثور على {products.Count} منتج");
                
                // اختبار الإحصائيات
                Console.WriteLine("3. حساب الإحصائيات...");
                var totalValue = products.Sum(p => p.Stock * p.PurchasePrice);
                Console.WriteLine($"   ✓ إجمالي قيمة المخزون: {totalValue:C}");
                
                var lowStockCount = products.Where(p => p.Stock <= p.MinStock).Count();
                Console.WriteLine($"   ✓ منتجات منخفضة المخزون: {lowStockCount}");
                
                Console.WriteLine("\n🎉 تم إكمال الاختبار البسيط بنجاح!");
                Console.WriteLine("✅ النظام يعمل بشكل صحيح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل: {ex.StackTrace}");
            }
        }
    }
}
