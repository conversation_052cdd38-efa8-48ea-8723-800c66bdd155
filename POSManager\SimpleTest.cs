using POSManager.Services;
using POSManager.TestData;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace POSManager
{
    public static class SimpleTest
    {
        public static async Task RunSimpleTestAsync()
        {
            var logFile = "test_results.txt";
            var results = new List<string>();

            results.Add("=== اختبار بسيط لنظام نقطة البيع ===");
            results.Add($"تاريخ الاختبار: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.Add("");

            try
            {
                // إنشاء البيانات التجريبية إذا لم تكن موجودة
                results.Add("1. التحقق من البيانات التجريبية...");
                var categories = await CategoryService.GetAllCategoriesAsync();
                var products = await ProductService.GetAllProductsAsync();

                if (categories.Count == 0 || products.Count == 0)
                {
                    results.Add("   ⚠️ لا توجد بيانات تجريبية، جاري إنشاؤها...");
                    await SampleDataGenerator.GenerateTestDataAsync();
                    results.Add("   ✅ تم إنشاء البيانات التجريبية بنجاح");

                    // إعادة تحميل البيانات
                    categories = await CategoryService.GetAllCategoriesAsync();
                    products = await ProductService.GetAllProductsAsync();
                }

                // اختبار قاعدة البيانات
                results.Add("2. اختبار قاعدة البيانات...");
                results.Add($"   ✓ تم العثور على {categories.Count} فئة");

                // اختبار المنتجات
                results.Add("3. اختبار المنتجات...");
                results.Add($"   ✓ تم العثور على {products.Count} منتج");

                // اختبار الإحصائيات
                results.Add("4. حساب الإحصائيات...");
                var totalValue = products.Sum(p => p.Stock * p.PurchasePrice);
                results.Add($"   ✓ إجمالي قيمة المخزون: {totalValue:C}");

                var lowStockCount = products.Where(p => p.Stock <= p.MinStock).Count();
                results.Add($"   ✓ منتجات منخفضة المخزون: {lowStockCount}");

                results.Add("");
                results.Add("🎉 تم إكمال الاختبار البسيط بنجاح!");
                results.Add("✅ النظام يعمل بشكل صحيح");
            }
            catch (Exception ex)
            {
                results.Add("");
                results.Add($"❌ خطأ في الاختبار: {ex.Message}");
                results.Add($"تفاصيل: {ex.StackTrace}");
            }

            // كتابة النتائج إلى الملف وإلى وحدة التحكم
            var output = string.Join(Environment.NewLine, results);
            await File.WriteAllTextAsync(logFile, output, System.Text.Encoding.UTF8);

            Console.WriteLine(output);
            Console.WriteLine($"\nتم حفظ نتائج الاختبار في: {logFile}");
        }
    }
}
