using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class AddEditUserForm : Form
    {
        private User? _user;
        private bool _isEditMode;

        public AddEditUserForm(User? user = null)
        {
            InitializeComponent();
            _user = user;
            _isEditMode = user != null;
            SetupForm();
            LoadData();
        }

        private void SetupForm()
        {
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد ComboBox للأدوار
            cmbRole.Items.Clear();
            cmbRole.Items.Add(new { Text = "مدير", Value = UserRole.Admin });
            cmbRole.Items.Add(new { Text = "كاشير", Value = UserRole.Cashier });
            cmbRole.DisplayMember = "Text";
            cmbRole.ValueMember = "Value";
            cmbRole.DropDownStyle = ComboBoxStyle.DropDownList;

            // تحديد العنوان
            this.Text = _isEditMode ? "تعديل مستخدم" : "إضافة مستخدم جديد";
            lblTitle.Text = _isEditMode ? "تعديل بيانات المستخدم" : "إضافة مستخدم جديد";

            // إخفاء حقول كلمة المرور في وضع التعديل
            if (_isEditMode)
            {
                lblPassword.Visible = false;
                txtPassword.Visible = false;
                lblConfirmPassword.Visible = false;
                txtConfirmPassword.Visible = false;
                
                // تعديل ارتفاع النموذج
                this.Height -= 100;
            }

            // ربط الأحداث
            txtUsername.Leave += TxtUsername_Leave;
        }

        private void LoadData()
        {
            if (_isEditMode && _user != null)
            {
                txtUsername.Text = _user.Username;
                txtUsername.ReadOnly = true; // منع تعديل اسم المستخدم
                txtFullName.Text = _user.FullName;
                txtEmail.Text = _user.Email;
                chkIsActive.Checked = _user.IsActive;

                // تحديد الدور
                for (int i = 0; i < cmbRole.Items.Count; i++)
                {
                    dynamic item = cmbRole.Items[i];
                    if (item.Value == _user.Role)
                    {
                        cmbRole.SelectedIndex = i;
                        break;
                    }
                }
            }
            else
            {
                // القيم الافتراضية للمستخدم الجديد
                chkIsActive.Checked = true;
                cmbRole.SelectedIndex = 1; // كاشير افتراضياً
            }
        }

        private bool ValidateInput()
        {
            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            if (txtUsername.Text.Length < 3)
            {
                MessageBox.Show("اسم المستخدم يجب أن يكون 3 أحرف على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            // التحقق من الاسم الكامل
            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullName.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني (اختياري)
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                if (!IsValidEmail(txtEmail.Text))
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            // التحقق من الدور
            if (cmbRole.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار دور المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbRole.Focus();
                return false;
            }

            // التحقق من كلمة المرور (في وضع الإضافة فقط)
            if (!_isEditMode)
            {
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text.Length < 6)
                {
                    MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtConfirmPassword.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                dynamic selectedRole = cmbRole.SelectedItem;
                UserRole role = selectedRole.Value;

                if (_isEditMode && _user != null)
                {
                    // تحديث المستخدم
                    _user.FullName = txtFullName.Text.Trim();
                    _user.Email = txtEmail.Text.Trim();
                    _user.Role = role;
                    _user.IsActive = chkIsActive.Checked;

                    var success = await UserService.UpdateUserAsync(_user);
                    if (success)
                    {
                        MessageBox.Show("تم تحديث بيانات المستخدم بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث بيانات المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    // إضافة مستخدم جديد
                    var newUser = new User
                    {
                        Username = txtUsername.Text.Trim(),
                        FullName = txtFullName.Text.Trim(),
                        Email = txtEmail.Text.Trim(),
                        Role = role,
                        IsActive = chkIsActive.Checked
                    };

                    var result = await UserService.CreateUserAsync(newUser, txtPassword.Text);
                    if (result.Success)
                    {
                        MessageBox.Show(result.ErrorMessage, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                    else
                    {
                        MessageBox.Show(result.ErrorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = "حفظ";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtUsername_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtFullName.Focus();
            }
        }

        private void txtFullName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtEmail.Focus();
            }
        }

        private void txtEmail_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                cmbRole.Focus();
            }
        }

        private void cmbRole_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (_isEditMode)
                {
                    chkIsActive.Focus();
                }
                else
                {
                    txtPassword.Focus();
                }
            }
        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtConfirmPassword.Focus();
            }
        }

        private void txtConfirmPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                chkIsActive.Focus();
            }
        }

        private async void TxtUsername_Leave(object? sender, EventArgs e)
        {
            // التحقق من اسم المستخدم فقط في وضع الإضافة
            if (!_isEditMode && !string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                var exists = await UserService.IsUsernameExistsAsync(txtUsername.Text.Trim());
                if (exists)
                {
                    MessageBox.Show("اسم المستخدم موجود مسبقاً. يرجى اختيار اسم مستخدم آخر.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    txtUsername.SelectAll();
                }
            }
        }

        private void AddEditUserForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnCancel_Click(sender, e);
            }
            else if (e.KeyCode == Keys.F12)
            {
                btnSave_Click(sender, e);
            }
        }
    }
}
