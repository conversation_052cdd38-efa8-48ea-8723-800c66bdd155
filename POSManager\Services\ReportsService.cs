using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;

namespace POSManager.Services
{
    public static class ReportsService
    {
        #region Sales Reports

        /// <summary>
        /// الحصول على تقرير المبيعات اليومية
        /// </summary>
        public static async Task<DailySalesReport> GetDailySalesReportAsync(DateTime date)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var report = new DailySalesReport
            {
                Date = date,
                SalesData = new List<SalesSummary>()
            };

            // إجمالي المبيعات اليومية
            var totalSalesCommand = connection.CreateCommand();
            totalSalesCommand.CommandText = @"
                SELECT 
                    COUNT(*) as TotalTransactions,
                    COALESCE(SUM(TotalAmount), 0) as TotalSales,
                    COALESCE(SUM(DiscountAmount), 0) as TotalDiscounts,
                    COALESCE(SUM(TaxAmount), 0) as TotalTax
                FROM Sales 
                WHERE DATE(SaleDate) = DATE(@Date)";
            totalSalesCommand.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));

            using var reader = await totalSalesCommand.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                report.TotalTransactions = Convert.ToInt32(reader[0]);
                report.TotalSales = Convert.ToDecimal(reader[1]);
                report.TotalDiscounts = Convert.ToDecimal(reader[2]);
                report.TotalTax = Convert.ToDecimal(reader[3]);
            }
            reader.Close();

            // تفاصيل المبيعات حسب المنتج
            var productSalesCommand = connection.CreateCommand();
            productSalesCommand.CommandText = @"
                SELECT 
                    sd.ProductName,
                    sd.ProductCode,
                    SUM(sd.Quantity) as TotalQuantity,
                    SUM(sd.TotalPrice) as TotalAmount,
                    AVG(sd.UnitPrice) as AveragePrice
                FROM SaleDetails sd
                INNER JOIN Sales s ON sd.SaleId = s.Id
                WHERE DATE(s.SaleDate) = DATE(@Date)
                GROUP BY sd.ProductId, sd.ProductName, sd.ProductCode
                ORDER BY TotalAmount DESC";
            productSalesCommand.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));

            using var productReader = await productSalesCommand.ExecuteReaderAsync();
            while (await productReader.ReadAsync())
            {
                report.SalesData.Add(new SalesSummary
                {
                    ProductName = Convert.ToString(productReader[0]) ?? "",
                    ProductCode = productReader.IsDBNull(1) ? "" : Convert.ToString(productReader[1]) ?? "",
                    TotalQuantity = Convert.ToInt32(productReader[2]),
                    TotalAmount = Convert.ToDecimal(productReader[3]),
                    AveragePrice = Convert.ToDecimal(productReader[4])
                });
            }

            return report;
        }

        /// <summary>
        /// الحصول على تقرير المبيعات لفترة محددة
        /// </summary>
        public static async Task<PeriodSalesReport> GetPeriodSalesReportAsync(DateTime startDate, DateTime endDate, int? userId = null)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var report = new PeriodSalesReport
            {
                StartDate = startDate,
                EndDate = endDate,
                DailySales = new List<DailySalesSummary>(),
                TopProducts = new List<ProductSalesSummary>(),
                UserPerformance = new List<UserSalesSummary>()
            };

            // بناء الاستعلام مع التصفية حسب المستخدم إذا لزم الأمر
            var userFilter = userId.HasValue ? "AND s.UserId = @UserId" : "";

            // المبيعات اليومية خلال الفترة
            var dailySalesCommand = connection.CreateCommand();
            dailySalesCommand.CommandText = $@"
                SELECT 
                    DATE(SaleDate) as SaleDate,
                    COUNT(*) as TransactionCount,
                    SUM(TotalAmount) as TotalSales,
                    SUM(DiscountAmount) as TotalDiscounts
                FROM Sales s
                WHERE DATE(SaleDate) BETWEEN DATE(@StartDate) AND DATE(@EndDate)
                {userFilter}
                GROUP BY DATE(SaleDate)
                ORDER BY SaleDate";
            
            dailySalesCommand.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
            dailySalesCommand.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));
            if (userId.HasValue)
                dailySalesCommand.Parameters.AddWithValue("@UserId", userId.Value);

            using var dailyReader = await dailySalesCommand.ExecuteReaderAsync();
            while (await dailyReader.ReadAsync())
            {
                report.DailySales.Add(new DailySalesSummary
                {
                    Date = DateTime.Parse(Convert.ToString(dailyReader[0]) ?? DateTime.Today.ToString()),
                    TransactionCount = Convert.ToInt32(dailyReader[1]),
                    TotalSales = Convert.ToDecimal(dailyReader[2]),
                    TotalDiscounts = Convert.ToDecimal(dailyReader[3])
                });
            }
            dailyReader.Close();

            // حساب الإجماليات
            report.TotalSales = report.DailySales.Sum(d => d.TotalSales);
            report.TotalTransactions = report.DailySales.Sum(d => d.TransactionCount);
            report.TotalDiscounts = report.DailySales.Sum(d => d.TotalDiscounts);
            report.AverageDailySales = report.DailySales.Count > 0 ? report.TotalSales / report.DailySales.Count : 0;

            return report;
        }

        /// <summary>
        /// الحصول على تقرير حالة المخزون
        /// </summary>
        public static async Task<InventoryStatusReport> GetInventoryStatusReportAsync()
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var report = new InventoryStatusReport
            {
                Products = new List<ProductInventoryStatus>(),
                GeneratedAt = DateTime.Now
            };

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT 
                    p.Id,
                    p.Name,
                    p.Code,
                    p.Barcode,
                    c.Name as CategoryName,
                    p.Quantity as CurrentStock,
                    p.MinimumQuantity as MinStock,
                    p.Price as SalePrice,
                    p.Cost as PurchasePrice,
                    (p.Quantity * p.Cost) as StockValue
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.IsActive = 1
                ORDER BY 
                    CASE 
                        WHEN p.Quantity <= 0 THEN 1
                        WHEN p.Quantity <= p.MinimumQuantity THEN 2
                        ELSE 3
                    END,
                    p.Name";

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var currentStock = Convert.ToInt32(reader[5]);
                var minStock = Convert.ToInt32(reader[6]);
                
                var product = new ProductInventoryStatus
                {
                    ProductId = Convert.ToInt32(reader[0]),
                    ProductName = Convert.ToString(reader[1]) ?? "",
                    ProductCode = reader.IsDBNull(2) ? "" : Convert.ToString(reader[2]) ?? "",
                    Barcode = reader.IsDBNull(3) ? "" : Convert.ToString(reader[3]) ?? "",
                    CategoryName = reader.IsDBNull(4) ? "غير محدد" : Convert.ToString(reader[4]) ?? "",
                    CurrentStock = currentStock,
                    MinStock = minStock,
                    SalePrice = Convert.ToDecimal(reader[7]),
                    PurchasePrice = Convert.ToDecimal(reader[8]),
                    StockValue = Convert.ToDecimal(reader[9]),
                    Status = currentStock <= 0 ? "نفد المخزون" : 
                             currentStock <= minStock ? "مخزون منخفض" : "متوفر"
                };

                report.Products.Add(product);
            }

            // حساب الإحصائيات
            report.TotalProducts = report.Products.Count;
            report.OutOfStockCount = report.Products.Count(p => p.CurrentStock <= 0);
            report.LowStockCount = report.Products.Count(p => p.CurrentStock > 0 && p.CurrentStock <= p.MinStock);
            report.InStockCount = report.Products.Count(p => p.CurrentStock > p.MinStock);
            report.TotalStockValue = report.Products.Sum(p => p.StockValue);

            return report;
        }

        /// <summary>
        /// الحصول على أفضل المنتجات مبيعاً
        /// </summary>
        public static async Task<List<ProductSalesSummary>> GetTopSellingProductsAsync(DateTime startDate, DateTime endDate, int limit = 10)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var products = new List<ProductSalesSummary>();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT 
                    sd.ProductName,
                    sd.ProductCode,
                    SUM(sd.Quantity) as TotalQuantity,
                    SUM(sd.TotalPrice) as TotalAmount,
                    COUNT(DISTINCT s.Id) as TransactionCount
                FROM SaleDetails sd
                INNER JOIN Sales s ON sd.SaleId = s.Id
                WHERE DATE(s.SaleDate) BETWEEN DATE(@StartDate) AND DATE(@EndDate)
                GROUP BY sd.ProductId, sd.ProductName, sd.ProductCode
                ORDER BY TotalAmount DESC
                LIMIT @Limit";
            
            command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@Limit", limit);

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                products.Add(new ProductSalesSummary
                {
                    ProductName = Convert.ToString(reader[0]) ?? "",
                    ProductCode = reader.IsDBNull(1) ? "" : Convert.ToString(reader[1]) ?? "",
                    TotalQuantity = Convert.ToInt32(reader[2]),
                    TotalAmount = Convert.ToDecimal(reader[3]),
                    TransactionCount = Convert.ToInt32(reader[4])
                });
            }

            return products;
        }

        /// <summary>
        /// الحصول على المنتجات منخفضة المخزون
        /// </summary>
        public static async Task<List<ProductInventoryStatus>> GetLowStockProductsAsync()
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var products = new List<ProductInventoryStatus>();

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT 
                    p.Id, p.Name, p.Code, p.Barcode, c.Name as CategoryName,
                    p.Quantity, p.MinimumQuantity, p.Price, p.Cost, (p.Quantity * p.Cost) as StockValue
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.IsActive = 1 AND p.Quantity <= p.MinimumQuantity
                ORDER BY p.Quantity ASC, p.Name";

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var currentStock = Convert.ToInt32(reader[5]);
                
                products.Add(new ProductInventoryStatus
                {
                    ProductId = Convert.ToInt32(reader[0]),
                    ProductName = Convert.ToString(reader[1]) ?? "",
                    ProductCode = reader.IsDBNull(2) ? "" : Convert.ToString(reader[2]) ?? "",
                    Barcode = reader.IsDBNull(3) ? "" : Convert.ToString(reader[3]) ?? "",
                    CategoryName = reader.IsDBNull(4) ? "غير محدد" : Convert.ToString(reader[4]) ?? "",
                    CurrentStock = currentStock,
                    MinStock = Convert.ToInt32(reader[6]),
                    SalePrice = Convert.ToDecimal(reader[7]),
                    PurchasePrice = Convert.ToDecimal(reader[8]),
                    StockValue = Convert.ToDecimal(reader[9]),
                    Status = currentStock <= 0 ? "نفد المخزون" : "مخزون منخفض"
                });
            }

            return products;
        }

        /// <summary>
        /// الحصول على تقرير أداء المستخدمين
        /// </summary>
        public static async Task<UserPerformanceReport> GetUserPerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var report = new UserPerformanceReport
            {
                StartDate = startDate,
                EndDate = endDate,
                UserDetails = new List<UserPerformanceDetail>(),
                GeneratedAt = DateTime.Now
            };

            var command = connection.CreateCommand();
            command.CommandText = @"
                SELECT
                    u.Id,
                    u.FullName,
                    u.Username,
                    u.Role,
                    COUNT(s.Id) as TotalTransactions,
                    COALESCE(SUM(s.TotalAmount), 0) as TotalSales,
                    COALESCE(AVG(s.TotalAmount), 0) as AverageSaleAmount,
                    COALESCE(SUM(sd.Quantity), 0) as TotalItemsSold,
                    COALESCE(SUM(s.DiscountAmount), 0) as TotalDiscountsGiven,
                    MAX(s.SaleDate) as LastSaleDate
                FROM Users u
                LEFT JOIN Sales s ON u.Id = s.UserId
                    AND DATE(s.SaleDate) BETWEEN DATE(@StartDate) AND DATE(@EndDate)
                LEFT JOIN SaleDetails sd ON s.Id = sd.SaleId
                WHERE u.IsActive = 1
                GROUP BY u.Id, u.FullName, u.Username, u.Role
                ORDER BY TotalSales DESC";

            command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                report.UserDetails.Add(new UserPerformanceDetail
                {
                    UserId = Convert.ToInt32(reader[0]),
                    UserName = Convert.ToString(reader[1]) ?? "",
                    Username = Convert.ToString(reader[2]) ?? "",
                    Role = Convert.ToString(reader[3]) ?? "",
                    TotalTransactions = Convert.ToInt32(reader[4]),
                    TotalSales = Convert.ToDecimal(reader[5]),
                    AverageSaleAmount = Convert.ToDecimal(reader[6]),
                    TotalItemsSold = Convert.ToInt32(reader[7]),
                    TotalDiscountsGiven = Convert.ToDecimal(reader[8]),
                    LastSaleDate = reader.IsDBNull(9) ? null : DateTime.Parse(Convert.ToString(reader[9]) ?? ""),
                    ActiveDays = (endDate - startDate).Days + 1
                });
            }

            return report;
        }

        /// <summary>
        /// الحصول على الملخص المالي
        /// </summary>
        public static async Task<FinancialSummary> GetFinancialSummaryAsync(DateTime startDate, DateTime endDate)
        {
            using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
            await connection.OpenAsync();

            var summary = new FinancialSummary
            {
                StartDate = startDate,
                EndDate = endDate,
                ExpensesByCategory = new List<ExpenseSummary>()
            };

            // إجمالي المبيعات
            var salesCommand = connection.CreateCommand();
            salesCommand.CommandText = @"
                SELECT
                    COALESCE(SUM(TotalAmount), 0) as TotalRevenue,
                    COALESCE(SUM(DiscountAmount), 0) as TotalDiscounts,
                    COALESCE(SUM(TaxAmount), 0) as TotalTax,
                    COUNT(*) as TransactionCount
                FROM Sales
                WHERE DATE(SaleDate) BETWEEN DATE(@StartDate) AND DATE(@EndDate)";

            salesCommand.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
            salesCommand.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

            using var salesReader = await salesCommand.ExecuteReaderAsync();
            if (await salesReader.ReadAsync())
            {
                summary.TotalRevenue = Convert.ToDecimal(salesReader[0]);
                summary.TotalDiscounts = Convert.ToDecimal(salesReader[1]);
                summary.TotalTax = Convert.ToDecimal(salesReader[2]);
                summary.TotalTransactions = Convert.ToInt32(salesReader[3]);
            }
            salesReader.Close();

            // إجمالي المصروفات
            var expensesCommand = connection.CreateCommand();
            expensesCommand.CommandText = @"
                SELECT COALESCE(SUM(Amount), 0) as TotalExpenses
                FROM Expenses
                WHERE DATE(ExpenseDate) BETWEEN DATE(@StartDate) AND DATE(@EndDate)";

            expensesCommand.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
            expensesCommand.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

            using var expensesReader = await expensesCommand.ExecuteReaderAsync();
            if (await expensesReader.ReadAsync())
            {
                summary.TotalExpenses = Convert.ToDecimal(expensesReader[0]);
            }
            expensesReader.Close();

            return summary;
        }

        #endregion
    }
}
