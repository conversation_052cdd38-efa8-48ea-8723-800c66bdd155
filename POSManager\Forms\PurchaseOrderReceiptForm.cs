using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class PurchaseOrderReceiptForm : Form
    {
        private readonly PurchaseOrderService _purchaseOrderService;
        private readonly User _currentUser;
        private readonly PurchaseOrder _purchaseOrder;
        private TextBox txtReceiptNumber, txtNotes;
        private DateTimePicker dtpReceiptDate;
        private DataGridView dgvReceiptItems;
        private Button btnSave, btnCancel;
        private Label lblOrderNumber, lblSupplierName, lblOrderDate;
        private List<PurchaseOrderReceiptItem> _receiptItems;

        public PurchaseOrderReceiptForm(PurchaseOrder purchaseOrder, User currentUser)
        {
            _purchaseOrder = purchaseOrder;
            _currentUser = currentUser;
            _purchaseOrderService = PurchaseOrderService.Instance;
            _receiptItems = new List<PurchaseOrderReceiptItem>();
            
            InitializeComponent();
            SetupForm();
            LoadOrderInfo();
            PrepareReceiptItems();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النافذة الأساسية
            this.Text = "استلام أمر الشراء";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // معلومات أمر الشراء
            var orderInfoPanel = new Panel { Height = 100, Dock = DockStyle.Top };
            
            var lblOrderNumberLabel = new Label 
            { 
                Text = "رقم أمر الشراء:", 
                Location = new Point(10, 15), 
                Size = new Size(100, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };
            
            lblOrderNumber = new Label 
            { 
                Location = new Point(120, 15), 
                Size = new Size(150, 23), 
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblSupplierLabel = new Label 
            { 
                Text = "المورد:", 
                Location = new Point(290, 15), 
                Size = new Size(60, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };
            
            lblSupplierName = new Label 
            { 
                Location = new Point(360, 15), 
                Size = new Size(200, 23), 
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var lblOrderDateLabel = new Label 
            { 
                Text = "تاريخ الأمر:", 
                Location = new Point(10, 45), 
                Size = new Size(100, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };
            
            lblOrderDate = new Label 
            { 
                Location = new Point(120, 45), 
                Size = new Size(150, 23), 
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft
            };

            orderInfoPanel.Controls.AddRange(new Control[] 
            { 
                lblOrderNumberLabel, lblOrderNumber, lblSupplierLabel, lblSupplierName, 
                lblOrderDateLabel, lblOrderDate 
            });

            // معلومات الاستلام
            var receiptInfoPanel = new Panel { Height = 80, Dock = DockStyle.Top };
            
            var lblReceiptNumber = new Label 
            { 
                Text = "رقم الاستلام:", 
                Location = new Point(10, 15), 
                Size = new Size(100, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };
            
            txtReceiptNumber = new TextBox 
            { 
                Location = new Point(120, 15), 
                Size = new Size(150, 23), 
                ReadOnly = true 
            };

            var lblReceiptDate = new Label 
            { 
                Text = "تاريخ الاستلام:", 
                Location = new Point(290, 15), 
                Size = new Size(100, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };
            
            dtpReceiptDate = new DateTimePicker 
            { 
                Location = new Point(400, 15), 
                Size = new Size(120, 23), 
                Value = DateTime.Now 
            };

            var lblNotes = new Label 
            { 
                Text = "ملاحظات:", 
                Location = new Point(10, 45), 
                Size = new Size(100, 23), 
                TextAlign = ContentAlignment.MiddleRight 
            };
            
            txtNotes = new TextBox 
            { 
                Location = new Point(120, 45), 
                Size = new Size(400, 23) 
            };

            receiptInfoPanel.Controls.AddRange(new Control[] 
            { 
                lblReceiptNumber, txtReceiptNumber, lblReceiptDate, dtpReceiptDate, 
                lblNotes, txtNotes 
            });

            // أزرار التحكم
            var buttonPanel = new Panel { Height = 50, Dock = DockStyle.Bottom };
            
            btnSave = new Button 
            { 
                Text = "حفظ الاستلام", 
                Location = new Point(10, 10), 
                Size = new Size(100, 30), 
                BackColor = Color.Green, 
                ForeColor = Color.White 
            };

            btnCancel = new Button 
            { 
                Text = "إلغاء", 
                Location = new Point(120, 10), 
                Size = new Size(80, 30), 
                BackColor = Color.Gray, 
                ForeColor = Color.White 
            };

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });

            // جدول عناصر الاستلام
            dgvReceiptItems = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupReceiptItemsGrid();

            mainPanel.Controls.Add(dgvReceiptItems);
            mainPanel.Controls.Add(buttonPanel);
            mainPanel.Controls.Add(receiptInfoPanel);
            mainPanel.Controls.Add(orderInfoPanel);
            this.Controls.Add(mainPanel);

            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            dgvReceiptItems.CellEndEdit += DgvReceiptItems_CellEndEdit;
        }

        private void SetupReceiptItemsGrid()
        {
            dgvReceiptItems.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "ProductName", HeaderText = "المنتج", Width = 200, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "ProductCode", HeaderText = "الكود", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "OrderedQuantity", HeaderText = "الكمية المطلوبة", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "PreviouslyReceived", HeaderText = "مستلم سابقاً", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "RemainingQuantity", HeaderText = "الكمية المتبقية", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "ReceivedQuantity", HeaderText = "الكمية المستلمة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "UnitCost", HeaderText = "سعر الوحدة", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", Width = 150 }
            });
        }

        private void LoadOrderInfo()
        {
            lblOrderNumber.Text = _purchaseOrder.OrderNumber;
            lblSupplierName.Text = _purchaseOrder.SupplierName;
            lblOrderDate.Text = _purchaseOrder.OrderDate.ToString("yyyy/MM/dd");
        }

        private async void PrepareReceiptItems()
        {
            try
            {
                txtReceiptNumber.Text = await _purchaseOrderService.GenerateReceiptNumberAsync();

                _receiptItems.Clear();
                
                foreach (var orderItem in _purchaseOrder.Items)
                {
                    var remainingQuantity = orderItem.Quantity - orderItem.ReceivedQuantity;
                    
                    var receiptItem = new PurchaseOrderReceiptItem
                    {
                        ProductId = orderItem.ProductId,
                        ProductName = orderItem.ProductName,
                        ProductCode = orderItem.ProductCode,
                        OrderedQuantity = orderItem.Quantity,
                        PreviouslyReceived = orderItem.ReceivedQuantity,
                        RemainingQuantity = remainingQuantity,
                        ReceivedQuantity = remainingQuantity, // افتراضياً نستلم الكمية المتبقية
                        UnitCost = orderItem.UnitCost,
                        Notes = ""
                    };

                    _receiptItems.Add(receiptItem);
                }

                dgvReceiptItems.DataSource = _receiptItems;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحضير عناصر الاستلام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvReceiptItems_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == dgvReceiptItems.Columns["ReceivedQuantity"].Index)
            {
                var receivedQuantity = Convert.ToDecimal(dgvReceiptItems.Rows[e.RowIndex].Cells["ReceivedQuantity"].Value ?? 0);
                var remainingQuantity = Convert.ToDecimal(dgvReceiptItems.Rows[e.RowIndex].Cells["RemainingQuantity"].Value ?? 0);

                if (receivedQuantity > remainingQuantity)
                {
                    MessageBox.Show("الكمية المستلمة لا يمكن أن تكون أكبر من الكمية المتبقية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dgvReceiptItems.Rows[e.RowIndex].Cells["ReceivedQuantity"].Value = remainingQuantity;
                }
                else if (receivedQuantity < 0)
                {
                    MessageBox.Show("الكمية المستلمة لا يمكن أن تكون سالبة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dgvReceiptItems.Rows[e.RowIndex].Cells["ReceivedQuantity"].Value = 0;
                }

                // تحديث البيانات في القائمة
                _receiptItems[e.RowIndex].ReceivedQuantity = Convert.ToDecimal(dgvReceiptItems.Rows[e.RowIndex].Cells["ReceivedQuantity"].Value ?? 0);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (await SaveReceiptAsync())
            {
                MessageBox.Show("تم حفظ استلام أمر الشراء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async Task<bool> SaveReceiptAsync()
        {
            try
            {
                if (!ValidateReceipt())
                    return false;

                var receipt = new PurchaseOrderReceipt
                {
                    PurchaseOrderId = _purchaseOrder.Id,
                    ReceiptNumber = txtReceiptNumber.Text,
                    ReceiptDate = dtpReceiptDate.Value,
                    ReceivedBy = _currentUser.Id,
                    Notes = txtNotes.Text,
                    CreatedAt = DateTime.Now,
                    Items = _receiptItems.Where(i => i.ReceivedQuantity > 0).ToList()
                };

                var receiptId = await _purchaseOrderService.CreateReceiptAsync(receipt);
                return receiptId > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الاستلام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private bool ValidateReceipt()
        {
            if (string.IsNullOrWhiteSpace(txtReceiptNumber.Text))
            {
                MessageBox.Show("رقم الاستلام مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReceiptNumber.Focus();
                return false;
            }

            var totalReceived = _receiptItems.Sum(i => i.ReceivedQuantity);
            if (totalReceived <= 0)
            {
                MessageBox.Show("يجب استلام كمية أكبر من صفر", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }
    }
}
