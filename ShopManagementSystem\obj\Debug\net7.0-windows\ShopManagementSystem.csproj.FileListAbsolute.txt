D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.csproj.AssemblyReference.cache
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.GeneratedMSBuildEditorConfig.editorconfig
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.AssemblyInfoInputs.cache
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.AssemblyInfo.cs
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.csproj.CoreCompileInputs.cache
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\ShopManagementSystem.exe
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\ShopManagementSystem.deps.json
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\ShopManagementSystem.runtimeconfig.json
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\ShopManagementSystem.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\ShopManagementSystem.pdb
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\Microsoft.Data.Sqlite.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\SQLitePCLRaw.batteries_v2.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\SQLitePCLRaw.core.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\browser-wasm\nativeassets\net7.0\e_sqlite3.a
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-musl-s390x\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\win-arm\native\e_sqlite3.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\win-x64\native\e_sqlite3.dll
D:\sys\ShopManagementSystem\bin\Debug\net7.0-windows\runtimes\win-x86\native\e_sqlite3.dll
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.csproj.CopyComplete
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.dll
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\refint\ShopManagementSystem.dll
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.pdb
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ShopManagementSystem.genruntimeconfig.cache
D:\sys\ShopManagementSystem\obj\Debug\net7.0-windows\ref\ShopManagementSystem.dll
