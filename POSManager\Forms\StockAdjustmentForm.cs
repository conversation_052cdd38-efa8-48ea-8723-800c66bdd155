using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class StockAdjustmentForm : Form
    {
        private readonly User _currentUser;
        private readonly AdvancedInventoryService _inventoryService;
        private List<StockAdjustment> _adjustments;
        private List<StockAdjustmentItem> _adjustmentItems;
        private int _currentAdjustmentId = 0;
        private bool _isEditing = false;

        // UI Controls
        private TabControl tabControl;
        private DataGridView dgvAdjustments, dgvAdjustmentItems;
        private ComboBox cmbReason, cmbStatus, cmbProduct;
        private DateTimePicker dtpFromDate, dtpToDate, dtpAdjustmentDate;
        private TextBox txtSearch, txtNotes, txtReference;
        private NumericUpDown nudCurrentStock, nudAdjustedStock, nudVariance;
        private Button btnNew, btnEdit, btnSave, btnCancel, btnDelete, btnApprove;
        private Button btnAddItem, btnEditItem, btnDeleteItem, btnSearch, btnRefresh;
        private Button btnPhysicalCount, btnExport;
        private Label lblTotalVariance, lblItemCount;

        public StockAdjustmentForm(User currentUser)
        {
            _currentUser = currentUser;
            _inventoryService = AdvancedInventoryService.Instance;
            _adjustments = new List<StockAdjustment>();
            _adjustmentItems = new List<StockAdjustmentItem>();
            
            InitializeComponent();
            SetupForm();
            _ = LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1200, 800);
            this.Text = "إدارة تعديل المخزون";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;

            // Create TabControl
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                RightToLeftLayout = true
            };

            // Create tabs
            var tabAdjustments = new TabPage("قائمة التعديلات");
            var tabAdjustmentDetails = new TabPage("تفاصيل التعديل");

            SetupAdjustmentsTab(tabAdjustments);
            SetupAdjustmentDetailsTab(tabAdjustmentDetails);

            tabControl.TabPages.Add(tabAdjustments);
            tabControl.TabPages.Add(tabAdjustmentDetails);

            this.Controls.Add(tabControl);
        }

        private void SetupAdjustmentsTab(TabPage tab)
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Search panel
            var searchPanel = new Panel { Height = 80, Dock = DockStyle.Top };
            
            txtSearch = new TextBox { Location = new Point(10, 15), Size = new Size(200, 25) };
            btnSearch = new Button { Text = "بحث", Location = new Point(220, 14), Size = new Size(80, 27) };
            btnRefresh = new Button { Text = "تحديث", Location = new Point(310, 14), Size = new Size(80, 27) };
            
            dtpFromDate = new DateTimePicker { Location = new Point(10, 45), Size = new Size(150, 25) };
            dtpToDate = new DateTimePicker { Location = new Point(170, 45), Size = new Size(150, 25) };
            
            cmbStatus = new ComboBox 
            { 
                Location = new Point(330, 45), 
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatus.Items.AddRange(new[] { "الكل", "مسودة", "معتمد", "مرفوض" });
            cmbStatus.SelectedIndex = 0;

            searchPanel.Controls.AddRange(new Control[] { txtSearch, btnSearch, btnRefresh, dtpFromDate, dtpToDate, cmbStatus });

            // Buttons panel
            var buttonsPanel = new Panel { Height = 50, Dock = DockStyle.Bottom };
            
            btnNew = new Button { Text = "جديد", Location = new Point(10, 10), Size = new Size(80, 30) };
            btnEdit = new Button { Text = "تعديل", Location = new Point(100, 10), Size = new Size(80, 30) };
            btnDelete = new Button { Text = "حذف", Location = new Point(190, 10), Size = new Size(80, 30) };
            btnApprove = new Button { Text = "اعتماد", Location = new Point(280, 10), Size = new Size(80, 30) };
            btnPhysicalCount = new Button { Text = "جرد فعلي", Location = new Point(370, 10), Size = new Size(100, 30) };
            btnExport = new Button { Text = "تصدير", Location = new Point(480, 10), Size = new Size(80, 30) };

            buttonsPanel.Controls.AddRange(new Control[] { btnNew, btnEdit, btnDelete, btnApprove, btnPhysicalCount, btnExport });

            // DataGridView
            dgvAdjustments = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RightToLeft = RightToLeft.Yes
            };

            SetupAdjustmentsGrid();

            panel.Controls.Add(dgvAdjustments);
            panel.Controls.Add(searchPanel);
            panel.Controls.Add(buttonsPanel);
            tab.Controls.Add(panel);
        }

        private void SetupAdjustmentDetailsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Header panel
            var headerPanel = new Panel { Height = 120, Dock = DockStyle.Top };
            
            var lblReference = new Label { Text = "رقم المرجع:", Location = new Point(10, 15), Size = new Size(80, 20) };
            txtReference = new TextBox { Location = new Point(100, 12), Size = new Size(150, 25), ReadOnly = true };
            
            var lblDate = new Label { Text = "تاريخ التعديل:", Location = new Point(270, 15), Size = new Size(80, 20) };
            dtpAdjustmentDate = new DateTimePicker { Location = new Point(360, 12), Size = new Size(150, 25) };
            
            var lblReason = new Label { Text = "سبب التعديل:", Location = new Point(10, 45), Size = new Size(80, 20) };
            cmbReason = new ComboBox 
            { 
                Location = new Point(100, 42), 
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbReason.Items.AddRange(new[] { "جرد فعلي", "تلف", "انتهاء صلاحية", "خطأ في الإدخال", "أخرى" });
            
            var lblNotes = new Label { Text = "ملاحظات:", Location = new Point(10, 75), Size = new Size(80, 20) };
            txtNotes = new TextBox { Location = new Point(100, 72), Size = new Size(400, 25) };

            headerPanel.Controls.AddRange(new Control[] { 
                lblReference, txtReference, lblDate, dtpAdjustmentDate,
                lblReason, cmbReason, lblNotes, txtNotes 
            });

            // Items panel
            var itemsPanel = new Panel { Dock = DockStyle.Fill };
            
            // Items buttons
            var itemButtonsPanel = new Panel { Height = 50, Dock = DockStyle.Top };
            
            btnAddItem = new Button { Text = "إضافة منتج", Location = new Point(10, 10), Size = new Size(100, 30) };
            btnEditItem = new Button { Text = "تعديل", Location = new Point(120, 10), Size = new Size(80, 30) };
            btnDeleteItem = new Button { Text = "حذف", Location = new Point(210, 10), Size = new Size(80, 30) };
            
            lblItemCount = new Label { Text = "عدد المنتجات: 0", Location = new Point(320, 15), Size = new Size(120, 20) };
            lblTotalVariance = new Label { Text = "إجمالي التباين: 0", Location = new Point(450, 15), Size = new Size(120, 20) };

            itemButtonsPanel.Controls.AddRange(new Control[] { btnAddItem, btnEditItem, btnDeleteItem, lblItemCount, lblTotalVariance });

            // Items grid
            dgvAdjustmentItems = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RightToLeft = RightToLeft.Yes
            };

            SetupAdjustmentItemsGrid();

            itemsPanel.Controls.Add(dgvAdjustmentItems);
            itemsPanel.Controls.Add(itemButtonsPanel);

            // Footer panel
            var footerPanel = new Panel { Height = 50, Dock = DockStyle.Bottom };
            
            btnSave = new Button { Text = "حفظ", Location = new Point(10, 10), Size = new Size(80, 30) };
            btnCancel = new Button { Text = "إلغاء", Location = new Point(100, 10), Size = new Size(80, 30) };

            footerPanel.Controls.AddRange(new Control[] { btnSave, btnCancel });

            mainPanel.Controls.Add(itemsPanel);
            mainPanel.Controls.Add(headerPanel);
            mainPanel.Controls.Add(footerPanel);
            tab.Controls.Add(mainPanel);
        }

        private void SetupForm()
        {
            // Wire up events
            btnSearch.Click += BtnSearch_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnNew.Click += BtnNew_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnApprove.Click += BtnApprove_Click;
            btnPhysicalCount.Click += BtnPhysicalCount_Click;
            btnExport.Click += BtnExport_Click;
            
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnAddItem.Click += BtnAddItem_Click;
            btnEditItem.Click += BtnEditItem_Click;
            btnDeleteItem.Click += BtnDeleteItem_Click;
            
            dgvAdjustments.SelectionChanged += DgvAdjustments_SelectionChanged;
            dgvAdjustmentItems.CellEndEdit += DgvAdjustmentItems_CellEndEdit;

            SetFormMode(false);
        }

        private void SetupAdjustmentsGrid()
        {
            dgvAdjustments.Columns.Clear();
            dgvAdjustments.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "ReferenceNumber", HeaderText = "رقم المرجع", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "AdjustmentDate", HeaderText = "التاريخ", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Reason", HeaderText = "السبب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "الحالة", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "ItemCount", HeaderText = "عدد المنتجات", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TotalVariance", HeaderText = "إجمالي التباين", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "CreatedBy", HeaderText = "أنشئ بواسطة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", Width = 200 }
            });
        }

        private void SetupAdjustmentItemsGrid()
        {
            dgvAdjustmentItems.Columns.Clear();
            dgvAdjustmentItems.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "ProductId", HeaderText = "معرف المنتج", Visible = false },
                new DataGridViewTextBoxColumn { Name = "ProductCode", HeaderText = "كود المنتج", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "ProductName", HeaderText = "اسم المنتج", Width = 200, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "CurrentStock", HeaderText = "المخزون الحالي", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "AdjustedStock", HeaderText = "المخزون المعدل", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Variance", HeaderText = "التباين", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "Reason", HeaderText = "السبب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", Width = 150 }
            });

            // Make AdjustedStock column editable
            dgvAdjustmentItems.Columns["AdjustedStock"].ReadOnly = false;
            dgvAdjustmentItems.Columns["Reason"].ReadOnly = false;
            dgvAdjustmentItems.Columns["Notes"].ReadOnly = false;
        }

        private async Task LoadDataAsync()
        {
            try
            {
                await LoadAdjustmentsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadAdjustmentsAsync()
        {
            try
            {
                _adjustments = await _inventoryService.GetStockAdjustmentsAsync();
                
                var displayData = _adjustments.Select(a => new
                {
                    Id = a.Id,
                    ReferenceNumber = a.ReferenceNumber,
                    AdjustmentDate = a.AdjustmentDate.ToString("yyyy-MM-dd"),
                    Reason = GetReasonText(a.Reason),
                    Status = GetStatusText(a.Status),
                    ItemCount = a.Items?.Count ?? 0,
                    TotalVariance = a.Items?.Sum(i => i.Variance) ?? 0,
                    CreatedBy = a.CreatedBy,
                    Notes = a.Notes
                }).ToList();

                dgvAdjustments.DataSource = displayData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التعديلات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetReasonText(string reason)
        {
            return reason switch
            {
                "PhysicalCount" => "جرد فعلي",
                "Damage" => "تلف",
                "Expiry" => "انتهاء صلاحية",
                "DataEntry" => "خطأ في الإدخال",
                "Other" => "أخرى",
                _ => reason
            };
        }

        private string GetStatusText(string status)
        {
            return status switch
            {
                "Draft" => "مسودة",
                "Approved" => "معتمد",
                "Rejected" => "مرفوض",
                _ => status
            };
        }

        private void SetFormMode(bool isEditing)
        {
            _isEditing = isEditing;

            // Enable/disable controls based on mode
            txtReference.ReadOnly = !isEditing;
            dtpAdjustmentDate.Enabled = isEditing;
            cmbReason.Enabled = isEditing;
            txtNotes.ReadOnly = !isEditing;

            btnAddItem.Enabled = isEditing;
            btnEditItem.Enabled = isEditing;
            btnDeleteItem.Enabled = isEditing;

            btnSave.Enabled = isEditing;
            btnCancel.Enabled = isEditing;

            dgvAdjustmentItems.ReadOnly = !isEditing;
        }

        // Event Handlers
        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            await LoadAdjustmentsAsync();
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadAdjustmentsAsync();
        }

        private void BtnNew_Click(object sender, EventArgs e)
        {
            _currentAdjustmentId = 0;
            _adjustmentItems.Clear();

            txtReference.Text = GenerateReferenceNumber();
            dtpAdjustmentDate.Value = DateTime.Now;
            cmbReason.SelectedIndex = 0;
            txtNotes.Text = "";

            dgvAdjustmentItems.DataSource = null;
            UpdateSummary();

            SetFormMode(true);
            tabControl.SelectedIndex = 1;
        }

        private async void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvAdjustments.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تعديل للتحرير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedId = Convert.ToInt32(dgvAdjustments.SelectedRows[0].Cells["Id"].Value);
            var adjustment = _adjustments.FirstOrDefault(a => a.Id == selectedId);

            if (adjustment == null) return;

            if (adjustment.Status == "Approved")
            {
                MessageBox.Show("لا يمكن تعديل التعديل المعتمد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            await LoadAdjustmentDetails(selectedId);
            SetFormMode(true);
            tabControl.SelectedIndex = 1;
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvAdjustments.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تعديل للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedId = Convert.ToInt32(dgvAdjustments.SelectedRows[0].Cells["Id"].Value);
            var adjustment = _adjustments.FirstOrDefault(a => a.Id == selectedId);

            if (adjustment?.Status == "Approved")
            {
                MessageBox.Show("لا يمكن حذف التعديل المعتمد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا التعديل؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _inventoryService.DeleteStockAdjustmentAsync(selectedId);
                    await LoadAdjustmentsAsync();
                    MessageBox.Show("تم حذف التعديل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف التعديل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void BtnApprove_Click(object sender, EventArgs e)
        {
            if (dgvAdjustments.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تعديل للاعتماد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedId = Convert.ToInt32(dgvAdjustments.SelectedRows[0].Cells["Id"].Value);
            var adjustment = _adjustments.FirstOrDefault(a => a.Id == selectedId);

            if (adjustment?.Status == "Approved")
            {
                MessageBox.Show("التعديل معتمد بالفعل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من اعتماد هذا التعديل؟\nسيتم تحديث المخزون تلقائياً",
                "تأكيد الاعتماد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    await _inventoryService.ApproveStockAdjustmentAsync(selectedId, _currentUser.Id);
                    await LoadAdjustmentsAsync();
                    MessageBox.Show("تم اعتماد التعديل وتحديث المخزون بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في اعتماد التعديل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnPhysicalCount_Click(object sender, EventArgs e)
        {
            var physicalCountForm = new PhysicalCountForm(_currentUser);
            if (physicalCountForm.ShowDialog() == DialogResult.OK)
            {
                _ = LoadAdjustmentsAsync();
            }
        }

        private async void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|Excel files (*.xlsx)|*.xlsx",
                    DefaultExt = "csv",
                    FileName = $"stock_adjustments_{DateTime.Now:yyyyMMdd}.csv"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    await ExportAdjustmentsAsync(saveDialog.FileName);
                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateAdjustment())
                return;

            try
            {
                var adjustment = new StockAdjustment
                {
                    Id = _currentAdjustmentId,
                    ReferenceNumber = txtReference.Text,
                    AdjustmentDate = dtpAdjustmentDate.Value,
                    Reason = GetReasonValue(cmbReason.Text),
                    Notes = txtNotes.Text,
                    Status = "Draft",
                    CreatedBy = _currentUser.Username,
                    CreatedAt = DateTime.Now,
                    Items = _adjustmentItems
                };

                if (_currentAdjustmentId == 0)
                {
                    await _inventoryService.CreateStockAdjustmentAsync(adjustment);
                    MessageBox.Show("تم إنشاء التعديل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    await _inventoryService.UpdateStockAdjustmentAsync(adjustment);
                    MessageBox.Show("تم تحديث التعديل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                SetFormMode(false);
                await LoadAdjustmentsAsync();
                tabControl.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            SetFormMode(false);
            tabControl.SelectedIndex = 0;
        }

        private string GenerateReferenceNumber()
        {
            return $"ADJ-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
        }

        private string GetReasonValue(string reasonText)
        {
            return reasonText switch
            {
                "جرد فعلي" => "PhysicalCount",
                "تلف" => "Damage",
                "انتهاء صلاحية" => "Expiry",
                "خطأ في الإدخال" => "DataEntry",
                "أخرى" => "Other",
                _ => "Other"
            };
        }

        private bool ValidateAdjustment()
        {
            if (string.IsNullOrWhiteSpace(txtReference.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المرجع", "خطأ في التحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReference.Focus();
                return false;
            }

            if (cmbReason.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار سبب التعديل", "خطأ في التحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbReason.Focus();
                return false;
            }

            if (_adjustmentItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة منتج واحد على الأقل", "خطأ في التحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void UpdateSummary()
        {
            lblItemCount.Text = $"عدد المنتجات: {_adjustmentItems.Count}";
            lblTotalVariance.Text = $"إجمالي التباين: {_adjustmentItems.Sum(i => i.Variance)}";
        }

        private async Task ExportAdjustmentsAsync(string fileName)
        {
            var csv = new StringBuilder();
            csv.AppendLine("رقم المرجع,التاريخ,السبب,الحالة,عدد المنتجات,إجمالي التباين,أنشئ بواسطة,ملاحظات");

            foreach (var adjustment in _adjustments)
            {
                csv.AppendLine($"{adjustment.ReferenceNumber},{adjustment.AdjustmentDate:yyyy-MM-dd}," +
                             $"{GetReasonText(adjustment.Reason)},{GetStatusText(adjustment.Status)}," +
                             $"{adjustment.Items?.Count ?? 0},{adjustment.Items?.Sum(i => i.Variance) ?? 0}," +
                             $"{adjustment.CreatedBy},{adjustment.Notes}");
            }

            await System.IO.File.WriteAllTextAsync(fileName, csv.ToString(), Encoding.UTF8);
        }

        private void BtnAddItem_Click(object sender, EventArgs e)
        {
            var itemForm = new StockAdjustmentItemForm();
            if (itemForm.ShowDialog() == DialogResult.OK)
            {
                var item = itemForm.AdjustmentItem;
                if (item != null)
                {
                    _adjustmentItems.Add(item);
                    RefreshItemsGrid();
                    UpdateSummary();
                }
            }
        }

        private void BtnEditItem_Click(object sender, EventArgs e)
        {
            if (dgvAdjustmentItems.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedIndex = dgvAdjustmentItems.SelectedRows[0].Index;
            var item = _adjustmentItems[selectedIndex];

            var itemForm = new StockAdjustmentItemForm(item);
            if (itemForm.ShowDialog() == DialogResult.OK)
            {
                _adjustmentItems[selectedIndex] = itemForm.AdjustmentItem;
                RefreshItemsGrid();
                UpdateSummary();
            }
        }

        private void BtnDeleteItem_Click(object sender, EventArgs e)
        {
            if (dgvAdjustmentItems.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المنتج؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                var selectedIndex = dgvAdjustmentItems.SelectedRows[0].Index;
                _adjustmentItems.RemoveAt(selectedIndex);
                RefreshItemsGrid();
                UpdateSummary();
            }
        }

        private async void DgvAdjustments_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvAdjustments.SelectedRows.Count > 0 && !_isEditing)
            {
                var selectedId = Convert.ToInt32(dgvAdjustments.SelectedRows[0].Cells["Id"].Value);
                await LoadAdjustmentDetails(selectedId);
            }
        }

        private void DgvAdjustmentItems_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.RowIndex < _adjustmentItems.Count)
            {
                var item = _adjustmentItems[e.RowIndex];
                var row = dgvAdjustmentItems.Rows[e.RowIndex];

                if (e.ColumnIndex == dgvAdjustmentItems.Columns["AdjustedStock"].Index)
                {
                    if (decimal.TryParse(row.Cells["AdjustedStock"].Value?.ToString(), out decimal adjustedStock))
                    {
                        item.AdjustedStock = adjustedStock;
                        item.Variance = adjustedStock - item.CurrentStock;
                        row.Cells["Variance"].Value = item.Variance;
                        UpdateSummary();
                    }
                }
                else if (e.ColumnIndex == dgvAdjustmentItems.Columns["Reason"].Index)
                {
                    item.Reason = row.Cells["Reason"].Value?.ToString() ?? "";
                }
                else if (e.ColumnIndex == dgvAdjustmentItems.Columns["Notes"].Index)
                {
                    item.Notes = row.Cells["Notes"].Value?.ToString() ?? "";
                }
            }
        }

        private async Task LoadAdjustmentDetails(int adjustmentId)
        {
            try
            {
                _currentAdjustmentId = adjustmentId;
                var adjustment = await _inventoryService.GetStockAdjustmentAsync(adjustmentId);

                if (adjustment != null)
                {
                    txtReference.Text = adjustment.ReferenceNumber;
                    dtpAdjustmentDate.Value = adjustment.AdjustmentDate;
                    cmbReason.Text = GetReasonText(adjustment.Reason);
                    txtNotes.Text = adjustment.Notes ?? "";

                    _adjustmentItems = adjustment.Items?.ToList() ?? new List<StockAdjustmentItem>();
                    RefreshItemsGrid();
                    UpdateSummary();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل التعديل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshItemsGrid()
        {
            var displayData = _adjustmentItems.Select(item => new
            {
                Id = item.Id,
                ProductId = item.ProductId,
                ProductCode = item.ProductCode,
                ProductName = item.ProductName,
                CurrentStock = item.CurrentStock,
                AdjustedStock = item.AdjustedStock,
                Variance = item.Variance,
                Reason = item.Reason,
                Notes = item.Notes
            }).ToList();

            dgvAdjustmentItems.DataSource = displayData;
        }
    }
}
