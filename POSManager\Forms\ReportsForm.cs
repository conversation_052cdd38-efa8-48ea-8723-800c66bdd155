using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class ReportsForm : Form
    {
        private User _currentUser;
        private ReportFilter _currentFilter;

        public ReportsForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            _currentFilter = new ReportFilter();
            SetupForm();
            LoadReportTypes();
        }

        private void SetupForm()
        {
            this.Text = "التقارير والإحصائيات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;

            // إعداد التواريخ الافتراضية
            dtpStartDate.Value = DateTime.Today.AddDays(-30);
            dtpEndDate.Value = DateTime.Today;
            
            // إعداد الأحداث
            btnGenerateReport.Click += BtnGenerateReport_Click;
            btnExportReport.Click += BtnExportReport_Click;
            btnClose.Click += (s, e) => this.Close();
            cmbReportType.SelectedIndexChanged += CmbReportType_SelectedIndexChanged;
            
            // إعداد الصلاحيات
            SetupPermissions();
        }

        private void SetupPermissions()
        {
            var permissions = UserPermissions.GetPermissions(_currentUser.Role);
            
            if (!permissions.CanViewReports)
            {
                MessageBox.Show("ليس لديك صلاحية لعرض التقارير", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                this.Close();
                return;
            }
        }

        private void LoadReportTypes()
        {
            cmbReportType.Items.Clear();
            cmbReportType.Items.Add(new { Text = "تقرير المبيعات اليومية", Value = ReportType.DailySales });
            cmbReportType.Items.Add(new { Text = "تقرير المبيعات لفترة", Value = ReportType.PeriodSales });
            cmbReportType.Items.Add(new { Text = "تقرير حالة المخزون", Value = ReportType.InventoryStatus });
            cmbReportType.Items.Add(new { Text = "تقرير أداء المستخدمين", Value = ReportType.UserPerformance });
            cmbReportType.Items.Add(new { Text = "الملخص المالي", Value = ReportType.FinancialSummary });
            cmbReportType.Items.Add(new { Text = "أفضل المنتجات مبيعاً", Value = ReportType.TopProducts });
            cmbReportType.Items.Add(new { Text = "تنبيه المخزون المنخفض", Value = ReportType.LowStockAlert });

            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.DropDownStyle = ComboBoxStyle.DropDownList;
            
            if (cmbReportType.Items.Count > 0)
                cmbReportType.SelectedIndex = 0;
        }

        private void CmbReportType_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (cmbReportType.SelectedItem == null) return;

            dynamic selectedItem = cmbReportType.SelectedItem;
            ReportType reportType = selectedItem.Value;

            // إظهار/إخفاء عناصر التحكم حسب نوع التقرير
            bool showDateRange = reportType != ReportType.InventoryStatus && reportType != ReportType.LowStockAlert;
            bool showUserFilter = reportType == ReportType.PeriodSales && _currentUser.Role == UserRole.Admin;

            dtpStartDate.Visible = showDateRange;
            dtpEndDate.Visible = showDateRange;
            lblStartDate.Visible = showDateRange;
            lblEndDate.Visible = showDateRange;
            
            cmbUser.Visible = showUserFilter;
            lblUser.Visible = showUserFilter;

            if (showUserFilter)
            {
                LoadUsers();
            }
        }

        private async void LoadUsers()
        {
            try
            {
                var users = await UserService.GetAllUsersAsync();
                
                cmbUser.Items.Clear();
                cmbUser.Items.Add(new { Text = "جميع المستخدمين", Value = (int?)null });
                
                foreach (var user in users.Where(u => u.IsActive))
                {
                    cmbUser.Items.Add(new { Text = user.FullName, Value = (int?)user.Id });
                }

                cmbUser.DisplayMember = "Text";
                cmbUser.ValueMember = "Value";
                cmbUser.DropDownStyle = ComboBoxStyle.DropDownList;
                
                if (cmbUser.Items.Count > 0)
                    cmbUser.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnGenerateReport_Click(object? sender, EventArgs e)
        {
            if (cmbReportType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع التقرير", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnGenerateReport.Enabled = false;
                btnGenerateReport.Text = "جاري إنشاء التقرير...";
                
                // إعداد الفلتر
                _currentFilter.StartDate = dtpStartDate.Value.Date;
                _currentFilter.EndDate = dtpEndDate.Value.Date;
                
                dynamic selectedItem = cmbReportType.SelectedItem;
                _currentFilter.ReportType = selectedItem.Value;

                if (cmbUser.Visible && cmbUser.SelectedItem != null)
                {
                    dynamic userItem = cmbUser.SelectedItem;
                    _currentFilter.UserId = userItem.Value;
                }

                await GenerateSelectedReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerateReport.Enabled = true;
                btnGenerateReport.Text = "إنشاء التقرير";
            }
        }

        private async Task GenerateSelectedReport()
        {
            switch (_currentFilter.ReportType)
            {
                case ReportType.DailySales:
                    await GenerateDailySalesReport();
                    break;
                case ReportType.PeriodSales:
                    await GeneratePeriodSalesReport();
                    break;
                case ReportType.InventoryStatus:
                    await GenerateInventoryStatusReport();
                    break;
                case ReportType.UserPerformance:
                    await GenerateUserPerformanceReport();
                    break;
                case ReportType.FinancialSummary:
                    await GenerateFinancialSummaryReport();
                    break;
                case ReportType.TopProducts:
                    await GenerateTopProductsReport();
                    break;
                case ReportType.LowStockAlert:
                    await GenerateLowStockReport();
                    break;
            }
        }

        private async Task GenerateDailySalesReport()
        {
            var report = await ReportsService.GetDailySalesReportAsync(_currentFilter.StartDate);
            DisplayDailySalesReport(report);
        }

        private async Task GeneratePeriodSalesReport()
        {
            var report = await ReportsService.GetPeriodSalesReportAsync(
                _currentFilter.StartDate, _currentFilter.EndDate, _currentFilter.UserId);
            DisplayPeriodSalesReport(report);
        }

        private async Task GenerateInventoryStatusReport()
        {
            var report = await ReportsService.GetInventoryStatusReportAsync();
            DisplayInventoryStatusReport(report);
        }

        private async Task GenerateUserPerformanceReport()
        {
            var report = await ReportsService.GetUserPerformanceReportAsync(
                _currentFilter.StartDate, _currentFilter.EndDate);
            DisplayUserPerformanceReport(report);
        }

        private async Task GenerateFinancialSummaryReport()
        {
            var report = await ReportsService.GetFinancialSummaryAsync(
                _currentFilter.StartDate, _currentFilter.EndDate);
            DisplayFinancialSummaryReport(report);
        }

        private async Task GenerateTopProductsReport()
        {
            var products = await ReportsService.GetTopSellingProductsAsync(
                _currentFilter.StartDate, _currentFilter.EndDate, 20);
            DisplayTopProductsReport(products);
        }

        private async Task GenerateLowStockReport()
        {
            var products = await ReportsService.GetLowStockProductsAsync();
            DisplayLowStockReport(products);
        }

        private void DisplayDailySalesReport(DailySalesReport report)
        {
            // مسح البيانات السابقة
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            // عرض الملخص
            rtbSummary.AppendText($"تقرير المبيعات اليومية - {report.Date:yyyy-MM-dd}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");
            rtbSummary.AppendText($"عدد المعاملات: {report.TotalTransactions}\n");
            rtbSummary.AppendText($"إجمالي المبيعات: {report.TotalSales:C}\n");
            rtbSummary.AppendText($"إجمالي الخصومات: {report.TotalDiscounts:C}\n");
            rtbSummary.AppendText($"إجمالي الضرائب: {report.TotalTax:C}\n");
            rtbSummary.AppendText($"صافي المبيعات: {report.NetSales:C}\n\n");

            // عرض تفاصيل المنتجات
            if (report.SalesData.Any())
            {
                dgvReport.DataSource = report.SalesData;
                SetupDataGridView("تفاصيل المبيعات حسب المنتج");
            }
        }

        private void DisplayPeriodSalesReport(PeriodSalesReport report)
        {
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            // عرض الملخص
            rtbSummary.AppendText($"تقرير المبيعات للفترة من {report.StartDate:yyyy-MM-dd} إلى {report.EndDate:yyyy-MM-dd}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");
            rtbSummary.AppendText($"إجمالي المبيعات: {report.TotalSales:C}\n");
            rtbSummary.AppendText($"عدد المعاملات: {report.TotalTransactions}\n");
            rtbSummary.AppendText($"متوسط المبيعات اليومية: {report.AverageDailySales:C}\n");
            rtbSummary.AppendText($"إجمالي الخصومات: {report.TotalDiscounts:C}\n\n");

            // عرض أفضل المنتجات
            if (report.TopProducts.Any())
            {
                dgvReport.DataSource = report.TopProducts;
                SetupDataGridView("أفضل المنتجات مبيعاً");
            }
        }

        private void DisplayInventoryStatusReport(InventoryStatusReport report)
        {
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            // عرض الملخص
            rtbSummary.AppendText($"تقرير حالة المخزون - {report.GeneratedAt:yyyy-MM-dd HH:mm}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");
            rtbSummary.AppendText($"إجمالي المنتجات: {report.TotalProducts}\n");
            rtbSummary.AppendText($"متوفر: {report.InStockCount}\n");
            rtbSummary.AppendText($"مخزون منخفض: {report.LowStockCount}\n");
            rtbSummary.AppendText($"نفد المخزون: {report.OutOfStockCount}\n");
            rtbSummary.AppendText($"قيمة المخزون الإجمالية: {report.TotalStockValue:C}\n\n");

            // عرض تفاصيل المنتجات
            if (report.Products.Any())
            {
                dgvReport.DataSource = report.Products;
                SetupDataGridView("تفاصيل المخزون");
            }
        }

        private void DisplayUserPerformanceReport(UserPerformanceReport report)
        {
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            // عرض الملخص
            rtbSummary.AppendText($"تقرير أداء المستخدمين للفترة من {report.StartDate:yyyy-MM-dd} إلى {report.EndDate:yyyy-MM-dd}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");

            var totalSales = report.UserDetails.Sum(u => u.TotalSales);
            var totalTransactions = report.UserDetails.Sum(u => u.TotalTransactions);
            
            rtbSummary.AppendText($"إجمالي المبيعات: {totalSales:C}\n");
            rtbSummary.AppendText($"إجمالي المعاملات: {totalTransactions}\n");
            rtbSummary.AppendText($"عدد المستخدمين النشطين: {report.UserDetails.Count(u => u.TotalTransactions > 0)}\n\n");

            // عرض تفاصيل الأداء
            if (report.UserDetails.Any())
            {
                dgvReport.DataSource = report.UserDetails;
                SetupDataGridView("أداء المستخدمين");
            }
        }

        private void DisplayFinancialSummaryReport(FinancialSummary report)
        {
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            // عرض الملخص المالي
            rtbSummary.AppendText($"الملخص المالي للفترة من {report.StartDate:yyyy-MM-dd} إلى {report.EndDate:yyyy-MM-dd}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");
            rtbSummary.AppendText($"إجمالي الإيرادات: {report.TotalRevenue:C}\n");
            rtbSummary.AppendText($"إجمالي المصروفات: {report.TotalExpenses:C}\n");
            rtbSummary.AppendText($"صافي الربح: {report.NetProfit:C}\n");
            rtbSummary.AppendText($"متوسط قيمة المعاملة: {report.AverageTransactionValue:C}\n\n");

            // عرض المصروفات حسب الفئة
            if (report.ExpensesByCategory.Any())
            {
                dgvReport.DataSource = report.ExpensesByCategory;
                SetupDataGridView("المصروفات حسب الفئة");
            }
        }

        private void DisplayTopProductsReport(List<ProductSalesSummary> products)
        {
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            rtbSummary.AppendText($"أفضل المنتجات مبيعاً للفترة من {_currentFilter.StartDate:yyyy-MM-dd} إلى {_currentFilter.EndDate:yyyy-MM-dd}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");
            rtbSummary.AppendText($"عدد المنتجات: {products.Count}\n");
            rtbSummary.AppendText($"إجمالي المبيعات: {products.Sum(p => p.TotalAmount):C}\n");
            rtbSummary.AppendText($"إجمالي الكمية المباعة: {products.Sum(p => p.TotalQuantity)}\n\n");

            if (products.Any())
            {
                dgvReport.DataSource = products;
                SetupDataGridView("أفضل المنتجات مبيعاً");
            }
        }

        private void DisplayLowStockReport(List<ProductInventoryStatus> products)
        {
            dgvReport.DataSource = null;
            rtbSummary.Clear();

            rtbSummary.AppendText($"تقرير المنتجات منخفضة المخزون - {DateTime.Now:yyyy-MM-dd HH:mm}\n");
            rtbSummary.AppendText($"═══════════════════════════════════════\n\n");
            rtbSummary.AppendText($"عدد المنتجات منخفضة المخزون: {products.Count}\n");
            rtbSummary.AppendText($"منتجات نفد مخزونها: {products.Count(p => p.IsOutOfStock)}\n");
            rtbSummary.AppendText($"منتجات مخزونها منخفض: {products.Count(p => p.IsLowStock)}\n\n");

            if (products.Any())
            {
                dgvReport.DataSource = products;
                SetupDataGridView("المنتجات منخفضة المخزون");
            }
        }

        private void SetupDataGridView(string title)
        {
            dgvReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvReport.MultiSelect = false;
            dgvReport.ReadOnly = true;
            dgvReport.AllowUserToAddRows = false;
            dgvReport.AllowUserToDeleteRows = false;
            
            // تحسين مظهر الجدول
            dgvReport.BackgroundColor = Color.White;
            dgvReport.GridColor = Color.LightGray;
            dgvReport.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
            dgvReport.DefaultCellStyle.SelectionForeColor = Color.Black;
            
            lblReportTitle.Text = title;
        }

        private void BtnExportReport_Click(object? sender, EventArgs e)
        {
            if (dgvReport.DataSource == null)
            {
                MessageBox.Show("لا يوجد تقرير لتصديره", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // TODO: تنفيذ تصدير التقرير
            MessageBox.Show("سيتم تنفيذ ميزة التصدير في المرحلة القادمة", "معلومات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
