namespace ShopManagementSystem.Forms
{
    partial class ProductAddEditForm
    {
        private System.ComponentModel.IContainer components = null;
        private Panel pnlMain;
        private Label lblName;
        private TextBox txtName;
        private Label lblCode;
        private TextBox txtCode;
        private Label lblBarcode;
        private TextBox txtBarcode;
        private Label lblPrice;
        private NumericUpDown nudPrice;
        private Label lblQuantity;
        private NumericUpDown nudQuantity;
        private Label lblCategory;
        private ComboBox cmbCategory;
        private Label lblDescription;
        private TextBox txtDescription;
        private Button btnSave;
        private Button btnCancel;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.pnlMain = new Panel();
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblCode = new Label();
            this.txtCode = new TextBox();
            this.lblBarcode = new Label();
            this.txtBarcode = new TextBox();
            this.lblPrice = new Label();
            this.nudPrice = new NumericUpDown();
            this.lblQuantity = new Label();
            this.nudQuantity = new NumericUpDown();
            this.lblCategory = new Label();
            this.cmbCategory = new ComboBox();
            this.lblDescription = new Label();
            this.txtDescription = new TextBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudQuantity)).BeginInit();
            this.SuspendLayout();

            // 
            // ProductAddEditForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 600);
            this.Controls.Add(this.pnlMain);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ProductAddEditForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إضافة منتج جديد";
            this.KeyPreview = true;
            this.KeyDown += new KeyEventHandler(this.ProductAddEditForm_KeyDown);

            // 
            // pnlMain
            // 
            this.pnlMain.BackColor = Color.White;
            this.pnlMain.Controls.Add(this.lblName);
            this.pnlMain.Controls.Add(this.txtName);
            this.pnlMain.Controls.Add(this.lblCode);
            this.pnlMain.Controls.Add(this.txtCode);
            this.pnlMain.Controls.Add(this.lblBarcode);
            this.pnlMain.Controls.Add(this.txtBarcode);
            this.pnlMain.Controls.Add(this.lblPrice);
            this.pnlMain.Controls.Add(this.nudPrice);
            this.pnlMain.Controls.Add(this.lblQuantity);
            this.pnlMain.Controls.Add(this.nudQuantity);
            this.pnlMain.Controls.Add(this.lblCategory);
            this.pnlMain.Controls.Add(this.cmbCategory);
            this.pnlMain.Controls.Add(this.lblDescription);
            this.pnlMain.Controls.Add(this.txtDescription);
            this.pnlMain.Controls.Add(this.btnSave);
            this.pnlMain.Controls.Add(this.btnCancel);
            this.pnlMain.Dock = DockStyle.Fill;
            this.pnlMain.Location = new Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new Padding(30);
            this.pnlMain.Size = new Size(500, 600);
            this.pnlMain.TabIndex = 0;

            // 
            // lblName
            // 
            this.lblName.AutoSize = true;
            this.lblName.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblName.Location = new Point(40, 40);
            this.lblName.Name = "lblName";
            this.lblName.Size = new Size(79, 19);
            this.lblName.TabIndex = 0;
            this.lblName.Text = "اسم المنتج *";

            // 
            // txtName
            // 
            this.txtName.Font = new Font("Segoe UI", 11F);
            this.txtName.Location = new Point(40, 65);
            this.txtName.Name = "txtName";
            this.txtName.Size = new Size(420, 27);
            this.txtName.TabIndex = 1;
            this.txtName.KeyDown += new KeyEventHandler(this.txtName_KeyDown);

            // 
            // lblCode
            // 
            this.lblCode.AutoSize = true;
            this.lblCode.Font = new Font("Segoe UI", 10F);
            this.lblCode.Location = new Point(40, 110);
            this.lblCode.Name = "lblCode";
            this.lblCode.Size = new Size(73, 19);
            this.lblCode.TabIndex = 2;
            this.lblCode.Text = "كود المنتج";

            // 
            // txtCode
            // 
            this.txtCode.Font = new Font("Segoe UI", 11F);
            this.txtCode.Location = new Point(40, 135);
            this.txtCode.Name = "txtCode";
            this.txtCode.Size = new Size(200, 27);
            this.txtCode.TabIndex = 3;
            this.txtCode.KeyDown += new KeyEventHandler(this.txtCode_KeyDown);

            // 
            // lblBarcode
            // 
            this.lblBarcode.AutoSize = true;
            this.lblBarcode.Font = new Font("Segoe UI", 10F);
            this.lblBarcode.Location = new Point(260, 110);
            this.lblBarcode.Name = "lblBarcode";
            this.lblBarcode.Size = new Size(58, 19);
            this.lblBarcode.TabIndex = 4;
            this.lblBarcode.Text = "الباركود";

            // 
            // txtBarcode
            // 
            this.txtBarcode.Font = new Font("Segoe UI", 11F);
            this.txtBarcode.Location = new Point(260, 135);
            this.txtBarcode.Name = "txtBarcode";
            this.txtBarcode.Size = new Size(200, 27);
            this.txtBarcode.TabIndex = 5;
            this.txtBarcode.KeyDown += new KeyEventHandler(this.txtBarcode_KeyDown);

            // 
            // lblPrice
            // 
            this.lblPrice.AutoSize = true;
            this.lblPrice.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblPrice.Location = new Point(40, 180);
            this.lblPrice.Name = "lblPrice";
            this.lblPrice.Size = new Size(54, 19);
            this.lblPrice.TabIndex = 6;
            this.lblPrice.Text = "السعر *";

            // 
            // nudPrice
            // 
            this.nudPrice.DecimalPlaces = 2;
            this.nudPrice.Font = new Font("Segoe UI", 11F);
            this.nudPrice.Location = new Point(40, 205);
            this.nudPrice.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.nudPrice.Name = "nudPrice";
            this.nudPrice.Size = new Size(200, 27);
            this.nudPrice.TabIndex = 7;
            this.nudPrice.KeyDown += new KeyEventHandler(this.nudPrice_KeyDown);

            // 
            // lblQuantity
            // 
            this.lblQuantity.AutoSize = true;
            this.lblQuantity.Font = new Font("Segoe UI", 10F);
            this.lblQuantity.Location = new Point(260, 180);
            this.lblQuantity.Name = "lblQuantity";
            this.lblQuantity.Size = new Size(42, 19);
            this.lblQuantity.TabIndex = 8;
            this.lblQuantity.Text = "الكمية";

            // 
            // nudQuantity
            // 
            this.nudQuantity.Font = new Font("Segoe UI", 11F);
            this.nudQuantity.Location = new Point(260, 205);
            this.nudQuantity.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            this.nudQuantity.Name = "nudQuantity";
            this.nudQuantity.Size = new Size(200, 27);
            this.nudQuantity.TabIndex = 9;
            this.nudQuantity.KeyDown += new KeyEventHandler(this.nudQuantity_KeyDown);

            // 
            // lblCategory
            // 
            this.lblCategory.AutoSize = true;
            this.lblCategory.Font = new Font("Segoe UI", 10F);
            this.lblCategory.Location = new Point(40, 250);
            this.lblCategory.Name = "lblCategory";
            this.lblCategory.Size = new Size(55, 19);
            this.lblCategory.TabIndex = 10;
            this.lblCategory.Text = "التصنيف";

            // 
            // cmbCategory
            // 
            this.cmbCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCategory.Font = new Font("Segoe UI", 11F);
            this.cmbCategory.FormattingEnabled = true;
            this.cmbCategory.Location = new Point(40, 275);
            this.cmbCategory.Name = "cmbCategory";
            this.cmbCategory.Size = new Size(420, 28);
            this.cmbCategory.TabIndex = 11;
            this.cmbCategory.KeyDown += new KeyEventHandler(this.cmbCategory_KeyDown);

            // 
            // lblDescription
            // 
            this.lblDescription.AutoSize = true;
            this.lblDescription.Font = new Font("Segoe UI", 10F);
            this.lblDescription.Location = new Point(40, 320);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new Size(44, 19);
            this.lblDescription.TabIndex = 12;
            this.lblDescription.Text = "الوصف";

            // 
            // txtDescription
            // 
            this.txtDescription.Font = new Font("Segoe UI", 10F);
            this.txtDescription.Location = new Point(40, 345);
            this.txtDescription.Multiline = true;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.ScrollBars = ScrollBars.Vertical;
            this.txtDescription.Size = new Size(420, 100);
            this.txtDescription.TabIndex = 13;
            this.txtDescription.KeyDown += new KeyEventHandler(this.txtDescription_KeyDown);

            // 
            // btnSave
            // 
            this.btnSave.BackColor = Color.FromArgb(46, 204, 113);
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.Location = new Point(250, 470);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(210, 45);
            this.btnSave.TabIndex = 14;
            this.btnSave.Text = "إضافة المنتج";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(40, 470);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(190, 45);
            this.btnCancel.TabIndex = 15;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.pnlMain.ResumeLayout(false);
            this.pnlMain.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nudQuantity)).EndInit();
            this.ResumeLayout(false);
        }
    }
}
