using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class SupplierManagementForm : Form
    {
        private readonly SupplierService _supplierService;
        private readonly User _currentUser;
        private DataGridView dgvSuppliers;
        private TextBox txtName, txtCode, txtContactPerson, txtPhone, txtEmail, txtAddress;
        private TextBox txtCity, txtPostalCode, txtCountry, txtTaxNumber, txtNotes;
        private NumericUpDown nudPaymentTerms, nudCreditLimit;
        private CheckBox chkIsActive;
        private Button btnAdd, btnEdit, btnDelete, btnSave, btnCancel, btnSearch, btnPerformanceReport;
        private TextBox txtSearch;
        private Panel pnlForm, pnlGrid;
        private Supplier? _currentSupplier;
        private bool _isEditing = false;

        public SupplierManagementForm(User currentUser)
        {
            _currentUser = currentUser;
            _supplierService = SupplierService.Instance;
            InitializeComponent();
            SetupForm();
            _ = LoadSuppliersAsync();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النافذة الأساسية
            this.Text = "إدارة الموردين";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // تقسيم النافذة إلى قسمين
            var splitter = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 300
            };

            // القسم العلوي - نموذج إدخال البيانات
            pnlForm = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = Color.LightGray
            };
            SetupFormPanel();
            splitter.Panel1.Controls.Add(pnlForm);

            // القسم السفلي - جدول الموردين
            pnlGrid = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };
            SetupGridPanel();
            splitter.Panel2.Controls.Add(pnlGrid);

            this.Controls.Add(splitter);
        }

        private void SetupFormPanel()
        {
            var lblTitle = new Label
            {
                Text = "بيانات المورد",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            // الصف الأول
            var lblName = new Label { Text = "اسم المورد:", Location = new Point(10, 50), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtName = new TextBox { Location = new Point(100, 50), Size = new Size(150, 23) };

            var lblCode = new Label { Text = "الكود:", Location = new Point(270, 50), Size = new Size(50, 23), TextAlign = ContentAlignment.MiddleRight };
            txtCode = new TextBox { Location = new Point(330, 50), Size = new Size(100, 23) };

            var lblContactPerson = new Label { Text = "الشخص المسؤول:", Location = new Point(450, 50), Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            txtContactPerson = new TextBox { Location = new Point(560, 50), Size = new Size(150, 23) };

            // الصف الثاني
            var lblPhone = new Label { Text = "الهاتف:", Location = new Point(10, 80), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtPhone = new TextBox { Location = new Point(100, 80), Size = new Size(150, 23) };

            var lblEmail = new Label { Text = "البريد الإلكتروني:", Location = new Point(270, 80), Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            txtEmail = new TextBox { Location = new Point(380, 80), Size = new Size(200, 23) };

            // الصف الثالث
            var lblAddress = new Label { Text = "العنوان:", Location = new Point(10, 110), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtAddress = new TextBox { Location = new Point(100, 110), Size = new Size(200, 23) };

            var lblCity = new Label { Text = "المدينة:", Location = new Point(320, 110), Size = new Size(50, 23), TextAlign = ContentAlignment.MiddleRight };
            txtCity = new TextBox { Location = new Point(380, 110), Size = new Size(100, 23) };

            var lblPostalCode = new Label { Text = "الرمز البريدي:", Location = new Point(500, 110), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtPostalCode = new TextBox { Location = new Point(590, 110), Size = new Size(80, 23) };

            // الصف الرابع
            var lblCountry = new Label { Text = "البلد:", Location = new Point(10, 140), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtCountry = new TextBox { Location = new Point(100, 140), Size = new Size(100, 23) };

            var lblTaxNumber = new Label { Text = "الرقم الضريبي:", Location = new Point(220, 140), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtTaxNumber = new TextBox { Location = new Point(310, 140), Size = new Size(120, 23) };

            var lblPaymentTerms = new Label { Text = "مدة الدفع (أيام):", Location = new Point(450, 140), Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            nudPaymentTerms = new NumericUpDown { Location = new Point(560, 140), Size = new Size(80, 23), Maximum = 365, Value = 30 };

            // الصف الخامس
            var lblCreditLimit = new Label { Text = "حد الائتمان:", Location = new Point(10, 170), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            nudCreditLimit = new NumericUpDown { Location = new Point(100, 170), Size = new Size(120, 23), Maximum = 999999, DecimalPlaces = 2 };

            chkIsActive = new CheckBox { Text = "نشط", Location = new Point(240, 170), Size = new Size(60, 23), Checked = true };

            // ملاحظات
            var lblNotes = new Label { Text = "ملاحظات:", Location = new Point(10, 200), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtNotes = new TextBox { Location = new Point(100, 200), Size = new Size(400, 50), Multiline = true };

            // أزرار التحكم
            btnAdd = new Button { Text = "إضافة", Location = new Point(520, 200), Size = new Size(80, 30), BackColor = Color.LightGreen };
            btnEdit = new Button { Text = "تعديل", Location = new Point(610, 200), Size = new Size(80, 30), BackColor = Color.LightBlue };
            btnDelete = new Button { Text = "حذف", Location = new Point(700, 200), Size = new Size(80, 30), BackColor = Color.LightCoral };
            btnSave = new Button { Text = "حفظ", Location = new Point(520, 240), Size = new Size(80, 30), BackColor = Color.Green, Visible = false };
            btnCancel = new Button { Text = "إلغاء", Location = new Point(610, 240), Size = new Size(80, 30), BackColor = Color.Gray, Visible = false };

            // ربط الأحداث
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر للوحة
            pnlForm.Controls.AddRange(new Control[]
            {
                lblTitle, lblName, txtName, lblCode, txtCode, lblContactPerson, txtContactPerson,
                lblPhone, txtPhone, lblEmail, txtEmail, lblAddress, txtAddress, lblCity, txtCity,
                lblPostalCode, txtPostalCode, lblCountry, txtCountry, lblTaxNumber, txtTaxNumber,
                lblPaymentTerms, nudPaymentTerms, lblCreditLimit, nudCreditLimit, chkIsActive,
                lblNotes, txtNotes, btnAdd, btnEdit, btnDelete, btnSave, btnCancel
            });
        }

        private void SetupGridPanel()
        {
            var lblTitle = new Label
            {
                Text = "قائمة الموردين",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            // أدوات البحث
            var lblSearch = new Label { Text = "البحث:", Location = new Point(10, 45), Size = new Size(50, 23), TextAlign = ContentAlignment.MiddleRight };
            txtSearch = new TextBox { Location = new Point(70, 45), Size = new Size(200, 23), PlaceholderText = "البحث في الاسم، الكود، الهاتف..." };
            btnSearch = new Button { Text = "بحث", Location = new Point(280, 45), Size = new Size(60, 23), BackColor = Color.LightBlue };
            btnPerformanceReport = new Button { Text = "تقرير الأداء", Location = new Point(350, 45), Size = new Size(100, 23), BackColor = Color.Orange };

            btnSearch.Click += BtnSearch_Click;
            btnPerformanceReport.Click += BtnPerformanceReport_Click;

            // جدول الموردين
            dgvSuppliers = new DataGridView
            {
                Location = new Point(10, 80),
                Size = new Size(pnlGrid.Width - 40, pnlGrid.Height - 100),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupSuppliersGrid();
            dgvSuppliers.SelectionChanged += DgvSuppliers_SelectionChanged;

            pnlGrid.Controls.AddRange(new Control[] { lblTitle, lblSearch, txtSearch, btnSearch, btnPerformanceReport, dgvSuppliers });
        }

        private void SetupSuppliersGrid()
        {
            dgvSuppliers.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "اسم المورد", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Code", HeaderText = "الكود", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "ContactPerson", HeaderText = "الشخص المسؤول", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Phone", HeaderText = "الهاتف", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "البريد الإلكتروني", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "City", HeaderText = "المدينة", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "PaymentTermsDays", HeaderText = "مدة الدفع", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "CreditLimit", HeaderText = "حد الائتمان", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CurrentBalance", HeaderText = "الرصيد الحالي", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "PerformanceRating", HeaderText = "تقييم الأداء", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "OnTimeDeliveryRate", HeaderText = "معدل التسليم في الوقت", Width = 120 },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "نشط", Width = 60 }
            });
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetSuppliersAsync();
                dgvSuppliers.DataSource = suppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtCode.Clear();
            txtContactPerson.Clear();
            txtPhone.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            txtCity.Clear();
            txtPostalCode.Clear();
            txtCountry.Clear();
            txtTaxNumber.Clear();
            txtNotes.Clear();
            nudPaymentTerms.Value = 30;
            nudCreditLimit.Value = 0;
            chkIsActive.Checked = true;
            _currentSupplier = null;
        }

        private void LoadSupplierToForm(Supplier supplier)
        {
            txtName.Text = supplier.Name;
            txtCode.Text = supplier.Code ?? "";
            txtContactPerson.Text = supplier.ContactPerson ?? "";
            txtPhone.Text = supplier.Phone ?? "";
            txtEmail.Text = supplier.Email ?? "";
            txtAddress.Text = supplier.Address ?? "";
            txtCity.Text = supplier.City ?? "";
            txtPostalCode.Text = supplier.PostalCode ?? "";
            txtCountry.Text = supplier.Country ?? "";
            txtTaxNumber.Text = supplier.TaxNumber ?? "";
            txtNotes.Text = supplier.Notes ?? "";
            nudPaymentTerms.Value = supplier.PaymentTermsDays;
            nudCreditLimit.Value = supplier.CreditLimit;
            chkIsActive.Checked = supplier.IsActive;
            _currentSupplier = supplier;
        }

        private Supplier CreateSupplierFromForm()
        {
            return new Supplier
            {
                Id = _currentSupplier?.Id ?? 0,
                Name = txtName.Text.Trim(),
                Code = string.IsNullOrWhiteSpace(txtCode.Text) ? null : txtCode.Text.Trim(),
                ContactPerson = string.IsNullOrWhiteSpace(txtContactPerson.Text) ? null : txtContactPerson.Text.Trim(),
                Phone = string.IsNullOrWhiteSpace(txtPhone.Text) ? null : txtPhone.Text.Trim(),
                Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim(),
                Address = string.IsNullOrWhiteSpace(txtAddress.Text) ? null : txtAddress.Text.Trim(),
                City = string.IsNullOrWhiteSpace(txtCity.Text) ? null : txtCity.Text.Trim(),
                PostalCode = string.IsNullOrWhiteSpace(txtPostalCode.Text) ? null : txtPostalCode.Text.Trim(),
                Country = string.IsNullOrWhiteSpace(txtCountry.Text) ? null : txtCountry.Text.Trim(),
                TaxNumber = string.IsNullOrWhiteSpace(txtTaxNumber.Text) ? null : txtTaxNumber.Text.Trim(),
                Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim(),
                PaymentTermsDays = (int)nudPaymentTerms.Value,
                CreditLimit = nudCreditLimit.Value,
                IsActive = chkIsActive.Checked,
                CreatedAt = _currentSupplier?.CreatedAt ?? DateTime.Now
            };
        }

        private void SetEditMode(bool editing)
        {
            _isEditing = editing;
            btnAdd.Visible = !editing;
            btnEdit.Visible = !editing;
            btnDelete.Visible = !editing;
            btnSave.Visible = editing;
            btnCancel.Visible = editing;

            // تمكين/تعطيل الحقول
            txtName.ReadOnly = !editing;
            txtCode.ReadOnly = !editing;
            txtContactPerson.ReadOnly = !editing;
            txtPhone.ReadOnly = !editing;
            txtEmail.ReadOnly = !editing;
            txtAddress.ReadOnly = !editing;
            txtCity.ReadOnly = !editing;
            txtPostalCode.ReadOnly = !editing;
            txtCountry.ReadOnly = !editing;
            txtTaxNumber.ReadOnly = !editing;
            txtNotes.ReadOnly = !editing;
            nudPaymentTerms.ReadOnly = !editing;
            nudCreditLimit.ReadOnly = !editing;
            chkIsActive.Enabled = editing;
        }

        #region Event Handlers

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            ClearForm();
            SetEditMode(true);
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_currentSupplier != null)
            {
                SetEditMode(true);
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (_currentSupplier != null)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف المورد '{_currentSupplier.Name}'؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (await _supplierService.DeleteSupplierAsync(_currentSupplier.Id))
                    {
                        MessageBox.Show("تم حذف المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadSuppliersAsync();
                        ClearForm();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            var supplier = CreateSupplierFromForm();

            if (!_supplierService.ValidateSupplier(supplier, out string errorMessage))
            {
                MessageBox.Show(errorMessage, "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من تكرار الكود
            if (!string.IsNullOrWhiteSpace(supplier.Code))
            {
                if (await _supplierService.IsSupplierCodeExistsAsync(supplier.Code, supplier.Id))
                {
                    MessageBox.Show("كود المورد موجود بالفعل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
            }

            bool success;
            if (supplier.Id == 0)
            {
                success = await _supplierService.AddSupplierAsync(supplier);
            }
            else
            {
                success = await _supplierService.UpdateSupplierAsync(supplier);
            }

            if (success)
            {
                MessageBox.Show("تم حفظ بيانات المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                SetEditMode(false);
                await LoadSuppliersAsync();
            }
            else
            {
                MessageBox.Show("فشل في حفظ بيانات المورد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (_currentSupplier != null)
            {
                LoadSupplierToForm(_currentSupplier);
            }
            else
            {
                ClearForm();
            }
            SetEditMode(false);
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                await LoadSuppliersAsync();
            }
            else
            {
                try
                {
                    var suppliers = await _supplierService.SearchSuppliersAsync(txtSearch.Text);
                    dgvSuppliers.DataSource = suppliers;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnPerformanceReport_Click(object sender, EventArgs e)
        {
            if (_currentSupplier != null)
            {
                // سيتم تطوير نافذة تقرير الأداء لاحقاً
                MessageBox.Show("تقرير أداء المورد قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد لعرض تقرير الأداء", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void DgvSuppliers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvSuppliers.SelectedRows.Count > 0 && !_isEditing)
            {
                var selectedRow = dgvSuppliers.SelectedRows[0];
                var supplierId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                
                // البحث عن المورد في البيانات المحملة
                var suppliers = (List<Supplier>)dgvSuppliers.DataSource;
                var supplier = suppliers.FirstOrDefault(s => s.Id == supplierId);
                
                if (supplier != null)
                {
                    LoadSupplierToForm(supplier);
                }
            }
        }

        #endregion
    }
}
