namespace POSManager.Models
{
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public int? CategoryId { get; set; }
        public string? CategoryName { get; set; } // للعرض فقط
        public decimal PurchasePrice { get; set; }
        public decimal SalePrice { get; set; }
        public int Stock { get; set; }
        public int MinStock { get; set; } = 5;
        public string Unit { get; set; } = "قطعة";
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }

        // Calculated properties
        public decimal ProfitMargin => SalePrice > 0 && PurchasePrice > 0 ? ((SalePrice - PurchasePrice) / SalePrice) * 100 : 0;
        public bool IsLowStock => Stock <= MinStock;
        public bool IsOutOfStock => Stock <= 0;
        public decimal Profit => SalePrice - PurchasePrice;
    }
    
    public class Category
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public int ProductCount { get; set; } // للعرض فقط
    }
    
    public class Customer
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
    }
}
