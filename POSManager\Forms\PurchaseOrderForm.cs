using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class PurchaseOrderForm : Form
    {
        private readonly PurchaseOrderService _purchaseOrderService;
        private readonly SupplierService _supplierService;
        private readonly User _currentUser;
        private TabControl tabControl;
        private DataGridView dgvOrders, dgvOrderItems;
        private ComboBox cmbSupplier, cmbStatus, cmbSupplierDetails;
        private DateTimePicker dtpFromDate, dtpToDate, dtpOrderDate, dtpExpectedDelivery;
        private TextBox txtOrderNumber, txtNotes, txtSearch;
        private NumericUpDown nudSubtotal, nudTaxAmount, nudDiscountAmount, nudShippingCost, nudTotal;
        private Button btnNew, btnEdit, btnSave, btnCancel, btnDelete, btnApprove, btnSend, btnReceive;
        private Button btnAddItem, btnEditItem, btnDeleteItem, btnSearch, btnRefresh;
        private PurchaseOrder? _currentOrder;
        private bool _isEditing = false;

        public PurchaseOrderForm(User currentUser)
        {
            _currentUser = currentUser;
            _purchaseOrderService = PurchaseOrderService.Instance;
            _supplierService = SupplierService.Instance;
            InitializeComponent();
            SetupForm();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة الأساسية
            this.Text = "إدارة أوامر الشراء";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F)
            };

            // تبويب قائمة أوامر الشراء
            var tabList = new TabPage("قائمة أوامر الشراء");
            SetupOrdersListTab(tabList);
            tabControl.TabPages.Add(tabList);

            // تبويب تفاصيل أمر الشراء
            var tabDetails = new TabPage("تفاصيل أمر الشراء");
            SetupOrderDetailsTab(tabDetails);
            tabControl.TabPages.Add(tabDetails);

            this.Controls.Add(tabControl);
        }

        private void SetupOrdersListTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // لوحة الفلاتر
            var filterPanel = new Panel { Height = 80, Dock = DockStyle.Top };

            var lblFromDate = new Label { Text = "من تاريخ:", Location = new Point(10, 15), Size = new Size(60, 23), TextAlign = ContentAlignment.MiddleRight };
            dtpFromDate = new DateTimePicker { Location = new Point(80, 15), Size = new Size(120, 23), Value = DateTime.Now.AddMonths(-1) };

            var lblToDate = new Label { Text = "إلى تاريخ:", Location = new Point(220, 15), Size = new Size(60, 23), TextAlign = ContentAlignment.MiddleRight };
            dtpToDate = new DateTimePicker { Location = new Point(290, 15), Size = new Size(120, 23), Value = DateTime.Now };

            var lblSupplier = new Label { Text = "المورد:", Location = new Point(430, 15), Size = new Size(50, 23), TextAlign = ContentAlignment.MiddleRight };
            cmbSupplier = new ComboBox { Location = new Point(490, 15), Size = new Size(150, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            var lblStatus = new Label { Text = "الحالة:", Location = new Point(660, 15), Size = new Size(50, 23), TextAlign = ContentAlignment.MiddleRight };
            cmbStatus = new ComboBox { Location = new Point(720, 15), Size = new Size(120, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            btnSearch = new Button { Text = "بحث", Location = new Point(860, 15), Size = new Size(60, 23), BackColor = Color.LightBlue };
            btnRefresh = new Button { Text = "تحديث", Location = new Point(930, 15), Size = new Size(60, 23), BackColor = Color.LightGreen };

            var lblSearch = new Label { Text = "البحث:", Location = new Point(10, 45), Size = new Size(50, 23), TextAlign = ContentAlignment.MiddleRight };
            txtSearch = new TextBox { Location = new Point(70, 45), Size = new Size(200, 23), PlaceholderText = "رقم الأمر أو اسم المورد..." };

            filterPanel.Controls.AddRange(new Control[]
            {
                lblFromDate, dtpFromDate, lblToDate, dtpToDate, lblSupplier, cmbSupplier,
                lblStatus, cmbStatus, btnSearch, btnRefresh, lblSearch, txtSearch
            });

            // لوحة الأزرار
            var buttonPanel = new Panel { Height = 50, Dock = DockStyle.Bottom };

            btnNew = new Button { Text = "جديد", Location = new Point(10, 10), Size = new Size(80, 30), BackColor = Color.LightGreen };
            btnEdit = new Button { Text = "تعديل", Location = new Point(100, 10), Size = new Size(80, 30), BackColor = Color.LightBlue };
            btnDelete = new Button { Text = "حذف", Location = new Point(190, 10), Size = new Size(80, 30), BackColor = Color.LightCoral };
            btnApprove = new Button { Text = "اعتماد", Location = new Point(280, 10), Size = new Size(80, 30), BackColor = Color.Orange };
            btnSend = new Button { Text = "إرسال", Location = new Point(370, 10), Size = new Size(80, 30), BackColor = Color.Yellow };
            btnReceive = new Button { Text = "استلام", Location = new Point(460, 10), Size = new Size(80, 30), BackColor = Color.LightPink };

            buttonPanel.Controls.AddRange(new Control[] { btnNew, btnEdit, btnDelete, btnApprove, btnSend, btnReceive });

            // جدول أوامر الشراء
            dgvOrders = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupOrdersGrid();

            mainPanel.Controls.Add(dgvOrders);
            mainPanel.Controls.Add(buttonPanel);
            mainPanel.Controls.Add(filterPanel);
            tab.Controls.Add(mainPanel);

            // ربط الأحداث
            btnSearch.Click += BtnSearch_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnNew.Click += BtnNew_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnApprove.Click += BtnApprove_Click;
            btnSend.Click += BtnSend_Click;
            btnReceive.Click += BtnReceive_Click;
            dgvOrders.SelectionChanged += DgvOrders_SelectionChanged;
        }

        private void SetupOrderDetailsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // معلومات أمر الشراء
            var infoPanel = new Panel { Height = 150, Dock = DockStyle.Top };

            var lblOrderNumber = new Label { Text = "رقم الأمر:", Location = new Point(10, 15), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtOrderNumber = new TextBox { Location = new Point(100, 15), Size = new Size(150, 23), ReadOnly = true };

            var lblOrderDate = new Label { Text = "تاريخ الأمر:", Location = new Point(270, 15), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            dtpOrderDate = new DateTimePicker { Location = new Point(360, 15), Size = new Size(120, 23) };

            var lblExpectedDelivery = new Label { Text = "التسليم المتوقع:", Location = new Point(500, 15), Size = new Size(100, 23), TextAlign = ContentAlignment.MiddleRight };
            dtpExpectedDelivery = new DateTimePicker { Location = new Point(610, 15), Size = new Size(120, 23) };

            var lblSupplierDetails = new Label { Text = "المورد:", Location = new Point(10, 45), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            cmbSupplierDetails = new ComboBox { Location = new Point(100, 45), Size = new Size(200, 23), DropDownStyle = ComboBoxStyle.DropDownList };

            var lblNotes = new Label { Text = "ملاحظات:", Location = new Point(10, 75), Size = new Size(80, 23), TextAlign = ContentAlignment.MiddleRight };
            txtNotes = new TextBox { Location = new Point(100, 75), Size = new Size(400, 50), Multiline = true };

            infoPanel.Controls.AddRange(new Control[]
            {
                lblOrderNumber, txtOrderNumber, lblOrderDate, dtpOrderDate,
                lblExpectedDelivery, dtpExpectedDelivery, lblSupplierDetails, cmbSupplierDetails, lblNotes, txtNotes
            });

            // أزرار التحكم
            var controlPanel = new Panel { Height = 50, Dock = DockStyle.Bottom };

            btnSave = new Button { Text = "حفظ", Location = new Point(10, 10), Size = new Size(80, 30), BackColor = Color.Green, Visible = false };
            btnCancel = new Button { Text = "إلغاء", Location = new Point(100, 10), Size = new Size(80, 30), BackColor = Color.Gray, Visible = false };
            btnAddItem = new Button { Text = "إضافة عنصر", Location = new Point(200, 10), Size = new Size(100, 30), BackColor = Color.LightGreen };
            btnEditItem = new Button { Text = "تعديل عنصر", Location = new Point(310, 10), Size = new Size(100, 30), BackColor = Color.LightBlue };
            btnDeleteItem = new Button { Text = "حذف عنصر", Location = new Point(420, 10), Size = new Size(100, 30), BackColor = Color.LightCoral };

            controlPanel.Controls.AddRange(new Control[] { btnSave, btnCancel, btnAddItem, btnEditItem, btnDeleteItem });

            // جدول عناصر أمر الشراء
            dgvOrderItems = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupOrderItemsGrid();

            mainPanel.Controls.Add(dgvOrderItems);
            mainPanel.Controls.Add(controlPanel);
            mainPanel.Controls.Add(infoPanel);
            tab.Controls.Add(mainPanel);

            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnAddItem.Click += BtnAddItem_Click;
            btnEditItem.Click += BtnEditItem_Click;
            btnDeleteItem.Click += BtnDeleteItem_Click;
        }

        private void SetupOrdersGrid()
        {
            dgvOrders.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "OrderNumber", HeaderText = "رقم الأمر", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "SupplierName", HeaderText = "المورد", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "OrderDate", HeaderText = "تاريخ الأمر", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ExpectedDeliveryDate", HeaderText = "التسليم المتوقع", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Status", HeaderText = "الحالة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Total", HeaderText = "المجموع", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CreatedByName", HeaderText = "أنشأ بواسطة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "تاريخ الإنشاء", Width = 120 }
            });
        }

        private void SetupOrderItemsGrid()
        {
            dgvOrderItems.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "ProductName", HeaderText = "المنتج", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "ProductCode", HeaderText = "الكود", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "الكمية المطلوبة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ReceivedQuantity", HeaderText = "الكمية المستلمة", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "UnitCost", HeaderText = "سعر الوحدة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "DiscountAmount", HeaderText = "الخصم", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "Total", HeaderText = "المجموع", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", Width = 150 }
            });
        }

        private async void LoadDataAsync()
        {
            await LoadSuppliersAsync();
            LoadStatusComboBox();
            await LoadOrdersAsync();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetSuppliersAsync();

                // تحميل موردين للفلتر
                cmbSupplier.Items.Clear();
                cmbSupplier.Items.Add(new { Id = 0, Name = "جميع الموردين" });

                foreach (var supplier in suppliers)
                {
                    cmbSupplier.Items.Add(new { Id = supplier.Id, Name = supplier.Name });
                }

                cmbSupplier.DisplayMember = "Name";
                cmbSupplier.ValueMember = "Id";
                cmbSupplier.SelectedIndex = 0;

                // تحميل موردين لتفاصيل أمر الشراء
                cmbSupplierDetails.Items.Clear();
                cmbSupplierDetails.Items.Add(new { Id = 0, Name = "اختر مورد..." });

                foreach (var supplier in suppliers)
                {
                    cmbSupplierDetails.Items.Add(new { Id = supplier.Id, Name = supplier.Name });
                }

                cmbSupplierDetails.DisplayMember = "Name";
                cmbSupplierDetails.ValueMember = "Id";
                cmbSupplierDetails.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadStatusComboBox()
        {
            var statuses = new[]
            {
                new { Value = -1, Text = "جميع الحالات" },
                new { Value = (int)PurchaseOrderStatus.Draft, Text = "مسودة" },
                new { Value = (int)PurchaseOrderStatus.Approved, Text = "معتمد" },
                new { Value = (int)PurchaseOrderStatus.Sent, Text = "مرسل" },
                new { Value = (int)PurchaseOrderStatus.PartiallyReceived, Text = "مستلم جزئياً" },
                new { Value = (int)PurchaseOrderStatus.Received, Text = "مستلم" },
                new { Value = (int)PurchaseOrderStatus.Cancelled, Text = "ملغي" }
            };

            cmbStatus.DataSource = statuses;
            cmbStatus.DisplayMember = "Text";
            cmbStatus.ValueMember = "Value";
            cmbStatus.SelectedIndex = 0;
        }

        private async Task LoadOrdersAsync()
        {
            try
            {
                PurchaseOrderStatus? status = null;
                if (cmbStatus.SelectedValue != null && (int)cmbStatus.SelectedValue != -1)
                {
                    status = (PurchaseOrderStatus)(int)cmbStatus.SelectedValue;
                }

                int? supplierId = null;
                if (cmbSupplier.SelectedValue != null && (int)cmbSupplier.SelectedValue != 0)
                {
                    supplierId = (int)cmbSupplier.SelectedValue;
                }

                var orders = await _purchaseOrderService.GetPurchaseOrdersAsync(status, supplierId, dtpFromDate.Value, dtpToDate.Value);

                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    orders = orders.Where(o =>
                        o.OrderNumber.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                        o.SupplierName.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                dgvOrders.DataSource = orders;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أوامر الشراء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Event Handlers

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            await LoadOrdersAsync();
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadOrdersAsync();
        }

        private void BtnNew_Click(object sender, EventArgs e)
        {
            tabControl.SelectedIndex = 1; // الانتقال لتبويب التفاصيل
            ClearOrderForm();
            SetEditMode(true);
        }

        private async void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_currentOrder != null && _currentOrder.Status == PurchaseOrderStatus.Draft)
            {
                tabControl.SelectedIndex = 1;
                await LoadOrderDetailsAsync(_currentOrder.Id);
                SetEditMode(true);
            }
            else
            {
                MessageBox.Show("يمكن تعديل المسودات فقط", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (_currentOrder != null && _currentOrder.Status == PurchaseOrderStatus.Draft)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف أمر الشراء '{_currentOrder.OrderNumber}'؟",
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // سيتم تطوير حذف أمر الشراء لاحقاً
                    MessageBox.Show("حذف أمر الشراء قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("يمكن حذف المسودات فقط", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnApprove_Click(object sender, EventArgs e)
        {
            if (_currentOrder != null && _currentOrder.Status == PurchaseOrderStatus.Draft)
            {
                if (await _purchaseOrderService.ApprovePurchaseOrderAsync(_currentOrder.Id, _currentUser.Id))
                {
                    MessageBox.Show("تم اعتماد أمر الشراء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadOrdersAsync();
                }
                else
                {
                    MessageBox.Show("فشل في اعتماد أمر الشراء", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("يمكن اعتماد المسودات فقط", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnSend_Click(object sender, EventArgs e)
        {
            if (_currentOrder != null && _currentOrder.Status == PurchaseOrderStatus.Approved)
            {
                if (await _purchaseOrderService.SendPurchaseOrderAsync(_currentOrder.Id))
                {
                    MessageBox.Show("تم إرسال أمر الشراء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadOrdersAsync();
                }
                else
                {
                    MessageBox.Show("فشل في إرسال أمر الشراء", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("يمكن إرسال الأوامر المعتمدة فقط", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnReceive_Click(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0)
            {
                var selectedOrder = (PurchaseOrder)dgvOrders.SelectedRows[0].DataBoundItem;

                if (selectedOrder.Status != PurchaseOrderStatus.Sent && selectedOrder.Status != PurchaseOrderStatus.PartiallyReceived)
                {
                    MessageBox.Show("يمكن استلام الأوامر المرسلة أو المستلمة جزئياً فقط", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تحميل تفاصيل أمر الشراء مع العناصر
                var orderWithItems = await _purchaseOrderService.GetPurchaseOrderByIdAsync(selectedOrder.Id);
                if (orderWithItems != null)
                {
                    var receiptForm = new PurchaseOrderReceiptForm(orderWithItems, _currentUser);
                    if (receiptForm.ShowDialog() == DialogResult.OK)
                    {
                        await LoadOrdersAsync(); // إعادة تحميل القائمة
                        MessageBox.Show("تم استلام أمر الشراء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            else
            {
                MessageBox.Show("يجب اختيار أمر شراء للاستلام", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            if (await SavePurchaseOrderAsync())
            {
                MessageBox.Show("تم حفظ أمر الشراء بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                SetEditMode(false);
                await LoadOrdersAsync();
                tabControl.SelectedIndex = 0; // العودة لقائمة الأوامر
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            SetEditMode(false);
            if (_currentOrder != null)
            {
                LoadOrderToForm(_currentOrder);
            }
            else
            {
                ClearOrderForm();
            }
        }

        private void BtnAddItem_Click(object sender, EventArgs e)
        {
            var itemForm = new PurchaseOrderItemForm();
            if (itemForm.ShowDialog() == DialogResult.OK && itemForm.OrderItem != null)
            {
                if (_currentOrder == null)
                {
                    _currentOrder = new PurchaseOrder
                    {
                        Items = new List<PurchaseOrderItem>()
                    };
                }

                _currentOrder.Items.Add(itemForm.OrderItem);
                RefreshOrderItemsGrid();
                CalculateOrderTotals();
            }
        }

        private void BtnEditItem_Click(object sender, EventArgs e)
        {
            if (dgvOrderItems.SelectedRows.Count > 0)
            {
                var selectedIndex = dgvOrderItems.SelectedRows[0].Index;
                var selectedItem = _currentOrder?.Items[selectedIndex];

                if (selectedItem != null)
                {
                    var itemForm = new PurchaseOrderItemForm(selectedItem);
                    if (itemForm.ShowDialog() == DialogResult.OK && itemForm.OrderItem != null)
                    {
                        _currentOrder.Items[selectedIndex] = itemForm.OrderItem;
                        RefreshOrderItemsGrid();
                        CalculateOrderTotals();
                    }
                }
            }
            else
            {
                MessageBox.Show("يجب اختيار عنصر للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDeleteItem_Click(object sender, EventArgs e)
        {
            if (dgvOrderItems.SelectedRows.Count > 0)
            {
                var selectedIndex = dgvOrderItems.SelectedRows[0].Index;
                var selectedItem = _currentOrder?.Items[selectedIndex];

                if (selectedItem != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف العنصر '{selectedItem.ProductName}'؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        _currentOrder.Items.RemoveAt(selectedIndex);
                        RefreshOrderItemsGrid();
                        CalculateOrderTotals();
                    }
                }
            }
            else
            {
                MessageBox.Show("يجب اختيار عنصر للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void DgvOrders_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvOrders.SelectedRows.Count > 0 && !_isEditing)
            {
                var selectedRow = dgvOrders.SelectedRows[0];
                var orderId = Convert.ToInt32(selectedRow.Cells["Id"].Value);

                var orders = (List<PurchaseOrder>)dgvOrders.DataSource;
                _currentOrder = orders.FirstOrDefault(o => o.Id == orderId);
            }
        }

        #endregion

        #region Helper Methods

        private void ClearOrderForm()
        {
            txtOrderNumber.Clear();
            dtpOrderDate.Value = DateTime.Now;
            dtpExpectedDelivery.Value = DateTime.Now.AddDays(7);
            txtNotes.Clear();
            cmbSupplierDetails.SelectedIndex = 0;
            dgvOrderItems.DataSource = null;
            _currentOrder = null;
        }

        private void LoadOrderToForm(PurchaseOrder order)
        {
            txtOrderNumber.Text = order.OrderNumber;
            dtpOrderDate.Value = order.OrderDate;
            dtpExpectedDelivery.Value = order.ExpectedDeliveryDate ?? DateTime.Now.AddDays(7);
            txtNotes.Text = order.Notes ?? "";

            // تحديد المورد
            for (int i = 0; i < cmbSupplierDetails.Items.Count; i++)
            {
                dynamic supplier = cmbSupplierDetails.Items[i];
                if (supplier.Id == order.SupplierId)
                {
                    cmbSupplierDetails.SelectedIndex = i;
                    break;
                }
            }

            dgvOrderItems.DataSource = order.Items;
        }

        private async Task LoadOrderDetailsAsync(int orderId)
        {
            try
            {
                var order = await _purchaseOrderService.GetPurchaseOrderByIdAsync(orderId);
                if (order != null)
                {
                    _currentOrder = order;
                    LoadOrderToForm(order);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل أمر الشراء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetEditMode(bool editing)
        {
            _isEditing = editing;
            btnSave.Visible = editing;
            btnCancel.Visible = editing;

            dtpOrderDate.Enabled = editing;
            dtpExpectedDelivery.Enabled = editing;
            cmbSupplierDetails.Enabled = editing;
            txtNotes.ReadOnly = !editing;
            btnAddItem.Enabled = editing;
            btnEditItem.Enabled = editing;
            btnDeleteItem.Enabled = editing;
        }

        private void RefreshOrderItemsGrid()
        {
            if (_currentOrder?.Items != null)
            {
                dgvOrderItems.DataSource = null;
                dgvOrderItems.DataSource = _currentOrder.Items;
            }
        }

        private void CalculateOrderTotals()
        {
            if (_currentOrder?.Items != null)
            {
                _currentOrder.Subtotal = _currentOrder.Items.Sum(i => i.Total);
                _currentOrder.TaxAmount = 0; // يمكن حساب الضريبة هنا
                _currentOrder.Total = _currentOrder.Subtotal + _currentOrder.TaxAmount + _currentOrder.ShippingCost - _currentOrder.DiscountAmount;
            }
        }

        private async Task<bool> SavePurchaseOrderAsync()
        {
            try
            {
                if (!ValidateOrderForm())
                    return false;

                if (_currentOrder == null)
                {
                    _currentOrder = new PurchaseOrder
                    {
                        Items = new List<PurchaseOrderItem>()
                    };
                }

                // تحديث بيانات أمر الشراء من النموذج
                if (string.IsNullOrEmpty(_currentOrder.OrderNumber))
                {
                    _currentOrder.OrderNumber = await _purchaseOrderService.GenerateOrderNumberAsync();
                }

                _currentOrder.OrderDate = dtpOrderDate.Value;
                _currentOrder.ExpectedDeliveryDate = dtpExpectedDelivery.Value;
                _currentOrder.Notes = txtNotes.Text;
                _currentOrder.CreatedBy = _currentUser.Id;
                _currentOrder.Status = PurchaseOrderStatus.Draft;

                // تحديد المورد
                if (cmbSupplierDetails.SelectedItem != null)
                {
                    dynamic selectedSupplier = cmbSupplierDetails.SelectedItem;
                    _currentOrder.SupplierId = selectedSupplier.Id;
                }

                // حساب المجاميع
                CalculateOrderTotals();

                // حفظ أمر الشراء
                int orderId = 0;
                if (_currentOrder.Id == 0)
                {
                    orderId = await _purchaseOrderService.CreatePurchaseOrderAsync(_currentOrder);
                    _currentOrder.Id = orderId;
                }
                else
                {
                    await _purchaseOrderService.UpdatePurchaseOrderAsync(_currentOrder);
                    orderId = _currentOrder.Id;
                }

                return orderId > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ أمر الشراء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private bool ValidateOrderForm()
        {
            if (cmbSupplierDetails.SelectedItem == null)
            {
                MessageBox.Show("يجب اختيار مورد", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedIndex = 1; // الانتقال لتبويب التفاصيل
                cmbSupplierDetails.Focus();
                return false;
            }

            dynamic selectedSupplier = cmbSupplierDetails.SelectedItem;
            if (selectedSupplier.Id == 0)
            {
                MessageBox.Show("يجب اختيار مورد صحيح", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedIndex = 1;
                cmbSupplierDetails.Focus();
                return false;
            }

            if (_currentOrder?.Items == null || _currentOrder.Items.Count == 0)
            {
                MessageBox.Show("يجب إضافة عنصر واحد على الأقل", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        #endregion
    }
}