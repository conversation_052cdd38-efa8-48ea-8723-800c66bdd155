using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class PrintPreviewForm : Form
    {
        private readonly Models.PrintDocument _document;
        private readonly PrintJobSettings _settings;
        private PrintPreviewControl _previewControl;
        private ToolStrip _toolStrip;
        private StatusStrip _statusStrip;
        private System.Drawing.Printing.PrintDocument _printDocument;

        public PrintPreviewForm(Models.PrintDocument document, PrintJobSettings? settings = null)
        {
            _document = document;
            _settings = settings ?? new PrintJobSettings();
            
            InitializeComponent();
            SetupPrintPreview();
            LoadDocument();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة
            this.Text = $"معاينة الطباعة - {_document.Title}";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = SystemIcons.Application;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // شريط الأدوات
            _toolStrip = new ToolStrip
            {
                Dock = DockStyle.Top,
                RightToLeft = RightToLeft.Yes
            };

            // أزرار شريط الأدوات
            var btnPrint = new ToolStripButton
            {
                Text = "طباعة",
                Image = SystemIcons.Application.ToBitmap(),
                ImageScaling = ToolStripItemImageScaling.SizeToFit,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnPrint.Click += BtnPrint_Click;

            var btnPageSetup = new ToolStripButton
            {
                Text = "إعداد الصفحة",
                Image = SystemIcons.Application.ToBitmap(),
                ImageScaling = ToolStripItemImageScaling.SizeToFit,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnPageSetup.Click += BtnPageSetup_Click;

            var separator1 = new ToolStripSeparator();

            var btnZoomIn = new ToolStripButton
            {
                Text = "تكبير",
                Image = SystemIcons.Application.ToBitmap(),
                ImageScaling = ToolStripItemImageScaling.SizeToFit,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnZoomIn.Click += BtnZoomIn_Click;

            var btnZoomOut = new ToolStripButton
            {
                Text = "تصغير",
                Image = SystemIcons.Application.ToBitmap(),
                ImageScaling = ToolStripItemImageScaling.SizeToFit,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnZoomOut.Click += BtnZoomOut_Click;

            var btnZoomFit = new ToolStripButton
            {
                Text = "ملء الشاشة",
                Image = SystemIcons.Application.ToBitmap(),
                ImageScaling = ToolStripItemImageScaling.SizeToFit,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnZoomFit.Click += BtnZoomFit_Click;

            var separator2 = new ToolStripSeparator();

            var btnClose = new ToolStripButton
            {
                Text = "إغلاق",
                Image = SystemIcons.Application.ToBitmap(),
                ImageScaling = ToolStripItemImageScaling.SizeToFit,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText
            };
            btnClose.Click += BtnClose_Click;

            _toolStrip.Items.AddRange(new ToolStripItem[]
            {
                btnPrint, btnPageSetup, separator1,
                btnZoomIn, btnZoomOut, btnZoomFit, separator2,
                btnClose
            });

            // معاينة الطباعة
            _previewControl = new PrintPreviewControl
            {
                Dock = DockStyle.Fill,
                UseAntiAlias = true,
                Zoom = 1.0
            };

            // شريط الحالة
            _statusStrip = new StatusStrip
            {
                RightToLeft = RightToLeft.Yes
            };

            var statusLabel = new ToolStripStatusLabel
            {
                Text = "جاهز للطباعة",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            var printerLabel = new ToolStripStatusLabel
            {
                Text = $"الطابعة: {_settings.PrinterName}",
                BorderSides = ToolStripStatusLabelBorderSides.Left
            };

            _statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, printerLabel });

            // إضافة العناصر للنافذة
            this.Controls.Add(_previewControl);
            this.Controls.Add(_toolStrip);
            this.Controls.Add(_statusStrip);

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void SetupPrintPreview()
        {
            try
            {
                // إنشاء مستند الطباعة
                _printDocument = new System.Drawing.Printing.PrintDocument();
                
                // إعداد الطابعة
                if (!string.IsNullOrEmpty(_settings.PrinterName))
                {
                    _printDocument.PrinterSettings.PrinterName = _settings.PrinterName;
                }

                // إعداد الصفحة
                _printDocument.DefaultPageSettings.Landscape = (_settings.Orientation == PrintOrientation.Landscape);
                
                // تحديد معالج الطباعة حسب نوع المستند
                switch (_document.Type)
                {
                    case PrintType.Invoice:
                        _printDocument.PrintPage += PrintInvoicePage;
                        break;
                    case PrintType.Report:
                        _printDocument.PrintPage += PrintReportPage;
                        break;
                    default:
                        _printDocument.PrintPage += PrintGenericPage;
                        break;
                }

                // ربط المعاينة بالمستند
                _previewControl.Document = _printDocument;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadDocument()
        {
            try
            {
                // تحديث معاينة الطباعة
                _previewControl.InvalidatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region معالجات الطباعة

        private void PrintInvoicePage(object sender, PrintPageEventArgs e)
        {
            // استخدام نفس معالج الطباعة من PrintService
            var printService = PrintService.Instance;
            var method = typeof(PrintService).GetMethod("PrintInvoicePage", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(printService, new object[] { sender, e, _document });
        }

        private void PrintReportPage(object sender, PrintPageEventArgs e)
        {
            // استخدام نفس معالج الطباعة من PrintService
            var printService = PrintService.Instance;
            var method = typeof(PrintService).GetMethod("PrintReportPage", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(printService, new object[] { sender, e, _document });
        }

        private void PrintGenericPage(object sender, PrintPageEventArgs e)
        {
            // استخدام نفس معالج الطباعة من PrintService
            var printService = PrintService.Instance;
            var method = typeof(PrintService).GetMethod("PrintGenericPage", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(printService, new object[] { sender, e, _document });
        }

        #endregion

        #region معالجات الأحداث

        private async void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printService = PrintService.Instance;
                var result = await printService.PrintDocument(_document, _settings);

                if (result.Success)
                {
                    MessageBox.Show("تمت الطباعة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show($"فشل في الطباعة: {result.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPageSetup_Click(object sender, EventArgs e)
        {
            try
            {
                var pageSetupDialog = new PageSetupDialog
                {
                    Document = _printDocument,
                    EnableMetric = true
                };

                if (pageSetupDialog.ShowDialog() == DialogResult.OK)
                {
                    _previewControl.InvalidatePreview();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الصفحة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnZoomIn_Click(object sender, EventArgs e)
        {
            if (_previewControl.Zoom < 2.0)
            {
                _previewControl.Zoom += 0.25;
            }
        }

        private void BtnZoomOut_Click(object sender, EventArgs e)
        {
            if (_previewControl.Zoom > 0.25)
            {
                _previewControl.Zoom -= 0.25;
            }
        }

        private void BtnZoomFit_Click(object sender, EventArgs e)
        {
            _previewControl.AutoZoom = true;
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _printDocument?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
