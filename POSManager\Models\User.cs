namespace POSManager.Models
{
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string PasswordHash { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLogin { get; set; }
    }
    
    public enum UserRole
    {
        Admin,
        Cashier
    }
    
    public class UserPermissions
    {
        public bool CanManageUsers { get; set; }
        public bool CanManageProducts { get; set; }
        public bool CanProcessSales { get; set; }
        public bool CanViewReports { get; set; }
        public bool CanManageSettings { get; set; }
        public bool CanManageInventory { get; set; }
        public bool CanDeleteSales { get; set; }
        public bool CanGiveDiscounts { get; set; }
        
        public static UserPermissions GetPermissions(UserRole role)
        {
            return role switch
            {
                UserRole.Admin => new UserPermissions
                {
                    CanManageUsers = true,
                    CanManageProducts = true,
                    CanProcessSales = true,
                    CanViewReports = true,
                    CanManageSettings = true,
                    CanManageInventory = true,
                    CanDeleteSales = true,
                    CanGiveDiscounts = true
                },
                UserRole.Cashier => new UserPermissions
                {
                    CanManageUsers = false,
                    CanManageProducts = false,
                    CanProcessSales = true,
                    CanViewReports = false,
                    CanManageSettings = false,
                    CanManageInventory = false,
                    CanDeleteSales = false,
                    CanGiveDiscounts = false
                },
                _ => new UserPermissions()
            };
        }
    }
}
