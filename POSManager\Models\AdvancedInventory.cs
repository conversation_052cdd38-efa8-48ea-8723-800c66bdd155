using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace POSManager.Models
{
    // تعداد أنواع حركة المخزون
    public enum InventoryMovementType
    {
        Purchase = 1,           // شراء
        Sale = 2,              // بيع
        Return = 3,            // مرتجع
        Adjustment = 4,        // تسوية
        Transfer = 5,          // نقل
        Damage = 6,            // تالف
        Loss = 7,              // فقدان
        Found = 8,             // عثور عليه
        Production = 9,        // إنتاج
        Consumption = 10       // استهلاك
    }

    // تعداد حالة أمر الشراء
    public enum PurchaseOrderStatus
    {
        Draft = 1,             // مسودة
        Pending = 2,           // معلق
        Approved = 3,          // معتمد
        Sent = 4,              // مرسل
        Ordered = 5,           // مطلوب
        PartiallyReceived = 6, // مستلم جزئياً
        Received = 7,          // مستلم
        Cancelled = 8,         // ملغي
        Closed = 9             // مغلق
    }

    // تعداد أسباب تسوية المخزون
    public enum StockAdjustmentReason
    {
        PhysicalCount = 1,     // جرد فعلي
        Damage = 2,            // تلف
        Theft = 3,             // سرقة
        Loss = 4,              // فقدان
        Found = 5,             // عثور عليه
        Correction = 6,        // تصحيح
        Expiry = 7,            // انتهاء صلاحية
        Other = 8              // أخرى
    }

    // تعداد مستوى التنبيه
    public enum AlertLevel
    {
        Info = 1,              // معلومات
        Warning = 2,           // تحذير
        Critical = 3,          // حرج
        Emergency = 4          // طوارئ
    }

    // نموذج حركة المخزون
    public class InventoryMovement
    {
        public int Id { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        public InventoryMovementType MovementType { get; set; }
        
        [Required]
        public decimal Quantity { get; set; }
        
        [Required]
        public decimal UnitCost { get; set; }
        
        public decimal TotalCost => Quantity * UnitCost;
        
        public decimal BalanceAfter { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        [StringLength(100)]
        public string? ReferenceNumber { get; set; }
        
        public int? ReferenceId { get; set; }
        
        [Required]
        public DateTime MovementDate { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // خصائص التنقل
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public string UserName { get; set; } = "";
    }

    // نموذج المورد
    public class Supplier
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = "";
        
        [StringLength(50)]
        public string? Code { get; set; }
        
        [StringLength(100)]
        public string? ContactPerson { get; set; }
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(100)]
        public string? Email { get; set; }
        
        [StringLength(200)]
        public string? Address { get; set; }
        
        [StringLength(50)]
        public string? City { get; set; }
        
        [StringLength(20)]
        public string? PostalCode { get; set; }
        
        [StringLength(50)]
        public string? Country { get; set; }
        
        [StringLength(50)]
        public string? TaxNumber { get; set; }
        
        public int PaymentTermsDays { get; set; } = 30;
        
        public decimal CreditLimit { get; set; } = 0;
        
        public decimal CurrentBalance { get; set; } = 0;
        
        public bool IsActive { get; set; } = true;
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? LastOrderDate { get; set; }
        
        public decimal TotalOrders { get; set; } = 0;
        
        public int OrderCount { get; set; } = 0;
        
        // تقييم الأداء
        public decimal PerformanceRating { get; set; } = 0;
        
        public int OnTimeDeliveries { get; set; } = 0;
        
        public int TotalDeliveries { get; set; } = 0;
        
        public decimal OnTimeDeliveryRate => TotalDeliveries > 0 ? (decimal)OnTimeDeliveries / TotalDeliveries * 100 : 0;
    }

    // نموذج أمر الشراء
    public class PurchaseOrder
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = "";
        
        [Required]
        public int SupplierId { get; set; }
        
        [Required]
        public DateTime OrderDate { get; set; }
        
        public DateTime? ExpectedDeliveryDate { get; set; }
        
        public DateTime? ActualDeliveryDate { get; set; }
        
        [Required]
        public PurchaseOrderStatus Status { get; set; } = PurchaseOrderStatus.Draft;
        
        public decimal Subtotal { get; set; }
        
        public decimal TaxAmount { get; set; }
        
        public decimal DiscountAmount { get; set; }
        
        public decimal ShippingCost { get; set; }
        
        public decimal Total { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        [Required]
        public int CreatedBy { get; set; }
        
        public int? ApprovedBy { get; set; }
        
        public DateTime? ApprovedAt { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        // خصائص التنقل
        public string SupplierName { get; set; } = "";
        public string CreatedByName { get; set; } = "";
        public string? ApprovedByName { get; set; }
        
        public List<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
    }

    // نموذج عنصر أمر الشراء
    public class PurchaseOrderItem
    {
        public int Id { get; set; }
        
        [Required]
        public int PurchaseOrderId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        public decimal Quantity { get; set; }
        
        [Required]
        public decimal UnitCost { get; set; }
        
        public decimal DiscountAmount { get; set; }
        
        public decimal Total { get; set; }
        
        public decimal ReceivedQuantity { get; set; }
        
        public decimal RemainingQuantity => Quantity - ReceivedQuantity;
        
        public bool IsCompleted => ReceivedQuantity >= Quantity;
        
        [StringLength(200)]
        public string? Notes { get; set; }
        
        // خصائص التنقل
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public string Unit { get; set; } = "";
    }

    // نموذج استلام أمر الشراء
    public class PurchaseOrderReceipt
    {
        public int Id { get; set; }
        
        [Required]
        public int PurchaseOrderId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string ReceiptNumber { get; set; } = "";
        
        [Required]
        public DateTime ReceiptDate { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        [Required]
        public int ReceivedBy { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // خصائص التنقل
        public string PurchaseOrderNumber { get; set; } = "";
        public string ReceivedByName { get; set; } = "";
        
        public List<PurchaseOrderReceiptItem> Items { get; set; } = new List<PurchaseOrderReceiptItem>();
    }

    // نموذج عنصر استلام أمر الشراء
    public class PurchaseOrderReceiptItem
    {
        public int Id { get; set; }
        
        [Required]
        public int ReceiptId { get; set; }
        
        [Required]
        public int PurchaseOrderItemId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        public decimal ReceivedQuantity { get; set; }
        
        [Required]
        public decimal UnitCost { get; set; }
        
        public decimal Total { get; set; }
        
        [StringLength(200)]
        public string? Notes { get; set; }
        
        // خصائص التنقل
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public decimal OrderedQuantity { get; set; }
        public decimal PreviouslyReceived { get; set; }
        public decimal RemainingQuantity { get; set; }
    }

    // نموذج تسوية المخزون
    public class StockAdjustment
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string AdjustmentNumber { get; set; } = "";

        [Required]
        public DateTime AdjustmentDate { get; set; }

        [Required]
        public StockAdjustmentReason Reason { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [Required]
        public int CreatedBy { get; set; }

        public int? ApprovedBy { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public bool IsApproved { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public decimal TotalAdjustmentValue { get; set; }

        // خصائص التنقل
        public string CreatedByName { get; set; } = "";
        public string? ApprovedByName { get; set; }

        public List<StockAdjustmentItem> Items { get; set; } = new List<StockAdjustmentItem>();
    }

    // نموذج عنصر تسوية المخزون
    public class StockAdjustmentItem
    {
        public int Id { get; set; }

        [Required]
        public int AdjustmentId { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required]
        public decimal SystemQuantity { get; set; }

        [Required]
        public decimal ActualQuantity { get; set; }

        public decimal AdjustmentQuantity => ActualQuantity - SystemQuantity;

        [Required]
        public decimal UnitCost { get; set; }

        public decimal AdjustmentValue => AdjustmentQuantity * UnitCost;

        [StringLength(200)]
        public string? Notes { get; set; }

        // خصائص التنقل
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public string Unit { get; set; } = "";
    }

    // نموذج تنبيهات المخزون
    public class InventoryAlert
    {
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required]
        public AlertLevel Level { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = "";

        [Required]
        [StringLength(500)]
        public string Message { get; set; } = "";

        public decimal CurrentStock { get; set; }

        public decimal MinimumStock { get; set; }

        public decimal ReorderLevel { get; set; }

        public bool IsRead { get; set; } = false;

        public bool IsResolved { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? ReadAt { get; set; }

        public DateTime? ResolvedAt { get; set; }

        public int? ResolvedBy { get; set; }

        // خصائص التنقل
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public string? ResolvedByName { get; set; }
    }

    // نموذج إعدادات المخزون
    public class InventorySettings
    {
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        public decimal MinimumStock { get; set; } = 0;

        public decimal ReorderLevel { get; set; } = 0;

        public decimal MaximumStock { get; set; } = 0;

        public decimal ReorderQuantity { get; set; } = 0;

        public int? PreferredSupplierId { get; set; }

        public decimal LeadTimeDays { get; set; } = 0;

        public bool AutoReorder { get; set; } = false;

        public bool EnableAlerts { get; set; } = true;

        public DateTime? LastUpdated { get; set; }

        public int? UpdatedBy { get; set; }

        // خصائص التنقل
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public string? PreferredSupplierName { get; set; }
        public string? UpdatedByName { get; set; }
    }

    // نموذج إحصائيات المخزون
    public class InventoryStatistics
    {
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public int OverStockProducts { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public decimal AverageInventoryValue { get; set; }
        public int TotalMovements { get; set; }
        public int PendingPurchaseOrders { get; set; }
        public int ActiveSuppliers { get; set; }
        public int UnresolvedAlerts { get; set; }
        public decimal InventoryTurnoverRatio { get; set; }
        public int DaysOfInventory { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    // نموذج تقرير حركة المخزون
    public class InventoryMovementReport
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = "";
        public string ProductCode { get; set; } = "";
        public decimal OpeningBalance { get; set; }
        public decimal TotalIn { get; set; }
        public decimal TotalOut { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal AverageCost { get; set; }
        public decimal TotalValue { get; set; }
        public int MovementCount { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }

    // نموذج تقرير أداء المورد
    public class SupplierPerformanceReport
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = "";
        public int TotalOrders { get; set; }
        public decimal TotalOrderValue { get; set; }
        public int OnTimeDeliveries { get; set; }
        public decimal OnTimeDeliveryRate { get; set; }
        public int LateDeliveries { get; set; }
        public decimal AverageDeliveryDays { get; set; }
        public decimal QualityRating { get; set; }
        public decimal PriceCompetitiveness { get; set; }
        public decimal OverallRating { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }

    // نموذج الجرد الفعلي
    public class PhysicalCount
    {
        public int Id { get; set; }
        public DateTime CountDate { get; set; }
        public string Notes { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedAt { get; set; }
        public List<PhysicalCountItem> Items { get; set; } = new List<PhysicalCountItem>();
    }

    // نموذج عنصر الجرد الفعلي
    public class PhysicalCountItem
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; } = "";
        public string ProductName { get; set; } = "";
        public string Category { get; set; } = "";
        public decimal SystemStock { get; set; }
        public decimal PhysicalStock { get; set; }
        public decimal Variance { get; set; }
        public bool IsCounted { get; set; }
        public string Notes { get; set; } = "";
    }
}
