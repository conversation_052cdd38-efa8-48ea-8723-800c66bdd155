namespace POSManager.Forms
{
    partial class SettingsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.tabControl1 = new TabControl();
            this.tabSystem = new TabPage();
            this.tabPrint = new TabPage();
            this.tabSecurity = new TabPage();
            this.tabBackup = new TabPage();
            this.tabUI = new TabPage();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnResetToDefault = new Button();
            this.btnExportSettings = new Button();
            this.btnImportSettings = new Button();

            // System Settings Controls
            this.lblCompanyName = new Label();
            this.txtCompanyName = new TextBox();
            this.lblCompanyAddress = new Label();
            this.txtCompanyAddress = new TextBox();
            this.lblCompanyPhone = new Label();
            this.txtCompanyPhone = new TextBox();
            this.lblCompanyEmail = new Label();
            this.txtCompanyEmail = new TextBox();
            this.lblTaxNumber = new Label();
            this.txtTaxNumber = new TextBox();
            this.lblTaxRate = new Label();
            this.numTaxRate = new NumericUpDown();
            this.lblCurrency = new Label();
            this.txtCurrency = new TextBox();
            this.lblCurrencySymbol = new Label();
            this.txtCurrencySymbol = new TextBox();
            this.chkRTL = new CheckBox();

            // Print Settings Controls
            this.lblDefaultPrinter = new Label();
            this.txtDefaultPrinter = new TextBox();
            this.btnSelectPrinter = new Button();
            this.lblPrinterType = new Label();
            this.cmbPrinterType = new ComboBox();
            this.lblPaperSize = new Label();
            this.cmbPaperSize = new ComboBox();
            this.chkAutoPrint = new CheckBox();
            this.chkPrintLogo = new CheckBox();
            this.chkPrintHeader = new CheckBox();
            this.chkPrintFooter = new CheckBox();
            this.lblHeaderText = new Label();
            this.txtHeaderText = new TextBox();
            this.lblFooterText = new Label();
            this.txtFooterText = new TextBox();
            this.lblCopiesCount = new Label();
            this.numCopiesCount = new NumericUpDown();
            this.chkPrintBarcode = new CheckBox();
            this.lblLogoPath = new Label();
            this.txtLogoPath = new TextBox();
            this.btnSelectLogo = new Button();

            // Security Settings Controls
            this.chkRequireStrongPassword = new CheckBox();
            this.lblMinPasswordLength = new Label();
            this.numMinPasswordLength = new NumericUpDown();
            this.lblMaxLoginAttempts = new Label();
            this.numMaxLoginAttempts = new NumericUpDown();
            this.lblAutoLogoutMinutes = new Label();
            this.numAutoLogoutMinutes = new NumericUpDown();
            this.chkEnableActivityLog = new CheckBox();

            // Backup Settings Controls
            this.lblBackupPath = new Label();
            this.txtBackupPath = new TextBox();
            this.btnSelectBackupPath = new Button();
            this.lblBackupFrequency = new Label();
            this.cmbBackupFrequency = new ComboBox();
            this.chkAutoBackup = new CheckBox();
            this.chkEmailBackup = new CheckBox();
            this.lblBackupEmail = new Label();
            this.txtBackupEmail = new TextBox();

            // UI Settings Controls
            this.lblTheme = new Label();
            this.cmbTheme = new ComboBox();
            this.lblPrimaryColor = new Label();
            this.btnPrimaryColor = new Button();
            this.lblSecondaryColor = new Label();
            this.btnSecondaryColor = new Button();
            this.lblFontFamily = new Label();
            this.cmbFontFamily = new ComboBox();
            this.lblFontSize = new Label();
            this.numFontSize = new NumericUpDown();
            this.chkShowToolbar = new CheckBox();

            this.tabControl1.SuspendLayout();
            this.tabSystem.SuspendLayout();
            this.tabPrint.SuspendLayout();
            this.tabSecurity.SuspendLayout();
            this.tabBackup.SuspendLayout();
            this.tabUI.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTaxRate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCopiesCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPasswordLength)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxLoginAttempts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAutoLogoutMinutes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFontSize)).BeginInit();
            this.SuspendLayout();

            // 
            // Form
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 580);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnResetToDefault);
            this.Controls.Add(this.btnExportSettings);
            this.Controls.Add(this.btnImportSettings);
            this.Font = new Font("Tahoma", 10F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SettingsForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "إعدادات النظام";

            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabSystem);
            this.tabControl1.Controls.Add(this.tabPrint);
            this.tabControl1.Controls.Add(this.tabSecurity);
            this.tabControl1.Controls.Add(this.tabBackup);
            this.tabControl1.Controls.Add(this.tabUI);
            this.tabControl1.Location = new Point(12, 12);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new Size(776, 500);
            this.tabControl1.TabIndex = 0;

            // 
            // tabSystem
            // 
            this.tabSystem.Controls.Add(this.lblCompanyName);
            this.tabSystem.Controls.Add(this.txtCompanyName);
            this.tabSystem.Controls.Add(this.lblCompanyAddress);
            this.tabSystem.Controls.Add(this.txtCompanyAddress);
            this.tabSystem.Controls.Add(this.lblCompanyPhone);
            this.tabSystem.Controls.Add(this.txtCompanyPhone);
            this.tabSystem.Controls.Add(this.lblCompanyEmail);
            this.tabSystem.Controls.Add(this.txtCompanyEmail);
            this.tabSystem.Controls.Add(this.lblTaxNumber);
            this.tabSystem.Controls.Add(this.txtTaxNumber);
            this.tabSystem.Controls.Add(this.lblTaxRate);
            this.tabSystem.Controls.Add(this.numTaxRate);
            this.tabSystem.Controls.Add(this.lblCurrency);
            this.tabSystem.Controls.Add(this.txtCurrency);
            this.tabSystem.Controls.Add(this.lblCurrencySymbol);
            this.tabSystem.Controls.Add(this.txtCurrencySymbol);
            this.tabSystem.Controls.Add(this.chkRTL);
            this.tabSystem.Location = new Point(4, 25);
            this.tabSystem.Name = "tabSystem";
            this.tabSystem.Padding = new Padding(3);
            this.tabSystem.Size = new Size(768, 471);
            this.tabSystem.TabIndex = 0;
            this.tabSystem.Text = "إعدادات النظام";
            this.tabSystem.UseVisualStyleBackColor = true;

            // System Settings Layout
            this.lblCompanyName.AutoSize = true;
            this.lblCompanyName.Location = new Point(20, 20);
            this.lblCompanyName.Name = "lblCompanyName";
            this.lblCompanyName.Size = new Size(80, 17);
            this.lblCompanyName.TabIndex = 0;
            this.lblCompanyName.Text = "اسم الشركة:";

            this.txtCompanyName.Location = new Point(120, 17);
            this.txtCompanyName.Name = "txtCompanyName";
            this.txtCompanyName.Size = new Size(300, 24);
            this.txtCompanyName.TabIndex = 1;

            this.lblCompanyAddress.AutoSize = true;
            this.lblCompanyAddress.Location = new Point(20, 60);
            this.lblCompanyAddress.Name = "lblCompanyAddress";
            this.lblCompanyAddress.Size = new Size(85, 17);
            this.lblCompanyAddress.TabIndex = 2;
            this.lblCompanyAddress.Text = "عنوان الشركة:";

            this.txtCompanyAddress.Location = new Point(120, 57);
            this.txtCompanyAddress.Multiline = true;
            this.txtCompanyAddress.Name = "txtCompanyAddress";
            this.txtCompanyAddress.Size = new Size(300, 60);
            this.txtCompanyAddress.TabIndex = 3;

            this.lblCompanyPhone.AutoSize = true;
            this.lblCompanyPhone.Location = new Point(20, 140);
            this.lblCompanyPhone.Name = "lblCompanyPhone";
            this.lblCompanyPhone.Size = new Size(82, 17);
            this.lblCompanyPhone.TabIndex = 4;
            this.lblCompanyPhone.Text = "هاتف الشركة:";

            this.txtCompanyPhone.Location = new Point(120, 137);
            this.txtCompanyPhone.Name = "txtCompanyPhone";
            this.txtCompanyPhone.Size = new Size(200, 24);
            this.txtCompanyPhone.TabIndex = 5;

            this.lblCompanyEmail.AutoSize = true;
            this.lblCompanyEmail.Location = new Point(20, 180);
            this.lblCompanyEmail.Name = "lblCompanyEmail";
            this.lblCompanyEmail.Size = new Size(110, 17);
            this.lblCompanyEmail.TabIndex = 6;
            this.lblCompanyEmail.Text = "بريد الشركة الإلكتروني:";

            this.txtCompanyEmail.Location = new Point(140, 177);
            this.txtCompanyEmail.Name = "txtCompanyEmail";
            this.txtCompanyEmail.Size = new Size(250, 24);
            this.txtCompanyEmail.TabIndex = 7;

            this.lblTaxNumber.AutoSize = true;
            this.lblTaxNumber.Location = new Point(20, 220);
            this.lblTaxNumber.Name = "lblTaxNumber";
            this.lblTaxNumber.Size = new Size(90, 17);
            this.lblTaxNumber.TabIndex = 8;
            this.lblTaxNumber.Text = "الرقم الضريبي:";

            this.txtTaxNumber.Location = new Point(120, 217);
            this.txtTaxNumber.Name = "txtTaxNumber";
            this.txtTaxNumber.Size = new Size(200, 24);
            this.txtTaxNumber.TabIndex = 9;

            this.lblTaxRate.AutoSize = true;
            this.lblTaxRate.Location = new Point(20, 260);
            this.lblTaxRate.Name = "lblTaxRate";
            this.lblTaxRate.Size = new Size(95, 17);
            this.lblTaxRate.TabIndex = 10;
            this.lblTaxRate.Text = "نسبة الضريبة (%):";

            this.numTaxRate.DecimalPlaces = 2;
            this.numTaxRate.Location = new Point(120, 257);
            this.numTaxRate.Maximum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numTaxRate.Name = "numTaxRate";
            this.numTaxRate.Size = new Size(100, 24);
            this.numTaxRate.TabIndex = 11;

            this.lblCurrency.AutoSize = true;
            this.lblCurrency.Location = new Point(20, 300);
            this.lblCurrency.Name = "lblCurrency";
            this.lblCurrency.Size = new Size(45, 17);
            this.lblCurrency.TabIndex = 12;
            this.lblCurrency.Text = "العملة:";

            this.txtCurrency.Location = new Point(120, 297);
            this.txtCurrency.Name = "txtCurrency";
            this.txtCurrency.Size = new Size(100, 24);
            this.txtCurrency.TabIndex = 13;

            this.lblCurrencySymbol.AutoSize = true;
            this.lblCurrencySymbol.Location = new Point(250, 300);
            this.lblCurrencySymbol.Name = "lblCurrencySymbol";
            this.lblCurrencySymbol.Size = new Size(70, 17);
            this.lblCurrencySymbol.TabIndex = 14;
            this.lblCurrencySymbol.Text = "رمز العملة:";

            this.txtCurrencySymbol.Location = new Point(330, 297);
            this.txtCurrencySymbol.Name = "txtCurrencySymbol";
            this.txtCurrencySymbol.Size = new Size(60, 24);
            this.txtCurrencySymbol.TabIndex = 15;

            this.chkRTL.AutoSize = true;
            this.chkRTL.Location = new Point(20, 340);
            this.chkRTL.Name = "chkRTL";
            this.chkRTL.Size = new Size(150, 21);
            this.chkRTL.TabIndex = 16;
            this.chkRTL.Text = "واجهة من اليمين إلى اليسار";
            this.chkRTL.UseVisualStyleBackColor = true;

            //
            // tabPrint
            //
            this.tabPrint.Controls.Add(this.lblDefaultPrinter);
            this.tabPrint.Controls.Add(this.txtDefaultPrinter);
            this.tabPrint.Controls.Add(this.btnSelectPrinter);
            this.tabPrint.Controls.Add(this.lblPrinterType);
            this.tabPrint.Controls.Add(this.cmbPrinterType);
            this.tabPrint.Controls.Add(this.lblPaperSize);
            this.tabPrint.Controls.Add(this.cmbPaperSize);
            this.tabPrint.Controls.Add(this.chkAutoPrint);
            this.tabPrint.Controls.Add(this.chkPrintLogo);
            this.tabPrint.Controls.Add(this.chkPrintHeader);
            this.tabPrint.Controls.Add(this.chkPrintFooter);
            this.tabPrint.Controls.Add(this.lblHeaderText);
            this.tabPrint.Controls.Add(this.txtHeaderText);
            this.tabPrint.Controls.Add(this.lblFooterText);
            this.tabPrint.Controls.Add(this.txtFooterText);
            this.tabPrint.Controls.Add(this.lblCopiesCount);
            this.tabPrint.Controls.Add(this.numCopiesCount);
            this.tabPrint.Controls.Add(this.chkPrintBarcode);
            this.tabPrint.Controls.Add(this.lblLogoPath);
            this.tabPrint.Controls.Add(this.txtLogoPath);
            this.tabPrint.Controls.Add(this.btnSelectLogo);
            this.tabPrint.Location = new Point(4, 25);
            this.tabPrint.Name = "tabPrint";
            this.tabPrint.Padding = new Padding(3);
            this.tabPrint.Size = new Size(768, 471);
            this.tabPrint.TabIndex = 1;
            this.tabPrint.Text = "إعدادات الطباعة";
            this.tabPrint.UseVisualStyleBackColor = true;

            // Print Settings Layout
            this.lblDefaultPrinter.AutoSize = true;
            this.lblDefaultPrinter.Location = new Point(20, 20);
            this.lblDefaultPrinter.Name = "lblDefaultPrinter";
            this.lblDefaultPrinter.Size = new Size(100, 17);
            this.lblDefaultPrinter.TabIndex = 0;
            this.lblDefaultPrinter.Text = "الطابعة الافتراضية:";

            this.txtDefaultPrinter.Location = new Point(130, 17);
            this.txtDefaultPrinter.Name = "txtDefaultPrinter";
            this.txtDefaultPrinter.ReadOnly = true;
            this.txtDefaultPrinter.Size = new Size(250, 24);
            this.txtDefaultPrinter.TabIndex = 1;

            this.btnSelectPrinter.Location = new Point(390, 16);
            this.btnSelectPrinter.Name = "btnSelectPrinter";
            this.btnSelectPrinter.Size = new Size(80, 26);
            this.btnSelectPrinter.TabIndex = 2;
            this.btnSelectPrinter.Text = "اختيار";
            this.btnSelectPrinter.UseVisualStyleBackColor = true;

            this.lblPrinterType.AutoSize = true;
            this.lblPrinterType.Location = new Point(20, 60);
            this.lblPrinterType.Name = "lblPrinterType";
            this.lblPrinterType.Size = new Size(70, 17);
            this.lblPrinterType.TabIndex = 3;
            this.lblPrinterType.Text = "نوع الطابعة:";

            this.cmbPrinterType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPrinterType.Items.AddRange(new object[] { "حرارية", "نفث حبر", "ليزر", "نقطية" });
            this.cmbPrinterType.Location = new Point(130, 57);
            this.cmbPrinterType.Name = "cmbPrinterType";
            this.cmbPrinterType.Size = new Size(150, 24);
            this.cmbPrinterType.TabIndex = 4;

            this.lblPaperSize.AutoSize = true;
            this.lblPaperSize.Location = new Point(300, 60);
            this.lblPaperSize.Name = "lblPaperSize";
            this.lblPaperSize.Size = new Size(65, 17);
            this.lblPaperSize.TabIndex = 5;
            this.lblPaperSize.Text = "حجم الورق:";

            this.cmbPaperSize.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaperSize.Items.AddRange(new object[] { "80 مم حراري", "58 مم حراري", "A4", "A5", "مخصص" });
            this.cmbPaperSize.Location = new Point(370, 57);
            this.cmbPaperSize.Name = "cmbPaperSize";
            this.cmbPaperSize.Size = new Size(120, 24);
            this.cmbPaperSize.TabIndex = 6;

            this.chkAutoPrint.AutoSize = true;
            this.chkAutoPrint.Location = new Point(20, 100);
            this.chkAutoPrint.Name = "chkAutoPrint";
            this.chkAutoPrint.Size = new Size(120, 21);
            this.chkAutoPrint.TabIndex = 7;
            this.chkAutoPrint.Text = "طباعة تلقائية";
            this.chkAutoPrint.UseVisualStyleBackColor = true;

            this.chkPrintLogo.AutoSize = true;
            this.chkPrintLogo.Location = new Point(160, 100);
            this.chkPrintLogo.Name = "chkPrintLogo";
            this.chkPrintLogo.Size = new Size(90, 21);
            this.chkPrintLogo.TabIndex = 8;
            this.chkPrintLogo.Text = "طباعة الشعار";
            this.chkPrintLogo.UseVisualStyleBackColor = true;

            this.chkPrintHeader.AutoSize = true;
            this.chkPrintHeader.Location = new Point(270, 100);
            this.chkPrintHeader.Name = "chkPrintHeader";
            this.chkPrintHeader.Size = new Size(95, 21);
            this.chkPrintHeader.TabIndex = 9;
            this.chkPrintHeader.Text = "طباعة الرأس";
            this.chkPrintHeader.UseVisualStyleBackColor = true;

            this.chkPrintFooter.AutoSize = true;
            this.chkPrintFooter.Location = new Point(380, 100);
            this.chkPrintFooter.Name = "chkPrintFooter";
            this.chkPrintFooter.Size = new Size(100, 21);
            this.chkPrintFooter.TabIndex = 10;
            this.chkPrintFooter.Text = "طباعة التذييل";
            this.chkPrintFooter.UseVisualStyleBackColor = true;

            this.lblHeaderText.AutoSize = true;
            this.lblHeaderText.Location = new Point(20, 140);
            this.lblHeaderText.Name = "lblHeaderText";
            this.lblHeaderText.Size = new Size(65, 17);
            this.lblHeaderText.TabIndex = 11;
            this.lblHeaderText.Text = "نص الرأس:";

            this.txtHeaderText.Location = new Point(90, 137);
            this.txtHeaderText.Multiline = true;
            this.txtHeaderText.Name = "txtHeaderText";
            this.txtHeaderText.Size = new Size(300, 50);
            this.txtHeaderText.TabIndex = 12;

            this.lblFooterText.AutoSize = true;
            this.lblFooterText.Location = new Point(20, 210);
            this.lblFooterText.Name = "lblFooterText";
            this.lblFooterText.Size = new Size(70, 17);
            this.lblFooterText.TabIndex = 13;
            this.lblFooterText.Text = "نص التذييل:";

            this.txtFooterText.Location = new Point(90, 207);
            this.txtFooterText.Multiline = true;
            this.txtFooterText.Name = "txtFooterText";
            this.txtFooterText.Size = new Size(300, 50);
            this.txtFooterText.TabIndex = 14;

            this.lblCopiesCount.AutoSize = true;
            this.lblCopiesCount.Location = new Point(20, 280);
            this.lblCopiesCount.Name = "lblCopiesCount";
            this.lblCopiesCount.Size = new Size(75, 17);
            this.lblCopiesCount.TabIndex = 15;
            this.lblCopiesCount.Text = "عدد النسخ:";

            this.numCopiesCount.Location = new Point(100, 277);
            this.numCopiesCount.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            this.numCopiesCount.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            this.numCopiesCount.Name = "numCopiesCount";
            this.numCopiesCount.Size = new Size(80, 24);
            this.numCopiesCount.TabIndex = 16;
            this.numCopiesCount.Value = new decimal(new int[] { 1, 0, 0, 0 });

            this.chkPrintBarcode.AutoSize = true;
            this.chkPrintBarcode.Location = new Point(200, 280);
            this.chkPrintBarcode.Name = "chkPrintBarcode";
            this.chkPrintBarcode.Size = new Size(120, 21);
            this.chkPrintBarcode.TabIndex = 17;
            this.chkPrintBarcode.Text = "طباعة الباركود";
            this.chkPrintBarcode.UseVisualStyleBackColor = true;

            this.lblLogoPath.AutoSize = true;
            this.lblLogoPath.Location = new Point(20, 320);
            this.lblLogoPath.Name = "lblLogoPath";
            this.lblLogoPath.Size = new Size(75, 17);
            this.lblLogoPath.TabIndex = 18;
            this.lblLogoPath.Text = "مسار الشعار:";

            this.txtLogoPath.Location = new Point(100, 317);
            this.txtLogoPath.Name = "txtLogoPath";
            this.txtLogoPath.ReadOnly = true;
            this.txtLogoPath.Size = new Size(250, 24);
            this.txtLogoPath.TabIndex = 19;

            this.btnSelectLogo.Location = new Point(360, 316);
            this.btnSelectLogo.Name = "btnSelectLogo";
            this.btnSelectLogo.Size = new Size(80, 26);
            this.btnSelectLogo.TabIndex = 20;
            this.btnSelectLogo.Text = "اختيار";
            this.btnSelectLogo.UseVisualStyleBackColor = true;

            //
            // tabSecurity
            //
            this.tabSecurity.Controls.Add(this.chkRequireStrongPassword);
            this.tabSecurity.Controls.Add(this.lblMinPasswordLength);
            this.tabSecurity.Controls.Add(this.numMinPasswordLength);
            this.tabSecurity.Controls.Add(this.lblMaxLoginAttempts);
            this.tabSecurity.Controls.Add(this.numMaxLoginAttempts);
            this.tabSecurity.Controls.Add(this.lblAutoLogoutMinutes);
            this.tabSecurity.Controls.Add(this.numAutoLogoutMinutes);
            this.tabSecurity.Controls.Add(this.chkEnableActivityLog);
            this.tabSecurity.Location = new Point(4, 29);
            this.tabSecurity.Name = "tabSecurity";
            this.tabSecurity.Padding = new Padding(3);
            this.tabSecurity.Size = new Size(792, 417);
            this.tabSecurity.TabIndex = 2;
            this.tabSecurity.Text = "الأمان";
            this.tabSecurity.UseVisualStyleBackColor = true;

            this.chkRequireStrongPassword.AutoSize = true;
            this.chkRequireStrongPassword.Location = new Point(20, 30);
            this.chkRequireStrongPassword.Name = "chkRequireStrongPassword";
            this.chkRequireStrongPassword.Size = new Size(150, 21);
            this.chkRequireStrongPassword.TabIndex = 0;
            this.chkRequireStrongPassword.Text = "كلمة مرور قوية مطلوبة";
            this.chkRequireStrongPassword.UseVisualStyleBackColor = true;

            this.lblMinPasswordLength.AutoSize = true;
            this.lblMinPasswordLength.Location = new Point(20, 70);
            this.lblMinPasswordLength.Name = "lblMinPasswordLength";
            this.lblMinPasswordLength.Size = new Size(120, 17);
            this.lblMinPasswordLength.TabIndex = 1;
            this.lblMinPasswordLength.Text = "الحد الأدنى لطول كلمة المرور:";

            this.numMinPasswordLength.Location = new Point(150, 67);
            this.numMinPasswordLength.Maximum = new decimal(new int[] { 20, 0, 0, 0 });
            this.numMinPasswordLength.Minimum = new decimal(new int[] { 4, 0, 0, 0 });
            this.numMinPasswordLength.Name = "numMinPasswordLength";
            this.numMinPasswordLength.Size = new Size(80, 24);
            this.numMinPasswordLength.TabIndex = 2;
            this.numMinPasswordLength.Value = new decimal(new int[] { 6, 0, 0, 0 });

            this.lblMaxLoginAttempts.AutoSize = true;
            this.lblMaxLoginAttempts.Location = new Point(20, 110);
            this.lblMaxLoginAttempts.Name = "lblMaxLoginAttempts";
            this.lblMaxLoginAttempts.Size = new Size(120, 17);
            this.lblMaxLoginAttempts.TabIndex = 3;
            this.lblMaxLoginAttempts.Text = "الحد الأقصى لمحاولات الدخول:";

            this.numMaxLoginAttempts.Location = new Point(150, 107);
            this.numMaxLoginAttempts.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            this.numMaxLoginAttempts.Minimum = new decimal(new int[] { 3, 0, 0, 0 });
            this.numMaxLoginAttempts.Name = "numMaxLoginAttempts";
            this.numMaxLoginAttempts.Size = new Size(80, 24);
            this.numMaxLoginAttempts.TabIndex = 4;
            this.numMaxLoginAttempts.Value = new decimal(new int[] { 5, 0, 0, 0 });

            this.lblAutoLogoutMinutes.AutoSize = true;
            this.lblAutoLogoutMinutes.Location = new Point(20, 150);
            this.lblAutoLogoutMinutes.Name = "lblAutoLogoutMinutes";
            this.lblAutoLogoutMinutes.Size = new Size(120, 17);
            this.lblAutoLogoutMinutes.TabIndex = 5;
            this.lblAutoLogoutMinutes.Text = "تسجيل خروج تلقائي (دقائق):";

            this.numAutoLogoutMinutes.Location = new Point(150, 147);
            this.numAutoLogoutMinutes.Maximum = new decimal(new int[] { 480, 0, 0, 0 });
            this.numAutoLogoutMinutes.Minimum = new decimal(new int[] { 5, 0, 0, 0 });
            this.numAutoLogoutMinutes.Name = "numAutoLogoutMinutes";
            this.numAutoLogoutMinutes.Size = new Size(80, 24);
            this.numAutoLogoutMinutes.TabIndex = 6;
            this.numAutoLogoutMinutes.Value = new decimal(new int[] { 30, 0, 0, 0 });

            this.chkEnableActivityLog.AutoSize = true;
            this.chkEnableActivityLog.Location = new Point(20, 190);
            this.chkEnableActivityLog.Name = "chkEnableActivityLog";
            this.chkEnableActivityLog.Size = new Size(120, 21);
            this.chkEnableActivityLog.TabIndex = 7;
            this.chkEnableActivityLog.Text = "تفعيل سجل الأنشطة";
            this.chkEnableActivityLog.UseVisualStyleBackColor = true;

            //
            // tabBackup
            //
            this.tabBackup.Controls.Add(this.lblBackupPath);
            this.tabBackup.Controls.Add(this.txtBackupPath);
            this.tabBackup.Controls.Add(this.btnSelectBackupPath);
            this.tabBackup.Controls.Add(this.lblBackupFrequency);
            this.tabBackup.Controls.Add(this.cmbBackupFrequency);
            this.tabBackup.Controls.Add(this.chkAutoBackup);
            this.tabBackup.Controls.Add(this.chkEmailBackup);
            this.tabBackup.Controls.Add(this.lblBackupEmail);
            this.tabBackup.Controls.Add(this.txtBackupEmail);
            this.tabBackup.Location = new Point(4, 29);
            this.tabBackup.Name = "tabBackup";
            this.tabBackup.Padding = new Padding(3);
            this.tabBackup.Size = new Size(792, 417);
            this.tabBackup.TabIndex = 3;
            this.tabBackup.Text = "النسخ الاحتياطي";
            this.tabBackup.UseVisualStyleBackColor = true;

            this.lblBackupPath.AutoSize = true;
            this.lblBackupPath.Location = new Point(20, 30);
            this.lblBackupPath.Name = "lblBackupPath";
            this.lblBackupPath.Size = new Size(100, 17);
            this.lblBackupPath.TabIndex = 0;
            this.lblBackupPath.Text = "مسار النسخ الاحتياطي:";

            this.txtBackupPath.Location = new Point(130, 27);
            this.txtBackupPath.Name = "txtBackupPath";
            this.txtBackupPath.ReadOnly = true;
            this.txtBackupPath.Size = new Size(300, 24);
            this.txtBackupPath.TabIndex = 1;

            this.btnSelectBackupPath.Location = new Point(440, 26);
            this.btnSelectBackupPath.Name = "btnSelectBackupPath";
            this.btnSelectBackupPath.Size = new Size(80, 26);
            this.btnSelectBackupPath.TabIndex = 2;
            this.btnSelectBackupPath.Text = "اختيار";
            this.btnSelectBackupPath.UseVisualStyleBackColor = true;

            this.lblBackupFrequency.AutoSize = true;
            this.lblBackupFrequency.Location = new Point(20, 70);
            this.lblBackupFrequency.Name = "lblBackupFrequency";
            this.lblBackupFrequency.Size = new Size(100, 17);
            this.lblBackupFrequency.TabIndex = 3;
            this.lblBackupFrequency.Text = "تكرار النسخ الاحتياطي:";

            this.cmbBackupFrequency.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbBackupFrequency.Items.AddRange(new object[] { "يومي", "أسبوعي", "شهري" });
            this.cmbBackupFrequency.Location = new Point(130, 67);
            this.cmbBackupFrequency.Name = "cmbBackupFrequency";
            this.cmbBackupFrequency.Size = new Size(150, 25);
            this.cmbBackupFrequency.TabIndex = 4;

            this.chkAutoBackup.AutoSize = true;
            this.chkAutoBackup.Location = new Point(20, 110);
            this.chkAutoBackup.Name = "chkAutoBackup";
            this.chkAutoBackup.Size = new Size(120, 21);
            this.chkAutoBackup.TabIndex = 5;
            this.chkAutoBackup.Text = "نسخ احتياطي تلقائي";
            this.chkAutoBackup.UseVisualStyleBackColor = true;

            this.chkEmailBackup.AutoSize = true;
            this.chkEmailBackup.Location = new Point(20, 150);
            this.chkEmailBackup.Name = "chkEmailBackup";
            this.chkEmailBackup.Size = new Size(150, 21);
            this.chkEmailBackup.TabIndex = 6;
            this.chkEmailBackup.Text = "إرسال النسخة بالبريد الإلكتروني";
            this.chkEmailBackup.UseVisualStyleBackColor = true;

            this.lblBackupEmail.AutoSize = true;
            this.lblBackupEmail.Location = new Point(20, 190);
            this.lblBackupEmail.Name = "lblBackupEmail";
            this.lblBackupEmail.Size = new Size(100, 17);
            this.lblBackupEmail.TabIndex = 7;
            this.lblBackupEmail.Text = "البريد الإلكتروني:";

            this.txtBackupEmail.Location = new Point(130, 187);
            this.txtBackupEmail.Name = "txtBackupEmail";
            this.txtBackupEmail.Size = new Size(250, 24);
            this.txtBackupEmail.TabIndex = 8;

            //
            // tabUI
            //
            this.tabUI.Controls.Add(this.lblTheme);
            this.tabUI.Controls.Add(this.cmbTheme);
            this.tabUI.Controls.Add(this.lblPrimaryColor);
            this.tabUI.Controls.Add(this.btnPrimaryColor);
            this.tabUI.Controls.Add(this.lblSecondaryColor);
            this.tabUI.Controls.Add(this.btnSecondaryColor);
            this.tabUI.Controls.Add(this.lblFontFamily);
            this.tabUI.Controls.Add(this.cmbFontFamily);
            this.tabUI.Controls.Add(this.lblFontSize);
            this.tabUI.Controls.Add(this.numFontSize);
            this.tabUI.Controls.Add(this.chkShowToolbar);
            this.tabUI.Location = new Point(4, 29);
            this.tabUI.Name = "tabUI";
            this.tabUI.Padding = new Padding(3);
            this.tabUI.Size = new Size(792, 417);
            this.tabUI.TabIndex = 4;
            this.tabUI.Text = "واجهة المستخدم";
            this.tabUI.UseVisualStyleBackColor = true;

            this.lblTheme.AutoSize = true;
            this.lblTheme.Location = new Point(20, 30);
            this.lblTheme.Name = "lblTheme";
            this.lblTheme.Size = new Size(50, 17);
            this.lblTheme.TabIndex = 0;
            this.lblTheme.Text = "المظهر:";

            this.cmbTheme.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbTheme.Items.AddRange(new object[] { "فاتح", "داكن", "تلقائي" });
            this.cmbTheme.Location = new Point(80, 27);
            this.cmbTheme.Name = "cmbTheme";
            this.cmbTheme.Size = new Size(150, 25);
            this.cmbTheme.TabIndex = 1;

            this.lblPrimaryColor.AutoSize = true;
            this.lblPrimaryColor.Location = new Point(20, 70);
            this.lblPrimaryColor.Name = "lblPrimaryColor";
            this.lblPrimaryColor.Size = new Size(80, 17);
            this.lblPrimaryColor.TabIndex = 2;
            this.lblPrimaryColor.Text = "اللون الأساسي:";

            this.btnPrimaryColor.BackColor = Color.FromArgb(0, 123, 255);
            this.btnPrimaryColor.Location = new Point(110, 67);
            this.btnPrimaryColor.Name = "btnPrimaryColor";
            this.btnPrimaryColor.Size = new Size(50, 25);
            this.btnPrimaryColor.TabIndex = 3;
            this.btnPrimaryColor.UseVisualStyleBackColor = false;

            this.lblSecondaryColor.AutoSize = true;
            this.lblSecondaryColor.Location = new Point(20, 110);
            this.lblSecondaryColor.Name = "lblSecondaryColor";
            this.lblSecondaryColor.Size = new Size(80, 17);
            this.lblSecondaryColor.TabIndex = 4;
            this.lblSecondaryColor.Text = "اللون الثانوي:";

            this.btnSecondaryColor.BackColor = Color.FromArgb(108, 117, 125);
            this.btnSecondaryColor.Location = new Point(110, 107);
            this.btnSecondaryColor.Name = "btnSecondaryColor";
            this.btnSecondaryColor.Size = new Size(50, 25);
            this.btnSecondaryColor.TabIndex = 5;
            this.btnSecondaryColor.UseVisualStyleBackColor = false;

            this.lblFontFamily.AutoSize = true;
            this.lblFontFamily.Location = new Point(20, 150);
            this.lblFontFamily.Name = "lblFontFamily";
            this.lblFontFamily.Size = new Size(60, 17);
            this.lblFontFamily.TabIndex = 6;
            this.lblFontFamily.Text = "نوع الخط:";

            this.cmbFontFamily.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbFontFamily.Items.AddRange(new object[] { "Tahoma", "Arial", "Segoe UI", "Calibri" });
            this.cmbFontFamily.Location = new Point(90, 147);
            this.cmbFontFamily.Name = "cmbFontFamily";
            this.cmbFontFamily.Size = new Size(150, 25);
            this.cmbFontFamily.TabIndex = 7;

            this.lblFontSize.AutoSize = true;
            this.lblFontSize.Location = new Point(20, 190);
            this.lblFontSize.Name = "lblFontSize";
            this.lblFontSize.Size = new Size(60, 17);
            this.lblFontSize.TabIndex = 8;
            this.lblFontSize.Text = "حجم الخط:";

            this.numFontSize.Location = new Point(90, 187);
            this.numFontSize.Maximum = new decimal(new int[] { 20, 0, 0, 0 });
            this.numFontSize.Minimum = new decimal(new int[] { 8, 0, 0, 0 });
            this.numFontSize.Name = "numFontSize";
            this.numFontSize.Size = new Size(80, 24);
            this.numFontSize.TabIndex = 9;
            this.numFontSize.Value = new decimal(new int[] { 9, 0, 0, 0 });

            this.chkShowToolbar.AutoSize = true;
            this.chkShowToolbar.Location = new Point(20, 230);
            this.chkShowToolbar.Name = "chkShowToolbar";
            this.chkShowToolbar.Size = new Size(120, 21);
            this.chkShowToolbar.TabIndex = 10;
            this.chkShowToolbar.Text = "إظهار شريط الأدوات";
            this.chkShowToolbar.UseVisualStyleBackColor = true;

            //
            // Bottom Buttons
            //
            this.btnSave.Location = new Point(600, 530);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new Size(90, 35);
            this.btnSave.TabIndex = 1;
            this.btnSave.Text = "حفظ";
            this.btnSave.UseVisualStyleBackColor = true;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel.Location = new Point(500, 530);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(90, 35);
            this.btnCancel.TabIndex = 2;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            this.btnResetToDefault.Location = new Point(400, 530);
            this.btnResetToDefault.Name = "btnResetToDefault";
            this.btnResetToDefault.Size = new Size(90, 35);
            this.btnResetToDefault.TabIndex = 3;
            this.btnResetToDefault.Text = "إعادة تعيين";
            this.btnResetToDefault.UseVisualStyleBackColor = true;

            this.btnExportSettings.Location = new Point(300, 530);
            this.btnExportSettings.Name = "btnExportSettings";
            this.btnExportSettings.Size = new Size(90, 35);
            this.btnExportSettings.TabIndex = 4;
            this.btnExportSettings.Text = "تصدير";
            this.btnExportSettings.UseVisualStyleBackColor = true;

            this.btnImportSettings.Location = new Point(200, 530);
            this.btnImportSettings.Name = "btnImportSettings";
            this.btnImportSettings.Size = new Size(90, 35);
            this.btnImportSettings.TabIndex = 5;
            this.btnImportSettings.Text = "استيراد";
            this.btnImportSettings.UseVisualStyleBackColor = true;

            // Initialize ComboBoxes
            this.cmbTheme.Items.AddRange(new object[] { "Default", "Dark", "Light", "Blue" });
            this.cmbFontFamily.Items.AddRange(new object[] { "Tahoma", "Arial", "Calibri", "Times New Roman" });
            this.cmbBackupFrequency.Items.AddRange(new object[] { "كل ساعة", "يومياً", "أسبوعياً", "شهرياً", "يدوي" });

            this.tabControl1.ResumeLayout(false);
            this.tabSystem.ResumeLayout(false);
            this.tabSystem.PerformLayout();
            this.tabPrint.ResumeLayout(false);
            this.tabPrint.PerformLayout();
            this.tabSecurity.ResumeLayout(false);
            this.tabSecurity.PerformLayout();
            this.tabBackup.ResumeLayout(false);
            this.tabBackup.PerformLayout();
            this.tabUI.ResumeLayout(false);
            this.tabUI.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numTaxRate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numCopiesCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMinPasswordLength)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numMaxLoginAttempts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numAutoLogoutMinutes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numFontSize)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl1;
        private TabPage tabSystem;
        private TabPage tabPrint;
        private TabPage tabSecurity;
        private TabPage tabBackup;
        private TabPage tabUI;
        private Button btnSave;
        private Button btnCancel;
        private Button btnResetToDefault;
        private Button btnExportSettings;
        private Button btnImportSettings;

        // System Settings Controls
        private Label lblCompanyName;
        private TextBox txtCompanyName;
        private Label lblCompanyAddress;
        private TextBox txtCompanyAddress;
        private Label lblCompanyPhone;
        private TextBox txtCompanyPhone;
        private Label lblCompanyEmail;
        private TextBox txtCompanyEmail;
        private Label lblTaxNumber;
        private TextBox txtTaxNumber;
        private Label lblTaxRate;
        private NumericUpDown numTaxRate;
        private Label lblCurrency;
        private TextBox txtCurrency;
        private Label lblCurrencySymbol;
        private TextBox txtCurrencySymbol;
        private CheckBox chkRTL;

        // Print Settings Controls
        private Label lblDefaultPrinter;
        private TextBox txtDefaultPrinter;
        private Button btnSelectPrinter;
        private Label lblPrinterType;
        private ComboBox cmbPrinterType;
        private Label lblPaperSize;
        private ComboBox cmbPaperSize;
        private CheckBox chkAutoPrint;
        private CheckBox chkPrintLogo;
        private CheckBox chkPrintHeader;
        private CheckBox chkPrintFooter;
        private Label lblHeaderText;
        private TextBox txtHeaderText;
        private Label lblFooterText;
        private TextBox txtFooterText;
        private Label lblCopiesCount;
        private NumericUpDown numCopiesCount;
        private CheckBox chkPrintBarcode;
        private Label lblLogoPath;
        private TextBox txtLogoPath;
        private Button btnSelectLogo;

        // Security Settings Controls
        private CheckBox chkRequireStrongPassword;
        private Label lblMinPasswordLength;
        private NumericUpDown numMinPasswordLength;
        private Label lblMaxLoginAttempts;
        private NumericUpDown numMaxLoginAttempts;
        private Label lblAutoLogoutMinutes;
        private NumericUpDown numAutoLogoutMinutes;
        private CheckBox chkEnableActivityLog;

        // Backup Settings Controls
        private Label lblBackupPath;
        private TextBox txtBackupPath;
        private Button btnSelectBackupPath;
        private Label lblBackupFrequency;
        private ComboBox cmbBackupFrequency;
        private CheckBox chkAutoBackup;
        private CheckBox chkEmailBackup;
        private Label lblBackupEmail;
        private TextBox txtBackupEmail;

        // UI Settings Controls
        private Label lblTheme;
        private ComboBox cmbTheme;
        private Label lblPrimaryColor;
        private Button btnPrimaryColor;
        private Label lblSecondaryColor;
        private Button btnSecondaryColor;
        private Label lblFontFamily;
        private ComboBox cmbFontFamily;
        private Label lblFontSize;
        private NumericUpDown numFontSize;
        private CheckBox chkShowToolbar;
    }
}
