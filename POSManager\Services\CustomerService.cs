using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;

namespace POSManager.Services
{
    public static class CustomerService
    {
        public static async Task<List<Customer>> GetAllCustomersAsync()
        {
            try
            {
                var customers = new List<Customer>();
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT * FROM Customers 
                    WHERE IsActive = 1 
                    ORDER BY Name";

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var customer = new Customer
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Phone = reader["Phone"]?.ToString() ?? "",
                        Email = reader["Email"]?.ToString() ?? "",
                        Address = reader["Address"]?.ToString() ?? "",
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = Convert.ToDateTime(reader["CreatedAt"])
                    };
                    customers.Add(customer);
                }

                return customers;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب العملاء: {ex.Message}");
            }
        }

        public static async Task<List<Customer>> SearchCustomersAsync(string searchTerm)
        {
            try
            {
                var customers = new List<Customer>();
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = @"
                    SELECT * FROM Customers 
                    WHERE IsActive = 1 
                    AND (Name LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm)
                    ORDER BY Name";

                command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var customer = new Customer
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Phone = reader["Phone"]?.ToString() ?? "",
                        Email = reader["Email"]?.ToString() ?? "",
                        Address = reader["Address"]?.ToString() ?? "",
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = Convert.ToDateTime(reader["CreatedAt"])
                    };
                    customers.Add(customer);
                }

                return customers;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن العملاء: {ex.Message}");
            }
        }

        public static async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT * FROM Customers WHERE Id = @Id AND IsActive = 1";
                command.Parameters.AddWithValue("@Id", id);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new Customer
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Phone = reader["Phone"]?.ToString() ?? "",
                        Email = reader["Email"]?.ToString() ?? "",
                        Address = reader["Address"]?.ToString() ?? "",
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = Convert.ToDateTime(reader["CreatedAt"])
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب العميل: {ex.Message}");
            }
        }

        public static async Task<bool> AddCustomerAsync(Customer customer)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                // التحقق من عدم تكرار الهاتف
                if (!string.IsNullOrWhiteSpace(customer.Phone))
                {
                    var checkCommand = connection.CreateCommand();
                    checkCommand.CommandText = "SELECT COUNT(*) FROM Customers WHERE Phone = @Phone AND IsActive = 1";
                    checkCommand.Parameters.AddWithValue("@Phone", customer.Phone);
                    var phoneExists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;
                    
                    if (phoneExists)
                    {
                        throw new Exception("رقم الهاتف مسجل مسبقاً");
                    }
                }

                var command = connection.CreateCommand();
                command.CommandText = @"
                    INSERT INTO Customers (Name, Phone, Email, Address, IsActive, CreatedAt)
                    VALUES (@Name, @Phone, @Email, @Address, @IsActive, @CreatedAt)";

                command.Parameters.AddWithValue("@Name", customer.Name);
                command.Parameters.AddWithValue("@Phone", customer.Phone ?? "");
                command.Parameters.AddWithValue("@Email", customer.Email ?? "");
                command.Parameters.AddWithValue("@Address", customer.Address ?? "");
                command.Parameters.AddWithValue("@IsActive", customer.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة العميل: {ex.Message}");
            }
        }

        public static async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                // التحقق من عدم تكرار الهاتف مع عملاء آخرين
                if (!string.IsNullOrWhiteSpace(customer.Phone))
                {
                    var checkCommand = connection.CreateCommand();
                    checkCommand.CommandText = "SELECT COUNT(*) FROM Customers WHERE Phone = @Phone AND Id != @Id AND IsActive = 1";
                    checkCommand.Parameters.AddWithValue("@Phone", customer.Phone);
                    checkCommand.Parameters.AddWithValue("@Id", customer.Id);
                    var phoneExists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;
                    
                    if (phoneExists)
                    {
                        throw new Exception("رقم الهاتف مسجل مسبقاً لعميل آخر");
                    }
                }

                var command = connection.CreateCommand();
                command.CommandText = @"
                    UPDATE Customers 
                    SET Name = @Name, Phone = @Phone, Email = @Email, Address = @Address, IsActive = @IsActive
                    WHERE Id = @Id";

                command.Parameters.AddWithValue("@Id", customer.Id);
                command.Parameters.AddWithValue("@Name", customer.Name);
                command.Parameters.AddWithValue("@Phone", customer.Phone ?? "");
                command.Parameters.AddWithValue("@Email", customer.Email ?? "");
                command.Parameters.AddWithValue("@Address", customer.Address ?? "");
                command.Parameters.AddWithValue("@IsActive", customer.IsActive ? 1 : 0);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث العميل: {ex.Message}");
            }
        }

        public static async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                // التحقق من وجود مبيعات مرتبطة بالعميل
                var checkCommand = connection.CreateCommand();
                checkCommand.CommandText = "SELECT COUNT(*) FROM Sales WHERE CustomerId = @CustomerId";
                checkCommand.Parameters.AddWithValue("@CustomerId", id);
                var salesCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());

                if (salesCount > 0)
                {
                    // إلغاء تفعيل العميل بدلاً من الحذف
                    var deactivateCommand = connection.CreateCommand();
                    deactivateCommand.CommandText = "UPDATE Customers SET IsActive = 0 WHERE Id = @Id";
                    deactivateCommand.Parameters.AddWithValue("@Id", id);
                    var result = await deactivateCommand.ExecuteNonQueryAsync();
                    return result > 0;
                }
                else
                {
                    // حذف العميل نهائياً
                    var deleteCommand = connection.CreateCommand();
                    deleteCommand.CommandText = "DELETE FROM Customers WHERE Id = @Id";
                    deleteCommand.Parameters.AddWithValue("@Id", id);
                    var result = await deleteCommand.ExecuteNonQueryAsync();
                    return result > 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف العميل: {ex.Message}");
            }
        }

        public static async Task<Customer?> GetCustomerByPhoneAsync(string phone)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phone)) return null;

                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT * FROM Customers WHERE Phone = @Phone AND IsActive = 1";
                command.Parameters.AddWithValue("@Phone", phone);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new Customer
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Phone = reader["Phone"]?.ToString() ?? "",
                        Email = reader["Email"]?.ToString() ?? "",
                        Address = reader["Address"]?.ToString() ?? "",
                        IsActive = Convert.ToInt64(reader["IsActive"]) == 1,
                        CreatedAt = Convert.ToDateTime(reader["CreatedAt"])
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن العميل بالهاتف: {ex.Message}");
            }
        }
    }
}
