using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class POSForm : Form
    {
        private readonly User _currentUser;
        private List<Product> _products = new List<Product>();
        private List<SaleDetail> _cartItems = new List<SaleDetail>();
        private Customer? _selectedCustomer;
        private decimal _taxPercentage = 15; // الضريبة الافتراضية
        private decimal _discountPercentage = 0;
        private decimal _discountAmount = 0;

        public POSForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            InitializeForm();
        }

        private async void InitializeForm()
        {
            try
            {
                // تحميل المنتجات
                await LoadProductsAsync();
                
                // تحميل إعدادات الضريبة
                await LoadTaxSettingsAsync();
                
                // إعداد الواجهة
                SetupUI();
                
                // تحديث الإحصائيات
                UpdateCartSummary();
                
                // تركيز على حقل البحث
                txtProductSearch.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadProductsAsync()
        {
            _products = await ProductService.GetAllProductsAsync();
            
            // تصفية المنتجات النشطة والمتوفرة في المخزون
            var availableProducts = _products.Where(p => p.IsActive && p.Stock > 0).ToList();
            
            // عرض المنتجات في الشبكة
            dgvProducts.DataSource = availableProducts;
            
            // إعداد أعمدة الشبكة
            SetupProductsGrid();
        }

        private void SetupProductsGrid()
        {
            if (dgvProducts.Columns.Count > 0)
            {
                dgvProducts.Columns["Id"].Visible = false;
                dgvProducts.Columns["CategoryId"].Visible = false;
                dgvProducts.Columns["PurchasePrice"].Visible = false;
                dgvProducts.Columns["IsActive"].Visible = false;
                dgvProducts.Columns["CreatedAt"].Visible = false;
                
                dgvProducts.Columns["Name"].HeaderText = "اسم المنتج";
                dgvProducts.Columns["Barcode"].HeaderText = "الباركود";
                dgvProducts.Columns["Barcode"].HeaderText = "الباركود";
                dgvProducts.Columns["SalePrice"].HeaderText = "السعر";
                dgvProducts.Columns["Stock"].HeaderText = "المخزون";
                dgvProducts.Columns["Unit"].HeaderText = "الوحدة";
                dgvProducts.Columns["CategoryName"].HeaderText = "الفئة";
                
                // تنسيق العملة
                dgvProducts.Columns["SalePrice"].DefaultCellStyle.Format = "C2";
                dgvProducts.Columns["SalePrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                
                // تلوين الصفوف حسب المخزون
                dgvProducts.CellFormatting += (s, e) =>
                {
                    if (e.RowIndex >= 0 && e.RowIndex < _products.Count)
                    {
                        var product = (Product)dgvProducts.Rows[e.RowIndex].DataBoundItem;
                        if (product.IsLowStock)
                        {
                            e.CellStyle.BackColor = Color.LightYellow;
                        }
                    }
                };
            }
        }

        private void SetupCartGrid()
        {
            dgvCart.AutoGenerateColumns = false;
            dgvCart.Columns.Clear();

            dgvCart.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "المنتج",
                DataPropertyName = "ProductName",
                Width = 200,
                ReadOnly = true
            });

            dgvCart.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "السعر",
                DataPropertyName = "UnitPrice",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2", Alignment = DataGridViewContentAlignment.MiddleRight },
                ReadOnly = true
            });

            dgvCart.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dgvCart.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalPrice",
                HeaderText = "الإجمالي",
                DataPropertyName = "TotalPrice",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2", Alignment = DataGridViewContentAlignment.MiddleRight },
                ReadOnly = true
            });

            dgvCart.Columns.Add(new DataGridViewButtonColumn
            {
                Name = "Remove",
                HeaderText = "حذف",
                Text = "حذف",
                UseColumnTextForButtonValue = true,
                Width = 60
            });

            dgvCart.DataSource = _cartItems;
        }

        private async Task LoadTaxSettingsAsync()
        {
            try
            {
                using var connection = new Microsoft.Data.Sqlite.SqliteConnection(POSManager.Data.DatabaseManager.GetConnectionString());
                await connection.OpenAsync();

                var command = connection.CreateCommand();
                command.CommandText = "SELECT Value FROM Settings WHERE Key = 'TaxPercentage'";
                var result = await command.ExecuteScalarAsync();
                
                if (result != null && decimal.TryParse(result.ToString(), out decimal tax))
                {
                    _taxPercentage = tax;
                }
            }
            catch
            {
                _taxPercentage = 15; // القيمة الافتراضية
            }
        }

        private void SetupUI()
        {
            // إعداد شبكة السلة
            SetupCartGrid();
            
            // إعداد أحداث البحث
            txtProductSearch.TextChanged += TxtProductSearch_TextChanged;
            txtProductSearch.KeyDown += TxtProductSearch_KeyDown;
            
            // إعداد أحداث الشبكات
            dgvProducts.CellDoubleClick += DgvProducts_CellDoubleClick;
            dgvCart.CellClick += DgvCart_CellClick;
            dgvCart.CellValueChanged += DgvCart_CellValueChanged;
            
            // إعداد أحداث الخصم
            txtDiscountPercentage.TextChanged += TxtDiscount_TextChanged;
            txtDiscountAmount.TextChanged += TxtDiscount_TextChanged;
            
            // إعداد أحداث العميل
            btnSelectCustomer.Click += BtnSelectCustomer_Click;
            btnClearCustomer.Click += BtnClearCustomer_Click;
            
            // إعداد أحداث الدفع
            btnProcessSale.Click += BtnProcessSale_Click;
            btnClearCart.Click += BtnClearCart_Click;
            
            // تحديث حالة الأزرار
            UpdateButtonStates();
        }

        private async void TxtProductSearch_TextChanged(object? sender, EventArgs e)
        {
            await SearchProducts();
        }

        private async void TxtProductSearch_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                await AddProductBySearchAsync();
            }
        }

        private async Task SearchProducts()
        {
            try
            {
                var searchTerm = txtProductSearch.Text.Trim();
                
                if (string.IsNullOrEmpty(searchTerm))
                {
                    var availableProducts = _products.Where(p => p.IsActive && p.Stock > 0).ToList();
                    dgvProducts.DataSource = availableProducts;
                }
                else
                {
                    var filteredProducts = _products.Where(p => 
                        p.IsActive && 
                        p.Stock > 0 && 
                        (p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                         p.Barcode?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                         p.Barcode?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)
                    ).ToList();
                    
                    dgvProducts.DataSource = filteredProducts;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task AddProductBySearchAsync()
        {
            try
            {
                var searchTerm = txtProductSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchTerm)) return;

                // البحث بالباركود أو الكود
                var product = _products.FirstOrDefault(p => 
                    p.IsActive && 
                    p.Stock > 0 && 
                    (p.Barcode == searchTerm));

                if (product != null)
                {
                    AddProductToCart(product);
                    txtProductSearch.Clear();
                    txtProductSearch.Focus();
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DgvProducts_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var product = (Product)dgvProducts.Rows[e.RowIndex].DataBoundItem;
                AddProductToCart(product);
            }
        }

        private void AddProductToCart(Product product)
        {
            try
            {
                // التحقق من توفر المنتج في المخزون
                if (product.Stock <= 0)
                {
                    MessageBox.Show("المنتج غير متوفر في المخزون", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // البحث عن المنتج في السلة
                var existingItem = _cartItems.FirstOrDefault(item => item.ProductId == product.Id);
                
                if (existingItem != null)
                {
                    // التحقق من عدم تجاوز المخزون المتاح
                    if (existingItem.Quantity >= product.Stock)
                    {
                        MessageBox.Show($"لا يمكن إضافة أكثر من {product.Stock} وحدة من هذا المنتج", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    
                    // زيادة الكمية
                    existingItem.Quantity++;
                    existingItem.TotalPrice = existingItem.Quantity * existingItem.UnitPrice;
                }
                else
                {
                    // إضافة منتج جديد للسلة
                    var cartItem = new SaleDetail
                    {
                        ProductId = product.Id,
                        ProductName = product.Name,
                        ProductCode = product.Barcode ?? "",
                        Quantity = 1,
                        UnitPrice = product.SalePrice,
                        TotalPrice = product.SalePrice,
                        Product = product
                    };
                    
                    _cartItems.Add(cartItem);
                }

                // تحديث الشبكة والملخص
                RefreshCartGrid();
                UpdateCartSummary();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج للسلة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshCartGrid()
        {
            dgvCart.DataSource = null;
            dgvCart.DataSource = _cartItems;
        }

        private void UpdateCartSummary()
        {
            try
            {
                var subtotal = _cartItems.Sum(item => item.TotalPrice);
                var discountAmount = CalculateDiscountAmount(subtotal);
                var taxableAmount = subtotal - discountAmount;
                var taxAmount = taxableAmount * (_taxPercentage / 100);
                var total = taxableAmount + taxAmount;

                lblSubtotal.Text = $"المجموع الفرعي: {subtotal:C2}";
                lblDiscount.Text = $"الخصم: {discountAmount:C2}";
                lblTax.Text = $"الضريبة ({_taxPercentage}%): {taxAmount:C2}";
                lblTotal.Text = $"الإجمالي: {total:C2}";
                
                // تحديث عدد الأصناف
                lblItemCount.Text = $"عدد الأصناف: {_cartItems.Count}";
                lblTotalQuantity.Text = $"إجمالي الكمية: {_cartItems.Sum(item => item.Quantity)}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب الملخص: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculateDiscountAmount(decimal subtotal)
        {
            if (_discountAmount > 0)
            {
                return Math.Min(_discountAmount, subtotal);
            }
            else if (_discountPercentage > 0)
            {
                return subtotal * (_discountPercentage / 100);
            }
            return 0;
        }

        private void UpdateButtonStates()
        {
            var hasItems = _cartItems.Count > 0;
            btnProcessSale.Enabled = hasItems;
            btnClearCart.Enabled = hasItems;
        }

        private void DgvCart_CellClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var columnName = dgvCart.Columns[e.ColumnIndex].Name;

                if (columnName == "Remove")
                {
                    RemoveItemFromCart(e.RowIndex);
                }
            }
        }

        private void DgvCart_CellValueChanged(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var columnName = dgvCart.Columns[e.ColumnIndex].Name;

                if (columnName == "Quantity")
                {
                    UpdateCartItemQuantity(e.RowIndex);
                }
            }
        }

        private void RemoveItemFromCart(int rowIndex)
        {
            try
            {
                if (rowIndex >= 0 && rowIndex < _cartItems.Count)
                {
                    var item = _cartItems[rowIndex];
                    var result = MessageBox.Show($"هل تريد حذف '{item.ProductName}' من السلة؟",
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        _cartItems.RemoveAt(rowIndex);
                        RefreshCartGrid();
                        UpdateCartSummary();
                        UpdateButtonStates();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateCartItemQuantity(int rowIndex)
        {
            try
            {
                if (rowIndex >= 0 && rowIndex < _cartItems.Count)
                {
                    var item = _cartItems[rowIndex];
                    var cell = dgvCart.Rows[rowIndex].Cells["Quantity"];

                    if (int.TryParse(cell.Value?.ToString(), out int newQuantity))
                    {
                        if (newQuantity <= 0)
                        {
                            RemoveItemFromCart(rowIndex);
                            return;
                        }

                        // التحقق من المخزون المتاح
                        var product = _products.FirstOrDefault(p => p.Id == item.ProductId);
                        if (product != null && newQuantity > product.Stock)
                        {
                            MessageBox.Show($"الكمية المطلوبة ({newQuantity}) أكبر من المخزون المتاح ({product.Stock})",
                                "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            cell.Value = item.Quantity; // إرجاع القيمة السابقة
                            return;
                        }

                        item.Quantity = newQuantity;
                        item.TotalPrice = item.Quantity * item.UnitPrice;

                        RefreshCartGrid();
                        UpdateCartSummary();
                    }
                    else
                    {
                        cell.Value = item.Quantity; // إرجاع القيمة السابقة
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الكمية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TxtDiscount_TextChanged(object? sender, EventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (textBox == null) return;

                if (textBox == txtDiscountPercentage)
                {
                    if (decimal.TryParse(txtDiscountPercentage.Text, out decimal percentage))
                    {
                        _discountPercentage = Math.Max(0, Math.Min(100, percentage));
                        _discountAmount = 0;
                        txtDiscountAmount.Text = "";
                    }
                    else
                    {
                        _discountPercentage = 0;
                    }
                }
                else if (textBox == txtDiscountAmount)
                {
                    if (decimal.TryParse(txtDiscountAmount.Text, out decimal amount))
                    {
                        _discountAmount = Math.Max(0, amount);
                        _discountPercentage = 0;
                        txtDiscountPercentage.Text = "";
                    }
                    else
                    {
                        _discountAmount = 0;
                    }
                }

                UpdateCartSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب الخصم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnSelectCustomer_Click(object? sender, EventArgs e)
        {
            try
            {
                using var customerForm = new CustomerSelectionForm();
                if (customerForm.ShowDialog() == DialogResult.OK)
                {
                    _selectedCustomer = customerForm.SelectedCustomer;
                    if (_selectedCustomer != null)
                    {
                        lblCustomer.Text = $"العميل: {_selectedCustomer.Name}";
                        btnClearCustomer.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClearCustomer_Click(object? sender, EventArgs e)
        {
            _selectedCustomer = null;
            lblCustomer.Text = "العميل: غير محدد";
            btnClearCustomer.Visible = false;
        }

        private async void BtnProcessSale_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_cartItems.Count == 0)
                {
                    MessageBox.Show("السلة فارغة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // فتح نافذة الدفع
                using var paymentForm = new PaymentForm(CalculateTotalAmount());
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    await ProcessSaleAsync(paymentForm.PaymentMethod, paymentForm.PaidAmount, paymentForm.Notes);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة البيع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculateTotalAmount()
        {
            var subtotal = _cartItems.Sum(item => item.TotalPrice);
            var discountAmount = CalculateDiscountAmount(subtotal);
            var taxableAmount = subtotal - discountAmount;
            var taxAmount = taxableAmount * (_taxPercentage / 100);
            return taxableAmount + taxAmount;
        }

        private async Task ProcessSaleAsync(PaymentMethod paymentMethod, decimal paidAmount, string notes)
        {
            try
            {
                var subtotal = _cartItems.Sum(item => item.TotalPrice);
                var discountAmount = CalculateDiscountAmount(subtotal);
                var taxableAmount = subtotal - discountAmount;
                var taxAmount = taxableAmount * (_taxPercentage / 100);
                var totalAmount = taxableAmount + taxAmount;
                var changeAmount = paidAmount - totalAmount;

                var sale = new Sale
                {
                    InvoiceNumber = await SaleService.GenerateInvoiceNumberAsync(),
                    CustomerId = _selectedCustomer?.Id,
                    UserId = _currentUser.Id,
                    SubTotal = subtotal,
                    DiscountAmount = discountAmount,
                    DiscountPercentage = _discountPercentage,
                    TaxAmount = taxAmount,
                    TaxPercentage = _taxPercentage,
                    TotalAmount = totalAmount,
                    PaidAmount = paidAmount,
                    ChangeAmount = changeAmount,
                    PaymentMethod = paymentMethod,
                    Notes = notes,
                    SaleDate = DateTime.Now,
                    SaleDetails = _cartItems.ToList()
                };

                var success = await SaleService.CreateSaleAsync(sale);

                if (success)
                {
                    MessageBox.Show($"تم حفظ الفاتورة بنجاح\nرقم الفاتورة: {sale.InvoiceNumber}\nالباقي: {changeAmount:C2}",
                        "نجح البيع", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // طباعة الفاتورة (اختياري)
                    var printResult = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (printResult == DialogResult.Yes)
                    {
                        // TODO: إضافة وظيفة الطباعة
                    }

                    // مسح السلة وإعادة تحميل المنتجات
                    ClearCart();
                    await LoadProductsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClearCart_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح جميع المنتجات من السلة؟",
                "تأكيد المسح", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                ClearCart();
            }
        }

        private void ClearCart()
        {
            _cartItems.Clear();
            _selectedCustomer = null;
            _discountAmount = 0;
            _discountPercentage = 0;

            RefreshCartGrid();
            UpdateCartSummary();
            UpdateButtonStates();

            lblCustomer.Text = "العميل: غير محدد";
            btnClearCustomer.Visible = false;
            txtDiscountAmount.Text = "";
            txtDiscountPercentage.Text = "";
            txtProductSearch.Focus();
        }
    }
}
