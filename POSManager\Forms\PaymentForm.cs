using POSManager.Models;

namespace POSManager.Forms
{
    public partial class PaymentForm : Form
    {
        private readonly decimal _totalAmount;
        public PaymentMethod PaymentMethod { get; private set; }
        public decimal PaidAmount { get; private set; }
        public string Notes { get; private set; } = string.Empty;

        public PaymentForm(decimal totalAmount)
        {
            InitializeComponent();
            _totalAmount = totalAmount;
            InitializeForm();
        }

        private void InitializeForm()
        {
            lblTotalAmount.Text = $"المبلغ المطلوب: {_totalAmount:C2}";
            
            // إعداد طرق الدفع
            cmbPaymentMethod.Items.Clear();
            cmbPaymentMethod.Items.Add(new { Text = "نقدي", Value = PaymentMethod.Cash });
            cmbPaymentMethod.Items.Add(new { Text = "بطاقة ائتمان", Value = PaymentMethod.Card });
            cmbPaymentMethod.Items.Add(new { Text = "تحويل بنكي", Value = PaymentMethod.Transfer });
            cmbPaymentMethod.Items.Add(new { Text = "مختلط", Value = PaymentMethod.Mixed });
            
            cmbPaymentMethod.DisplayMember = "Text";
            cmbPaymentMethod.ValueMember = "Value";
            cmbPaymentMethod.SelectedIndex = 0; // نقدي كافتراضي
            
            txtPaidAmount.Text = _totalAmount.ToString("F2");
            UpdateChangeAmount();
            
            txtPaidAmount.Focus();
            txtPaidAmount.SelectAll();
        }

        private void txtPaidAmount_TextChanged(object sender, EventArgs e)
        {
            UpdateChangeAmount();
        }

        private void UpdateChangeAmount()
        {
            if (decimal.TryParse(txtPaidAmount.Text, out decimal paidAmount))
            {
                var changeAmount = paidAmount - _totalAmount;
                lblChangeAmount.Text = $"الباقي: {changeAmount:C2}";
                
                if (changeAmount < 0)
                {
                    lblChangeAmount.ForeColor = Color.Red;
                    btnConfirm.Enabled = false;
                }
                else
                {
                    lblChangeAmount.ForeColor = Color.Green;
                    btnConfirm.Enabled = true;
                }
            }
            else
            {
                lblChangeAmount.Text = "الباقي: 0.00";
                lblChangeAmount.ForeColor = Color.Black;
                btnConfirm.Enabled = false;
            }
        }

        private void btnConfirm_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedItem = (dynamic)cmbPaymentMethod.SelectedItem;
                PaymentMethod = (PaymentMethod)selectedItem.Value;
                PaidAmount = decimal.Parse(txtPaidAmount.Text);
                Notes = txtNotes.Text.Trim();

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة الدفع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من طريقة الدفع
            if (cmbPaymentMethod.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentMethod.Focus();
                return false;
            }

            // التحقق من المبلغ المدفوع
            if (!decimal.TryParse(txtPaidAmount.Text, out decimal paidAmount))
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPaidAmount.Focus();
                return false;
            }

            if (paidAmount < _totalAmount)
            {
                MessageBox.Show("المبلغ المدفوع أقل من المطلوب", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPaidAmount.Focus();
                return false;
            }

            return true;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtPaidAmount_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                if (btnConfirm.Enabled)
                {
                    btnConfirm_Click(sender, e);
                }
            }
        }

        private void txtPaidAmount_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح فقط بالأرقام والفاصلة العشرية
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && 
                e.KeyChar != '.' && e.KeyChar != ',')
            {
                e.Handled = true;
            }

            // السماح بفاصلة عشرية واحدة فقط
            if ((e.KeyChar == '.' || e.KeyChar == ',') && 
                (txtPaidAmount.Text.Contains('.') || txtPaidAmount.Text.Contains(',')))
            {
                e.Handled = true;
            }
        }

        private void btnQuickAmount_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            if (button != null && decimal.TryParse(button.Tag?.ToString(), out decimal amount))
            {
                txtPaidAmount.Text = (_totalAmount + amount).ToString("F2");
                UpdateChangeAmount();
            }
        }

        private void btnExactAmount_Click(object sender, EventArgs e)
        {
            txtPaidAmount.Text = _totalAmount.ToString("F2");
            UpdateChangeAmount();
        }

        private void cmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            // يمكن إضافة منطق خاص بكل طريقة دفع هنا إذا لزم الأمر
        }
    }
}
