using ShopManagementSystem.Models;

namespace ShopManagementSystem.Forms
{
    public partial class ProductAddEditForm : Form
    {
        private readonly List<Category> _categories;
        private readonly bool _isEditMode;
        public Product Product { get; private set; }

        public ProductAddEditForm(List<Category> categories, Product? product = null)
        {
            InitializeComponent();
            _categories = categories;
            _isEditMode = product != null;
            Product = product ?? new Product();
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            // تحديد عنوان النافذة
            this.Text = _isEditMode ? "تعديل منتج" : "إضافة منتج جديد";
            btnSave.Text = _isEditMode ? "حفظ التعديلات" : "إضافة المنتج";

            // تحميل التصنيفات
            LoadCategories();

            // إذا كان في وضع التعديل، تحميل بيانات المنتج
            if (_isEditMode)
            {
                LoadProductData();
            }

            // تركيز على حقل الاسم
            txtName.Focus();
        }

        private void LoadCategories()
        {
            cmbCategory.Items.Clear();
            cmbCategory.Items.Add(new { Id = 0, Name = "-- اختر التصنيف --" });
            
            foreach (var category in _categories)
            {
                cmbCategory.Items.Add(new { Id = category.Id, Name = category.Name });
            }
            
            cmbCategory.DisplayMember = "Name";
            cmbCategory.ValueMember = "Id";
            cmbCategory.SelectedIndex = 0;
        }

        private void LoadProductData()
        {
            txtName.Text = Product.Name;
            txtCode.Text = Product.Code;
            txtBarcode.Text = Product.Barcode;
            nudPrice.Value = Product.Price;
            nudQuantity.Value = Product.Quantity;
            txtDescription.Text = Product.Description;

            // تحديد التصنيف
            for (int i = 0; i < cmbCategory.Items.Count; i++)
            {
                dynamic item = cmbCategory.Items[i];
                if (item.Id == Product.CategoryId)
                {
                    cmbCategory.SelectedIndex = i;
                    break;
                }
            }
        }

        private bool ValidateInput()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من السعر
            if (nudPrice.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صحيح للمنتج", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudPrice.Focus();
                return false;
            }

            // التحقق من الكمية
            if (nudQuantity.Value < 0)
            {
                MessageBox.Show("الكمية لا يمكن أن تكون سالبة", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nudQuantity.Focus();
                return false;
            }

            return true;
        }

        private void SaveProductData()
        {
            Product.Name = txtName.Text.Trim();
            Product.Code = txtCode.Text.Trim();
            Product.Barcode = txtBarcode.Text.Trim();
            Product.Price = nudPrice.Value;
            Product.Quantity = (int)nudQuantity.Value;
            Product.Description = txtDescription.Text.Trim();
            
            // التصنيف
            if (cmbCategory.SelectedValue != null && Convert.ToInt32(cmbCategory.SelectedValue) > 0)
            {
                Product.CategoryId = Convert.ToInt32(cmbCategory.SelectedValue);
            }
            else
            {
                Product.CategoryId = 0;
            }

            if (_isEditMode)
            {
                Product.UpdatedAt = DateTime.Now;
            }
            else
            {
                Product.CreatedAt = DateTime.Now;
                Product.UpdatedAt = DateTime.Now;
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveProductData();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtCode.Focus();
            }
        }

        private void txtCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtBarcode.Focus();
            }
        }

        private void txtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                nudPrice.Focus();
            }
        }

        private void nudPrice_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                nudQuantity.Focus();
            }
        }

        private void nudQuantity_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                cmbCategory.Focus();
            }
        }

        private void cmbCategory_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtDescription.Focus();
            }
        }

        private void txtDescription_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && e.Control)
            {
                btnSave_Click(sender, e);
            }
        }

        private void ProductAddEditForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                btnCancel_Click(sender, e);
            }
            else if (e.KeyCode == Keys.F2)
            {
                btnSave_Click(sender, e);
            }
        }
    }
}
