using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using POSManager.Models;
using POSManager.Data;

namespace POSManager.Services
{
    public class AdvancedInventoryService
    {
        private readonly DatabaseManager _dbManager;
        private static AdvancedInventoryService? _instance;
        private static readonly object _lock = new object();

        private AdvancedInventoryService()
        {
            _dbManager = DatabaseManager.Instance;
        }

        public static AdvancedInventoryService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new AdvancedInventoryService();
                    }
                }
                return _instance;
            }
        }

        #region Inventory Movement Methods

        // إضافة حركة مخزون
        public async Task<bool> AddInventoryMovementAsync(InventoryMovement movement)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();
                try
                {
                    // إدراج حركة المخزون
                    var insertSql = @"
                        INSERT INTO InventoryMovements 
                        (ProductId, MovementType, Quantity, UnitCost, BalanceAfter, Notes, 
                         ReferenceNumber, ReferenceId, MovementDate, UserId, CreatedAt)
                        VALUES 
                        (@ProductId, @MovementType, @Quantity, @UnitCost, @BalanceAfter, @Notes,
                         @ReferenceNumber, @ReferenceId, @MovementDate, @UserId, @CreatedAt)";

                    using var command = new SqliteCommand(insertSql, connection, transaction);
                    command.Parameters.AddWithValue("@ProductId", movement.ProductId);
                    command.Parameters.AddWithValue("@MovementType", (int)movement.MovementType);
                    command.Parameters.AddWithValue("@Quantity", movement.Quantity);
                    command.Parameters.AddWithValue("@UnitCost", movement.UnitCost);
                    command.Parameters.AddWithValue("@BalanceAfter", movement.BalanceAfter);
                    command.Parameters.AddWithValue("@Notes", movement.Notes ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ReferenceNumber", movement.ReferenceNumber ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ReferenceId", movement.ReferenceId ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@MovementDate", movement.MovementDate);
                    command.Parameters.AddWithValue("@UserId", movement.UserId);
                    command.Parameters.AddWithValue("@CreatedAt", movement.CreatedAt);

                    await command.ExecuteNonQueryAsync();

                    // تحديث رصيد المنتج
                    await UpdateProductStockAsync(movement.ProductId, movement.BalanceAfter, connection, transaction);

                    // التحقق من التنبيهات
                    await CheckAndCreateAlertsAsync(movement.ProductId, connection, transaction);

                    await transaction.CommitAsync();
                    return true;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        // حساب الرصيد الجديد بعد الحركة
        public async Task<decimal> CalculateNewBalanceAsync(int productId, InventoryMovementType movementType, decimal quantity)
        {
            try
            {
                var currentStock = await GetCurrentStockAsync(productId);
                
                return movementType switch
                {
                    InventoryMovementType.Purchase => currentStock + quantity,
                    InventoryMovementType.Return => currentStock + quantity,
                    InventoryMovementType.Found => currentStock + quantity,
                    InventoryMovementType.Production => currentStock + quantity,
                    InventoryMovementType.Sale => currentStock - quantity,
                    InventoryMovementType.Damage => currentStock - quantity,
                    InventoryMovementType.Loss => currentStock - quantity,
                    InventoryMovementType.Consumption => currentStock - quantity,
                    InventoryMovementType.Adjustment => quantity, // الكمية المطلقة
                    InventoryMovementType.Transfer => currentStock - quantity,
                    _ => currentStock
                };
            }
            catch
            {
                return 0;
            }
        }

        // الحصول على الرصيد الحالي
        public async Task<decimal> GetCurrentStockAsync(int productId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "SELECT StockQuantity FROM Products WHERE Id = @ProductId";
                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@ProductId", productId);

                var result = await command.ExecuteScalarAsync();
                return result != null ? Convert.ToDecimal(result) : 0;
            }
            catch
            {
                return 0;
            }
        }

        // تحديث رصيد المنتج
        private async Task UpdateProductStockAsync(int productId, decimal newStock, SqliteConnection connection, SqliteTransaction transaction)
        {
            var sql = "UPDATE Products SET StockQuantity = @Stock WHERE Id = @ProductId";
            using var command = new SqliteCommand(sql, connection, transaction);
            command.Parameters.AddWithValue("@Stock", newStock);
            command.Parameters.AddWithValue("@ProductId", productId);
            await command.ExecuteNonQueryAsync();
        }

        // الحصول على حركات المخزون
        public async Task<List<InventoryMovement>> GetInventoryMovementsAsync(int? productId = null, DateTime? fromDate = null, DateTime? toDate = null, int limit = 100)
        {
            var movements = new List<InventoryMovement>();

            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT im.*, p.Name as ProductName, p.Code as ProductCode, u.FullName as UserName
                    FROM InventoryMovements im
                    LEFT JOIN Products p ON im.ProductId = p.Id
                    LEFT JOIN Users u ON im.UserId = u.Id
                    WHERE 1=1";

                var parameters = new List<SqliteParameter>();

                if (productId.HasValue)
                {
                    sql += " AND im.ProductId = @ProductId";
                    parameters.Add(new SqliteParameter("@ProductId", productId.Value));
                }

                if (fromDate.HasValue)
                {
                    sql += " AND im.MovementDate >= @FromDate";
                    parameters.Add(new SqliteParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    sql += " AND im.MovementDate <= @ToDate";
                    parameters.Add(new SqliteParameter("@ToDate", toDate.Value));
                }

                sql += " ORDER BY im.MovementDate DESC, im.Id DESC LIMIT @Limit";
                parameters.Add(new SqliteParameter("@Limit", limit));

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddRange(parameters.ToArray());

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    movements.Add(new InventoryMovement
                    {
                        Id = Convert.ToInt32(reader[0]),
                        ProductId = Convert.ToInt32(reader[1]),
                        MovementType = (InventoryMovementType)Convert.ToInt32(reader[2]),
                        Quantity = Convert.ToDecimal(reader[3]),
                        UnitCost = Convert.ToDecimal(reader[4]),
                        BalanceAfter = Convert.ToDecimal(reader[5]),
                        Notes = Convert.ToString(reader[6]),
                        ReferenceNumber = Convert.ToString(reader[7]),
                        ReferenceId = reader[8] != DBNull.Value ? Convert.ToInt32(reader[8]) : null,
                        MovementDate = Convert.ToDateTime(reader[9]),
                        UserId = Convert.ToInt32(reader[10]),
                        CreatedAt = Convert.ToDateTime(reader[11]),
                        ProductName = Convert.ToString(reader[12]) ?? "",
                        ProductCode = Convert.ToString(reader[13]) ?? "",
                        UserName = Convert.ToString(reader[14]) ?? ""
                    });
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return movements;
        }

        // إنشاء حركة مخزون للبيع
        public async Task<bool> CreateSaleMovementAsync(int productId, decimal quantity, decimal unitCost, int saleId, int userId)
        {
            var newBalance = await CalculateNewBalanceAsync(productId, InventoryMovementType.Sale, quantity);
            
            var movement = new InventoryMovement
            {
                ProductId = productId,
                MovementType = InventoryMovementType.Sale,
                Quantity = quantity,
                UnitCost = unitCost,
                BalanceAfter = newBalance,
                ReferenceNumber = $"SALE-{saleId}",
                ReferenceId = saleId,
                MovementDate = DateTime.Now,
                UserId = userId,
                Notes = "بيع منتج"
            };

            return await AddInventoryMovementAsync(movement);
        }

        // إنشاء حركة مخزون للشراء
        public async Task<bool> CreatePurchaseMovementAsync(int productId, decimal quantity, decimal unitCost, int purchaseOrderId, int userId)
        {
            var newBalance = await CalculateNewBalanceAsync(productId, InventoryMovementType.Purchase, quantity);
            
            var movement = new InventoryMovement
            {
                ProductId = productId,
                MovementType = InventoryMovementType.Purchase,
                Quantity = quantity,
                UnitCost = unitCost,
                BalanceAfter = newBalance,
                ReferenceNumber = $"PO-{purchaseOrderId}",
                ReferenceId = purchaseOrderId,
                MovementDate = DateTime.Now,
                UserId = userId,
                Notes = "شراء منتج"
            };

            return await AddInventoryMovementAsync(movement);
        }

        #endregion

        #region Alert Methods

        // التحقق من التنبيهات وإنشاؤها
        private async Task CheckAndCreateAlertsAsync(int productId, SqliteConnection connection, SqliteTransaction transaction)
        {
            try
            {
                // الحصول على إعدادات المنتج
                var settingsSql = @"
                    SELECT p.Name, p.Code, p.StockQuantity, 
                           COALESCE(is.MinimumStock, 0) as MinStock,
                           COALESCE(is.ReorderLevel, 0) as ReorderLevel,
                           COALESCE(is.EnableAlerts, 1) as EnableAlerts
                    FROM Products p
                    LEFT JOIN InventorySettings is ON p.Id = is.ProductId
                    WHERE p.Id = @ProductId";

                using var settingsCommand = new SqliteCommand(settingsSql, connection, transaction);
                settingsCommand.Parameters.AddWithValue("@ProductId", productId);

                using var reader = await settingsCommand.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    var productName = Convert.ToString(reader[0]) ?? "";
                    var productCode = Convert.ToString(reader[1]) ?? "";
                    var currentStock = Convert.ToDecimal(reader[2]);
                    var minStock = Convert.ToDecimal(reader[3]);
                    var reorderLevel = Convert.ToDecimal(reader[4]);
                    var enableAlerts = Convert.ToBoolean(reader[5]);

                    if (!enableAlerts) return;

                    await reader.CloseAsync();

                    // التحقق من وجود تنبيهات غير محلولة
                    var existingAlertSql = "SELECT COUNT(*) FROM InventoryAlerts WHERE ProductId = @ProductId AND IsResolved = 0";
                    using var existingCommand = new SqliteCommand(existingAlertSql, connection, transaction);
                    existingCommand.Parameters.AddWithValue("@ProductId", productId);
                    var existingCount = Convert.ToInt32(await existingCommand.ExecuteScalarAsync());

                    // إنشاء تنبيه إذا لم يكن موجوداً
                    if (existingCount == 0)
                    {
                        AlertLevel? alertLevel = null;
                        string? alertTitle = null;
                        string? alertMessage = null;

                        if (currentStock <= 0)
                        {
                            alertLevel = AlertLevel.Emergency;
                            alertTitle = "نفاد المخزون";
                            alertMessage = $"المنتج {productName} ({productCode}) نفد من المخزون";
                        }
                        else if (currentStock <= minStock)
                        {
                            alertLevel = AlertLevel.Critical;
                            alertTitle = "مخزون منخفض";
                            alertMessage = $"المنتج {productName} ({productCode}) وصل للحد الأدنى. الرصيد الحالي: {currentStock}";
                        }
                        else if (currentStock <= reorderLevel)
                        {
                            alertLevel = AlertLevel.Warning;
                            alertTitle = "حاجة لإعادة الطلب";
                            alertMessage = $"المنتج {productName} ({productCode}) يحتاج إعادة طلب. الرصيد الحالي: {currentStock}";
                        }

                        if (alertLevel.HasValue)
                        {
                            await CreateAlertAsync(productId, alertLevel.Value, alertTitle!, alertMessage!, 
                                                 currentStock, minStock, reorderLevel, connection, transaction);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }
        }

        // إنشاء تنبيه
        private async Task CreateAlertAsync(int productId, AlertLevel level, string title, string message,
                                          decimal currentStock, decimal minStock, decimal reorderLevel,
                                          SqliteConnection connection, SqliteTransaction transaction)
        {
            var sql = @"
                INSERT INTO InventoryAlerts 
                (ProductId, Level, Title, Message, CurrentStock, MinimumStock, ReorderLevel, CreatedAt)
                VALUES 
                (@ProductId, @Level, @Title, @Message, @CurrentStock, @MinimumStock, @ReorderLevel, @CreatedAt)";

            using var command = new SqliteCommand(sql, connection, transaction);
            command.Parameters.AddWithValue("@ProductId", productId);
            command.Parameters.AddWithValue("@Level", (int)level);
            command.Parameters.AddWithValue("@Title", title);
            command.Parameters.AddWithValue("@Message", message);
            command.Parameters.AddWithValue("@CurrentStock", currentStock);
            command.Parameters.AddWithValue("@MinimumStock", minStock);
            command.Parameters.AddWithValue("@ReorderLevel", reorderLevel);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now);

            await command.ExecuteNonQueryAsync();
        }

        // الحصول على التنبيهات
        public async Task<List<InventoryAlert>> GetAlertsAsync(bool unreadOnly = false, bool unresolvedOnly = true)
        {
            var alerts = new List<InventoryAlert>();

            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT ia.*, p.Name as ProductName, p.Code as ProductCode, u.FullName as ResolvedByName
                    FROM InventoryAlerts ia
                    LEFT JOIN Products p ON ia.ProductId = p.Id
                    LEFT JOIN Users u ON ia.ResolvedBy = u.Id
                    WHERE 1=1";

                if (unreadOnly)
                    sql += " AND ia.IsRead = 0";

                if (unresolvedOnly)
                    sql += " AND ia.IsResolved = 0";

                sql += " ORDER BY ia.Level DESC, ia.CreatedAt DESC";

                using var command = new SqliteCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    alerts.Add(new InventoryAlert
                    {
                        Id = Convert.ToInt32(reader[0]),
                        ProductId = Convert.ToInt32(reader[1]),
                        Level = (AlertLevel)Convert.ToInt32(reader[2]),
                        Title = Convert.ToString(reader[3]) ?? "",
                        Message = Convert.ToString(reader[4]) ?? "",
                        CurrentStock = Convert.ToDecimal(reader[5]),
                        MinimumStock = Convert.ToDecimal(reader[6]),
                        ReorderLevel = Convert.ToDecimal(reader[7]),
                        IsRead = Convert.ToBoolean(reader[8]),
                        IsResolved = Convert.ToBoolean(reader[9]),
                        CreatedAt = Convert.ToDateTime(reader[10]),
                        ReadAt = reader[11] != DBNull.Value ? Convert.ToDateTime(reader[11]) : null,
                        ResolvedAt = reader[12] != DBNull.Value ? Convert.ToDateTime(reader[12]) : null,
                        ResolvedBy = reader[13] != DBNull.Value ? Convert.ToInt32(reader[13]) : null,
                        ProductName = Convert.ToString(reader[14]) ?? "",
                        ProductCode = Convert.ToString(reader[15]) ?? "",
                        ResolvedByName = Convert.ToString(reader[16])
                    });
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return alerts;
        }

        // تحديد التنبيه كمقروء
        public async Task<bool> MarkAlertAsReadAsync(int alertId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "UPDATE InventoryAlerts SET IsRead = 1, ReadAt = @ReadAt WHERE Id = @Id";
                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@ReadAt", DateTime.Now);
                command.Parameters.AddWithValue("@Id", alertId);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch
            {
                return false;
            }
        }

        // حل التنبيه
        public async Task<bool> ResolveAlertAsync(int alertId, int userId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = "UPDATE InventoryAlerts SET IsResolved = 1, ResolvedAt = @ResolvedAt, ResolvedBy = @ResolvedBy WHERE Id = @Id";
                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@ResolvedAt", DateTime.Now);
                command.Parameters.AddWithValue("@ResolvedBy", userId);
                command.Parameters.AddWithValue("@Id", alertId);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Statistics Methods

        // الحصول على إحصائيات المخزون
        public async Task<InventoryStatistics> GetInventoryStatisticsAsync()
        {
            var stats = new InventoryStatistics();

            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                // إجمالي المنتجات
                var totalProductsSql = "SELECT COUNT(*) FROM Products WHERE IsActive = 1";
                using var totalProductsCommand = new SqliteCommand(totalProductsSql, connection);
                stats.TotalProducts = Convert.ToInt32(await totalProductsCommand.ExecuteScalarAsync());

                // المنتجات منخفضة المخزون
                var lowStockSql = @"
                    SELECT COUNT(*) FROM Products p
                    LEFT JOIN InventorySettings is ON p.Id = is.ProductId
                    WHERE p.IsActive = 1 AND p.StockQuantity <= COALESCE(is.MinimumStock, 0) AND p.StockQuantity > 0";
                using var lowStockCommand = new SqliteCommand(lowStockSql, connection);
                stats.LowStockProducts = Convert.ToInt32(await lowStockCommand.ExecuteScalarAsync());

                // المنتجات نافدة المخزون
                var outOfStockSql = "SELECT COUNT(*) FROM Products WHERE IsActive = 1 AND StockQuantity <= 0";
                using var outOfStockCommand = new SqliteCommand(outOfStockSql, connection);
                stats.OutOfStockProducts = Convert.ToInt32(await outOfStockCommand.ExecuteScalarAsync());

                // قيمة المخزون الإجمالية
                var totalValueSql = "SELECT COALESCE(SUM(StockQuantity * PurchasePrice), 0) FROM Products WHERE IsActive = 1";
                using var totalValueCommand = new SqliteCommand(totalValueSql, connection);
                stats.TotalInventoryValue = Convert.ToDecimal(await totalValueCommand.ExecuteScalarAsync());

                // متوسط قيمة المخزون
                if (stats.TotalProducts > 0)
                    stats.AverageInventoryValue = stats.TotalInventoryValue / stats.TotalProducts;

                // إجمالي الحركات (آخر 30 يوم)
                var movementsSql = "SELECT COUNT(*) FROM InventoryMovements WHERE MovementDate >= @FromDate";
                using var movementsCommand = new SqliteCommand(movementsSql, connection);
                movementsCommand.Parameters.AddWithValue("@FromDate", DateTime.Now.AddDays(-30));
                stats.TotalMovements = Convert.ToInt32(await movementsCommand.ExecuteScalarAsync());

                // التنبيهات غير المحلولة
                var alertsSql = "SELECT COUNT(*) FROM InventoryAlerts WHERE IsResolved = 0";
                using var alertsCommand = new SqliteCommand(alertsSql, connection);
                stats.UnresolvedAlerts = Convert.ToInt32(await alertsCommand.ExecuteScalarAsync());

                stats.LastUpdated = DateTime.Now;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return stats;
        }

        #endregion

        #region Inventory Settings Methods

        // الحصول على إعدادات المخزون للمنتج
        public async Task<InventorySettings?> GetInventorySettingsAsync(int productId)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                var sql = @"
                    SELECT is.*, p.Name as ProductName, p.Code as ProductCode,
                           s.Name as PreferredSupplierName, u.FullName as UpdatedByName
                    FROM InventorySettings is
                    LEFT JOIN Products p ON is.ProductId = p.Id
                    LEFT JOIN Suppliers s ON is.PreferredSupplierId = s.Id
                    LEFT JOIN Users u ON is.UpdatedBy = u.Id
                    WHERE is.ProductId = @ProductId";

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@ProductId", productId);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new InventorySettings
                    {
                        Id = Convert.ToInt32(reader[0]),
                        ProductId = Convert.ToInt32(reader[1]),
                        MinimumStock = Convert.ToDecimal(reader[2]),
                        ReorderLevel = Convert.ToDecimal(reader[3]),
                        MaximumStock = Convert.ToDecimal(reader[4]),
                        ReorderQuantity = Convert.ToDecimal(reader[5]),
                        PreferredSupplierId = reader[6] != DBNull.Value ? Convert.ToInt32(reader[6]) : null,
                        LeadTimeDays = Convert.ToDecimal(reader[7]),
                        AutoReorder = Convert.ToBoolean(reader[8]),
                        EnableAlerts = Convert.ToBoolean(reader[9]),
                        LastUpdated = reader[10] != DBNull.Value ? Convert.ToDateTime(reader[10]) : null,
                        UpdatedBy = reader[11] != DBNull.Value ? Convert.ToInt32(reader[11]) : null,
                        ProductName = Convert.ToString(reader[12]) ?? "",
                        ProductCode = Convert.ToString(reader[13]) ?? "",
                        PreferredSupplierName = Convert.ToString(reader[14]),
                        UpdatedByName = Convert.ToString(reader[15])
                    };
                }
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
            }

            return null;
        }

        // حفظ إعدادات المخزون
        public async Task<bool> SaveInventorySettingsAsync(InventorySettings settings)
        {
            try
            {
                using var connection = _dbManager.GetConnection();
                await connection.OpenAsync();

                // التحقق من وجود الإعدادات
                var existsSql = "SELECT COUNT(*) FROM InventorySettings WHERE ProductId = @ProductId";
                using var existsCommand = new SqliteCommand(existsSql, connection);
                existsCommand.Parameters.AddWithValue("@ProductId", settings.ProductId);
                var exists = Convert.ToInt32(await existsCommand.ExecuteScalarAsync()) > 0;

                string sql;
                if (exists)
                {
                    sql = @"
                        UPDATE InventorySettings SET
                            MinimumStock = @MinimumStock,
                            ReorderLevel = @ReorderLevel,
                            MaximumStock = @MaximumStock,
                            ReorderQuantity = @ReorderQuantity,
                            PreferredSupplierId = @PreferredSupplierId,
                            LeadTimeDays = @LeadTimeDays,
                            AutoReorder = @AutoReorder,
                            EnableAlerts = @EnableAlerts,
                            LastUpdated = @LastUpdated,
                            UpdatedBy = @UpdatedBy
                        WHERE ProductId = @ProductId";
                }
                else
                {
                    sql = @"
                        INSERT INTO InventorySettings
                        (ProductId, MinimumStock, ReorderLevel, MaximumStock, ReorderQuantity,
                         PreferredSupplierId, LeadTimeDays, AutoReorder, EnableAlerts, LastUpdated, UpdatedBy)
                        VALUES
                        (@ProductId, @MinimumStock, @ReorderLevel, @MaximumStock, @ReorderQuantity,
                         @PreferredSupplierId, @LeadTimeDays, @AutoReorder, @EnableAlerts, @LastUpdated, @UpdatedBy)";
                }

                using var command = new SqliteCommand(sql, connection);
                command.Parameters.AddWithValue("@ProductId", settings.ProductId);
                command.Parameters.AddWithValue("@MinimumStock", settings.MinimumStock);
                command.Parameters.AddWithValue("@ReorderLevel", settings.ReorderLevel);
                command.Parameters.AddWithValue("@MaximumStock", settings.MaximumStock);
                command.Parameters.AddWithValue("@ReorderQuantity", settings.ReorderQuantity);
                command.Parameters.AddWithValue("@PreferredSupplierId", settings.PreferredSupplierId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LeadTimeDays", settings.LeadTimeDays);
                command.Parameters.AddWithValue("@AutoReorder", settings.AutoReorder);
                command.Parameters.AddWithValue("@EnableAlerts", settings.EnableAlerts);
                command.Parameters.AddWithValue("@LastUpdated", DateTime.Now);
                command.Parameters.AddWithValue("@UpdatedBy", settings.UpdatedBy ?? (object)DBNull.Value);

                return await command.ExecuteNonQueryAsync() > 0;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                return false;
            }
        }

        #endregion

        #region Stock Adjustment Methods

        // Stock Adjustment Methods
        public async Task<List<StockAdjustment>> GetStockAdjustmentsAsync()
        {
            var adjustments = new List<StockAdjustment>();

            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();

            var sql = @"SELECT Id, AdjustmentNumber, AdjustmentDate, Reason, Notes, IsApproved,
                              CreatedBy, CreatedAt, ApprovedBy, ApprovedAt
                       FROM StockAdjustments
                       ORDER BY CreatedAt DESC";

            using var command = connection.CreateCommand();
            command.CommandText = sql;

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var adjustment = new StockAdjustment
                {
                    Id = Convert.ToInt32(reader[0]),
                    AdjustmentNumber = Convert.ToString(reader[1]) ?? "",
                    AdjustmentDate = Convert.ToDateTime(reader[2]),
                    Reason = (StockAdjustmentReason)Convert.ToInt32(reader[3]),
                    Notes = Convert.ToString(reader[4]) ?? "",
                    IsApproved = Convert.ToBoolean(reader[5]),
                    CreatedBy = Convert.ToInt32(reader[6]),
                    CreatedAt = Convert.ToDateTime(reader[7]),
                    ApprovedBy = reader[8] == DBNull.Value ? null : Convert.ToInt32(reader[8]),
                    ApprovedAt = reader[9] == DBNull.Value ? null : Convert.ToDateTime(reader[9])
                };

                // Load items for this adjustment
                adjustment.Items = await GetStockAdjustmentItemsAsync(adjustment.Id);
                adjustments.Add(adjustment);
            }

            return adjustments;
        }

        public async Task<StockAdjustment?> GetStockAdjustmentAsync(int adjustmentId)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();

            var sql = @"SELECT Id, AdjustmentNumber, AdjustmentDate, Reason, Notes, IsApproved,
                              CreatedBy, CreatedAt, ApprovedBy, ApprovedAt
                       FROM StockAdjustments
                       WHERE Id = @Id";

            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.Parameters.AddWithValue("@Id", adjustmentId);

            using var reader = await command.ExecuteReaderAsync();
            if (await reader.ReadAsync())
            {
                var adjustment = new StockAdjustment
                {
                    Id = Convert.ToInt32(reader[0]),
                    AdjustmentNumber = Convert.ToString(reader[1]) ?? "",
                    AdjustmentDate = Convert.ToDateTime(reader[2]),
                    Reason = (StockAdjustmentReason)Convert.ToInt32(reader[3]),
                    Notes = Convert.ToString(reader[4]) ?? "",
                    IsApproved = Convert.ToBoolean(reader[5]),
                    CreatedBy = Convert.ToInt32(reader[6]),
                    CreatedAt = Convert.ToDateTime(reader[7]),
                    ApprovedBy = reader[8] == DBNull.Value ? null : Convert.ToInt32(reader[8]),
                    ApprovedAt = reader[9] == DBNull.Value ? null : Convert.ToDateTime(reader[9])
                };

                // Load items for this adjustment
                adjustment.Items = await GetStockAdjustmentItemsAsync(adjustment.Id);
                return adjustment;
            }

            return null;
        }

        private async Task<List<StockAdjustmentItem>> GetStockAdjustmentItemsAsync(int adjustmentId)
        {
            var items = new List<StockAdjustmentItem>();

            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();

            var sql = @"SELECT sai.Id, sai.ProductId, sai.SystemQuantity, sai.ActualQuantity,
                              sai.UnitCost, sai.Notes,
                              sai.ProductCode, sai.ProductName, sai.Unit
                       FROM StockAdjustmentItems sai
                       WHERE sai.AdjustmentId = @AdjustmentId";

            using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.Parameters.AddWithValue("@AdjustmentId", adjustmentId);

            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                items.Add(new StockAdjustmentItem
                {
                    Id = Convert.ToInt32(reader[0]),
                    ProductId = Convert.ToInt32(reader[1]),
                    SystemQuantity = Convert.ToDecimal(reader[2]),
                    ActualQuantity = Convert.ToDecimal(reader[3]),
                    UnitCost = Convert.ToDecimal(reader[4]),
                    Notes = Convert.ToString(reader[5]) ?? "",
                    ProductCode = Convert.ToString(reader[6]) ?? "",
                    ProductName = Convert.ToString(reader[7]) ?? "",
                    Unit = Convert.ToString(reader[8]) ?? ""
                });
            }

            return items;
        }

        public async Task<int> CreateStockAdjustmentAsync(StockAdjustment adjustment)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Insert adjustment
                var adjustmentSql = @"INSERT INTO StockAdjustments
                    (AdjustmentNumber, AdjustmentDate, Reason, Notes, IsApproved, CreatedBy, CreatedAt, TotalAdjustmentValue)
                    VALUES (@AdjustmentNumber, @AdjustmentDate, @Reason, @Notes, @IsApproved, @CreatedBy, @CreatedAt, @TotalAdjustmentValue);
                    SELECT last_insert_rowid();";

                using var adjustmentCommand = connection.CreateCommand();
                adjustmentCommand.Transaction = transaction;
                adjustmentCommand.CommandText = adjustmentSql;
                adjustmentCommand.Parameters.AddWithValue("@AdjustmentNumber", adjustment.AdjustmentNumber);
                adjustmentCommand.Parameters.AddWithValue("@AdjustmentDate", adjustment.AdjustmentDate);
                adjustmentCommand.Parameters.AddWithValue("@Reason", (int)adjustment.Reason);
                adjustmentCommand.Parameters.AddWithValue("@Notes", adjustment.Notes ?? "");
                adjustmentCommand.Parameters.AddWithValue("@IsApproved", adjustment.IsApproved);
                adjustmentCommand.Parameters.AddWithValue("@CreatedBy", adjustment.CreatedBy);
                adjustmentCommand.Parameters.AddWithValue("@TotalAdjustmentValue", adjustment.TotalAdjustmentValue);
                adjustmentCommand.Parameters.AddWithValue("@CreatedAt", adjustment.CreatedAt);

                var adjustmentId = Convert.ToInt32(await adjustmentCommand.ExecuteScalarAsync());

                // Insert adjustment items
                if (adjustment.Items != null && adjustment.Items.Count > 0)
                {
                    var itemSql = @"INSERT INTO StockAdjustmentItems
                        (AdjustmentId, ProductId, ProductCode, ProductName, SystemQuantity, ActualQuantity, UnitCost, Notes, Unit)
                        VALUES (@AdjustmentId, @ProductId, @ProductCode, @ProductName, @SystemQuantity, @ActualQuantity, @UnitCost, @Notes, @Unit)";

                    foreach (var item in adjustment.Items)
                    {
                        using var itemCommand = connection.CreateCommand();
                        itemCommand.Transaction = transaction;
                        itemCommand.CommandText = itemSql;
                        itemCommand.Parameters.AddWithValue("@AdjustmentId", adjustmentId);
                        itemCommand.Parameters.AddWithValue("@ProductId", item.ProductId);
                        itemCommand.Parameters.AddWithValue("@ProductCode", item.ProductCode);
                        itemCommand.Parameters.AddWithValue("@ProductName", item.ProductName);
                        itemCommand.Parameters.AddWithValue("@SystemQuantity", item.SystemQuantity);
                        itemCommand.Parameters.AddWithValue("@ActualQuantity", item.ActualQuantity);
                        itemCommand.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                        itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? "");
                        itemCommand.Parameters.AddWithValue("@Unit", item.Unit);

                        await itemCommand.ExecuteNonQueryAsync();
                    }
                }

                transaction.Commit();
                return adjustmentId;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<bool> ApproveStockAdjustmentAsync(int adjustmentId, int approvedBy)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Get adjustment details
                var adjustment = await GetStockAdjustmentAsync(adjustmentId);
                if (adjustment == null || adjustment.IsApproved)
                    return false;

                // Update adjustment status
                var updateSql = @"UPDATE StockAdjustments
                                 SET IsApproved = 1, ApprovedBy = @ApprovedBy, ApprovedAt = @ApprovedAt
                                 WHERE Id = @Id";

                using var updateCommand = connection.CreateCommand();
                updateCommand.Transaction = transaction;
                updateCommand.CommandText = updateSql;
                updateCommand.Parameters.AddWithValue("@ApprovedBy", approvedBy);
                updateCommand.Parameters.AddWithValue("@ApprovedAt", DateTime.Now);
                updateCommand.Parameters.AddWithValue("@Id", adjustmentId);

                await updateCommand.ExecuteNonQueryAsync();

                // Create inventory movements and update stock
                foreach (var item in adjustment.Items)
                {
                    if (item.AdjustmentQuantity != 0)
                    {
                        // Create inventory movement
                        var movementType = item.AdjustmentQuantity > 0 ? InventoryMovementType.Adjustment : InventoryMovementType.Adjustment;
                        var movement = new InventoryMovement
                        {
                            ProductId = item.ProductId,
                            MovementType = movementType,
                            Quantity = Math.Abs(item.AdjustmentQuantity),
                            UnitCost = item.UnitCost,
                            ReferenceNumber = $"ADJ-{adjustmentId}",
                            ReferenceId = adjustmentId,
                            Notes = $"Stock adjustment approved",
                            MovementDate = DateTime.Now,
                            UserId = approvedBy,
                            CreatedAt = DateTime.Now
                        };

                        await AddInventoryMovementAsync(movement);

                        // Update product stock
                        var stockUpdateSql = "UPDATE Products SET Stock = @NewStock WHERE Id = @ProductId";
                        using var stockCommand = connection.CreateCommand();
                        stockCommand.Transaction = transaction;
                        stockCommand.CommandText = stockUpdateSql;
                        stockCommand.Parameters.AddWithValue("@NewStock", item.ActualQuantity);
                        stockCommand.Parameters.AddWithValue("@ProductId", item.ProductId);

                        await stockCommand.ExecuteNonQueryAsync();
                    }
                }

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<bool> SavePhysicalCountAsync(PhysicalCount physicalCount)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Insert physical count record
                var countSql = @"INSERT INTO PhysicalCounts
                    (CountDate, Notes, CreatedBy, CreatedAt)
                    VALUES (@CountDate, @Notes, @CreatedBy, @CreatedAt);
                    SELECT last_insert_rowid();";

                using var countCommand = connection.CreateCommand();
                countCommand.Transaction = transaction;
                countCommand.CommandText = countSql;
                countCommand.Parameters.AddWithValue("@CountDate", physicalCount.CountDate);
                countCommand.Parameters.AddWithValue("@Notes", physicalCount.Notes ?? "");
                countCommand.Parameters.AddWithValue("@CreatedBy", physicalCount.CreatedBy);
                countCommand.Parameters.AddWithValue("@CreatedAt", physicalCount.CreatedAt);

                var countId = Convert.ToInt32(await countCommand.ExecuteScalarAsync());

                // Insert count items
                if (physicalCount.Items != null && physicalCount.Items.Count > 0)
                {
                    var itemSql = @"INSERT INTO PhysicalCountItems
                        (CountId, ProductId, SystemStock, PhysicalStock, Variance, Notes)
                        VALUES (@CountId, @ProductId, @SystemStock, @PhysicalStock, @Variance, @Notes)";

                    foreach (var item in physicalCount.Items)
                    {
                        using var itemCommand = connection.CreateCommand();
                        itemCommand.Transaction = transaction;
                        itemCommand.CommandText = itemSql;
                        itemCommand.Parameters.AddWithValue("@CountId", countId);
                        itemCommand.Parameters.AddWithValue("@ProductId", item.ProductId);
                        itemCommand.Parameters.AddWithValue("@SystemStock", item.SystemStock);
                        itemCommand.Parameters.AddWithValue("@PhysicalStock", item.PhysicalStock);
                        itemCommand.Parameters.AddWithValue("@Variance", item.Variance);
                        itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? "");

                        await itemCommand.ExecuteNonQueryAsync();
                    }
                }

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<bool> DeleteStockAdjustmentAsync(int adjustmentId)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Delete adjustment items first
                var deleteItemsSql = "DELETE FROM StockAdjustmentItems WHERE AdjustmentId = @AdjustmentId";
                using var itemsCommand = connection.CreateCommand();
                itemsCommand.CommandText = deleteItemsSql;
                itemsCommand.Parameters.AddWithValue("@AdjustmentId", adjustmentId);
                await itemsCommand.ExecuteNonQueryAsync();

                // Delete adjustment
                var deleteAdjustmentSql = "DELETE FROM StockAdjustments WHERE Id = @Id";
                using var adjustmentCommand = connection.CreateCommand();
                adjustmentCommand.CommandText = deleteAdjustmentSql;
                adjustmentCommand.Parameters.AddWithValue("@Id", adjustmentId);
                await adjustmentCommand.ExecuteNonQueryAsync();

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public async Task<bool> UpdateStockAdjustmentAsync(StockAdjustment adjustment)
        {
            using var connection = _dbManager.GetConnection();
            await connection.OpenAsync();
            using var transaction = connection.BeginTransaction();

            try
            {
                // Update adjustment
                var updateSql = @"UPDATE StockAdjustments SET
                                AdjustmentNumber = @AdjustmentNumber,
                                AdjustmentDate = @AdjustmentDate,
                                Reason = @Reason,
                                Notes = @Notes,
                                IsApproved = @IsApproved,
                                ApprovedBy = @ApprovedBy,
                                ApprovedAt = @ApprovedAt,
                                TotalAdjustmentValue = @TotalAdjustmentValue
                                WHERE Id = @Id";

                using var command = connection.CreateCommand();
                command.CommandText = updateSql;
                command.Parameters.AddWithValue("@Id", adjustment.Id);
                command.Parameters.AddWithValue("@AdjustmentNumber", adjustment.AdjustmentNumber);
                command.Parameters.AddWithValue("@AdjustmentDate", adjustment.AdjustmentDate);
                command.Parameters.AddWithValue("@Reason", (int)adjustment.Reason);
                command.Parameters.AddWithValue("@Notes", adjustment.Notes ?? "");
                command.Parameters.AddWithValue("@IsApproved", adjustment.IsApproved);
                command.Parameters.AddWithValue("@ApprovedBy", adjustment.ApprovedBy ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ApprovedAt", adjustment.ApprovedAt ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TotalAdjustmentValue", adjustment.TotalAdjustmentValue);

                await command.ExecuteNonQueryAsync();

                // Delete existing items
                var deleteItemsSql = "DELETE FROM StockAdjustmentItems WHERE AdjustmentId = @AdjustmentId";
                using var deleteCommand = connection.CreateCommand();
                deleteCommand.CommandText = deleteItemsSql;
                deleteCommand.Parameters.AddWithValue("@AdjustmentId", adjustment.Id);
                await deleteCommand.ExecuteNonQueryAsync();

                // Insert updated items
                if (adjustment.Items != null && adjustment.Items.Any())
                {
                    foreach (var item in adjustment.Items)
                    {
                        var insertItemSql = @"INSERT INTO StockAdjustmentItems
                                            (AdjustmentId, ProductId, ProductCode, ProductName, SystemQuantity,
                                             ActualQuantity, UnitCost, Notes, Unit)
                                            VALUES (@AdjustmentId, @ProductId, @ProductCode, @ProductName,
                                                   @SystemQuantity, @ActualQuantity, @UnitCost, @Notes, @Unit)";

                        using var itemCommand = connection.CreateCommand();
                        itemCommand.CommandText = insertItemSql;
                        itemCommand.Parameters.AddWithValue("@AdjustmentId", adjustment.Id);
                        itemCommand.Parameters.AddWithValue("@ProductId", item.ProductId);
                        itemCommand.Parameters.AddWithValue("@ProductCode", item.ProductCode);
                        itemCommand.Parameters.AddWithValue("@ProductName", item.ProductName);
                        itemCommand.Parameters.AddWithValue("@SystemQuantity", item.SystemQuantity);
                        itemCommand.Parameters.AddWithValue("@ActualQuantity", item.ActualQuantity);
                        itemCommand.Parameters.AddWithValue("@UnitCost", item.UnitCost);
                        itemCommand.Parameters.AddWithValue("@Notes", item.Notes ?? "");
                        itemCommand.Parameters.AddWithValue("@Unit", item.Unit);

                        await itemCommand.ExecuteNonQueryAsync();
                    }
                }

                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        #endregion
    }
}
