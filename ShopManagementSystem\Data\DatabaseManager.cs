using Microsoft.Data.Sqlite;
using System;
using System.IO;

namespace ShopManagementSystem.Data
{
    public class DatabaseManager
    {
        private static readonly string DatabasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "shop_database.db");
        private static readonly string ConnectionString = $"Data Source={DatabasePath}";

        public static string GetConnectionString()
        {
            return ConnectionString;
        }

        public static void InitializeDatabase()
        {
            try
            {
                using var connection = new SqliteConnection(ConnectionString);
                connection.Open();

                // إنشاء جدول التصنيفات
                var createCategoriesTable = @"
                    CREATE TABLE IF NOT EXISTS Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1
                    )";

                // إنشاء جدول المنتجات
                var createProductsTable = @"
                    CREATE TABLE IF NOT EXISTS Products (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Code TEXT UNIQUE,
                        Barcode TEXT,
                        Price DECIMAL(10,2) NOT NULL,
                        Quantity INTEGER DEFAULT 0,
                        MinimumQuantity INTEGER DEFAULT 5,
                        CategoryId INTEGER,
                        Description TEXT,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                    )";

                // إنشاء جدول المستخدمين
                var createUsersTable = @"
                    CREATE TABLE IF NOT EXISTS Users (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Username TEXT UNIQUE NOT NULL,
                        Password TEXT NOT NULL,
                        FullName TEXT NOT NULL,
                        Role INTEGER DEFAULT 2,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        LastLogin DATETIME,
                        IsActive BOOLEAN DEFAULT 1
                    )";

                // إنشاء جدول المبيعات
                var createSalesTable = @"
                    CREATE TABLE IF NOT EXISTS Sales (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        InvoiceNumber TEXT UNIQUE NOT NULL,
                        SaleDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        TotalAmount DECIMAL(10,2) NOT NULL,
                        DiscountAmount DECIMAL(10,2) DEFAULT 0,
                        NetAmount DECIMAL(10,2) NOT NULL,
                        UserId INTEGER,
                        CustomerName TEXT,
                        CustomerPhone TEXT,
                        Notes TEXT,
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // إنشاء جدول تفاصيل المبيعات
                var createSaleDetailsTable = @"
                    CREATE TABLE IF NOT EXISTS SaleDetails (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        SaleId INTEGER,
                        ProductId INTEGER,
                        ProductName TEXT NOT NULL,
                        UnitPrice DECIMAL(10,2) NOT NULL,
                        Quantity INTEGER NOT NULL,
                        TotalPrice DECIMAL(10,2) NOT NULL,
                        DiscountAmount DECIMAL(10,2) DEFAULT 0,
                        NetPrice DECIMAL(10,2) NOT NULL,
                        FOREIGN KEY (SaleId) REFERENCES Sales(Id),
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )";

                // إنشاء جدول حركات المخزون
                var createInventoryMovementsTable = @"
                    CREATE TABLE IF NOT EXISTS InventoryMovements (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ProductId INTEGER NOT NULL,
                        ProductName TEXT NOT NULL,
                        MovementType INTEGER NOT NULL,
                        Quantity INTEGER NOT NULL,
                        PreviousQuantity INTEGER NOT NULL,
                        NewQuantity INTEGER NOT NULL,
                        Notes TEXT,
                        UserId INTEGER NOT NULL,
                        UserName TEXT NOT NULL,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (ProductId) REFERENCES Products(Id),
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // إنشاء جدول تنبيهات المخزون
                var createInventoryAlertsTable = @"
                    CREATE TABLE IF NOT EXISTS InventoryAlerts (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ProductId INTEGER NOT NULL,
                        ProductName TEXT NOT NULL,
                        CurrentQuantity INTEGER NOT NULL,
                        MinimumQuantity INTEGER NOT NULL,
                        AlertType TEXT NOT NULL,
                        IsResolved BOOLEAN DEFAULT 0,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ResolvedAt DATETIME,
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )";

                using var command = connection.CreateCommand();
                
                command.CommandText = createCategoriesTable;
                command.ExecuteNonQuery();
                
                command.CommandText = createProductsTable;
                command.ExecuteNonQuery();
                
                command.CommandText = createUsersTable;
                command.ExecuteNonQuery();
                
                command.CommandText = createSalesTable;
                command.ExecuteNonQuery();
                
                command.CommandText = createSaleDetailsTable;
                command.ExecuteNonQuery();

                command.CommandText = createInventoryMovementsTable;
                command.ExecuteNonQuery();

                command.CommandText = createInventoryAlertsTable;
                command.ExecuteNonQuery();

                // إضافة عمود UpdatedAt إلى جدول Categories إذا لم يكن موجوداً
                try
                {
                    command.CommandText = "ALTER TABLE Categories ADD COLUMN UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP";
                    command.ExecuteNonQuery();
                }
                catch
                {
                    // العمود موجود بالفعل
                }

                // إدراج بيانات أولية
                InsertInitialData(connection);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء قاعدة البيانات: {ex.Message}");
            }
        }

        private static void InsertInitialData(SqliteConnection connection)
        {
            // التحقق من وجود مستخدم مدير
            var checkAdminQuery = "SELECT COUNT(*) FROM Users WHERE Role = 1";
            using var checkCommand = connection.CreateCommand();
            checkCommand.CommandText = checkAdminQuery;
            var adminCount = Convert.ToInt32(checkCommand.ExecuteScalar());

            if (adminCount == 0)
            {
                // إضافة مستخدم مدير افتراضي
                var insertAdminQuery = @"
                    INSERT INTO Users (Username, Password, FullName, Role) 
                    VALUES ('admin', 'admin123', 'مدير النظام', 1)";
                
                using var insertCommand = connection.CreateCommand();
                insertCommand.CommandText = insertAdminQuery;
                insertCommand.ExecuteNonQuery();
            }

            // إضافة تصنيفات افتراضية
            var checkCategoriesQuery = "SELECT COUNT(*) FROM Categories";
            using var checkCatCommand = connection.CreateCommand();
            checkCatCommand.CommandText = checkCategoriesQuery;
            var categoryCount = Convert.ToInt32(checkCatCommand.ExecuteScalar());

            if (categoryCount == 0)
            {
                var insertCategoriesQuery = @"
                    INSERT INTO Categories (Name, Description) VALUES 
                    ('مشروبات', 'عصائر ومشروبات غازية'),
                    ('وجبات خفيفة', 'سندوتشات وأطعمة سريعة'),
                    ('حلويات', 'شوكولاتة وحلوى'),
                    ('منتجات عامة', 'منتجات متنوعة')";
                
                using var insertCatCommand = connection.CreateCommand();
                insertCatCommand.CommandText = insertCategoriesQuery;
                insertCatCommand.ExecuteNonQuery();
            }
        }
    }
}
