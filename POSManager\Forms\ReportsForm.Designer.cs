namespace POSManager.Forms
{
    partial class ReportsForm
    {
        private System.ComponentModel.IContainer components = null;
        private Panel pnlTop;
        private Panel pnlFilters;
        private Panel pnlContent;
        private Panel pnlButtons;
        private Label lblTitle;
        private Label lblReportType;
        private ComboBox cmbReportType;
        private Label lblStartDate;
        private DateTimePicker dtpStartDate;
        private Label lblEndDate;
        private DateTimePicker dtpEndDate;
        private Label lblUser;
        private ComboBox cmbUser;
        private Button btnGenerateReport;
        private Button btnExportReport;
        private Button btnClose;
        private SplitContainer splitContainer;
        private RichTextBox rtbSummary;
        private DataGridView dgvReport;
        private Label lblReportTitle;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.pnlTop = new Panel();
            this.lblTitle = new Label();
            this.pnlFilters = new Panel();
            this.lblReportType = new Label();
            this.cmbReportType = new ComboBox();
            this.lblStartDate = new Label();
            this.dtpStartDate = new DateTimePicker();
            this.lblEndDate = new Label();
            this.dtpEndDate = new DateTimePicker();
            this.lblUser = new Label();
            this.cmbUser = new ComboBox();
            this.pnlButtons = new Panel();
            this.btnGenerateReport = new Button();
            this.btnExportReport = new Button();
            this.btnClose = new Button();
            this.pnlContent = new Panel();
            this.splitContainer = new SplitContainer();
            this.rtbSummary = new RichTextBox();
            this.dgvReport = new DataGridView();
            this.lblReportTitle = new Label();

            this.SuspendLayout();

            // 
            // pnlTop
            // 
            this.pnlTop.BackColor = Color.FromArgb(41, 128, 185);
            this.pnlTop.Controls.Add(this.lblTitle);
            this.pnlTop.Dock = DockStyle.Top;
            this.pnlTop.Location = new Point(0, 0);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new Size(1200, 60);
            this.pnlTop.TabIndex = 0;

            // 
            // lblTitle
            // 
            this.lblTitle.Anchor = AnchorStyles.None;
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Segoe UI", 18F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(500, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 32);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "التقارير والإحصائيات";
            this.lblTitle.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // pnlFilters
            // 
            this.pnlFilters.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlFilters.Controls.Add(this.lblReportType);
            this.pnlFilters.Controls.Add(this.cmbReportType);
            this.pnlFilters.Controls.Add(this.lblStartDate);
            this.pnlFilters.Controls.Add(this.dtpStartDate);
            this.pnlFilters.Controls.Add(this.lblEndDate);
            this.pnlFilters.Controls.Add(this.dtpEndDate);
            this.pnlFilters.Controls.Add(this.lblUser);
            this.pnlFilters.Controls.Add(this.cmbUser);
            this.pnlFilters.Dock = DockStyle.Top;
            this.pnlFilters.Location = new Point(0, 60);
            this.pnlFilters.Name = "pnlFilters";
            this.pnlFilters.Size = new Size(1200, 80);
            this.pnlFilters.TabIndex = 1;

            // 
            // lblReportType
            // 
            this.lblReportType.AutoSize = true;
            this.lblReportType.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblReportType.Location = new Point(1050, 15);
            this.lblReportType.Name = "lblReportType";
            this.lblReportType.Size = new Size(80, 19);
            this.lblReportType.TabIndex = 0;
            this.lblReportType.Text = "نوع التقرير:";

            // 
            // cmbReportType
            // 
            this.cmbReportType.Font = new Font("Segoe UI", 10F);
            this.cmbReportType.FormattingEnabled = true;
            this.cmbReportType.Location = new Point(850, 12);
            this.cmbReportType.Name = "cmbReportType";
            this.cmbReportType.Size = new Size(200, 25);
            this.cmbReportType.TabIndex = 1;

            // 
            // lblStartDate
            // 
            this.lblStartDate.AutoSize = true;
            this.lblStartDate.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblStartDate.Location = new Point(750, 15);
            this.lblStartDate.Name = "lblStartDate";
            this.lblStartDate.Size = new Size(60, 19);
            this.lblStartDate.TabIndex = 2;
            this.lblStartDate.Text = "من تاريخ:";

            // 
            // dtpStartDate
            // 
            this.dtpStartDate.Font = new Font("Segoe UI", 10F);
            this.dtpStartDate.Format = DateTimePickerFormat.Short;
            this.dtpStartDate.Location = new Point(600, 12);
            this.dtpStartDate.Name = "dtpStartDate";
            this.dtpStartDate.Size = new Size(150, 25);
            this.dtpStartDate.TabIndex = 3;

            // 
            // lblEndDate
            // 
            this.lblEndDate.AutoSize = true;
            this.lblEndDate.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblEndDate.Location = new Point(520, 15);
            this.lblEndDate.Name = "lblEndDate";
            this.lblEndDate.Size = new Size(70, 19);
            this.lblEndDate.TabIndex = 4;
            this.lblEndDate.Text = "إلى تاريخ:";

            // 
            // dtpEndDate
            // 
            this.dtpEndDate.Font = new Font("Segoe UI", 10F);
            this.dtpEndDate.Format = DateTimePickerFormat.Short;
            this.dtpEndDate.Location = new Point(370, 12);
            this.dtpEndDate.Name = "dtpEndDate";
            this.dtpEndDate.Size = new Size(150, 25);
            this.dtpEndDate.TabIndex = 5;

            // 
            // lblUser
            // 
            this.lblUser.AutoSize = true;
            this.lblUser.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.lblUser.Location = new Point(290, 15);
            this.lblUser.Name = "lblUser";
            this.lblUser.Size = new Size(70, 19);
            this.lblUser.TabIndex = 6;
            this.lblUser.Text = "المستخدم:";
            this.lblUser.Visible = false;

            // 
            // cmbUser
            // 
            this.cmbUser.Font = new Font("Segoe UI", 10F);
            this.cmbUser.FormattingEnabled = true;
            this.cmbUser.Location = new Point(120, 12);
            this.cmbUser.Name = "cmbUser";
            this.cmbUser.Size = new Size(170, 25);
            this.cmbUser.TabIndex = 7;
            this.cmbUser.Visible = false;

            // 
            // pnlButtons
            // 
            this.pnlButtons.BackColor = Color.FromArgb(236, 240, 241);
            this.pnlButtons.Controls.Add(this.btnGenerateReport);
            this.pnlButtons.Controls.Add(this.btnExportReport);
            this.pnlButtons.Controls.Add(this.btnClose);
            this.pnlButtons.Dock = DockStyle.Bottom;
            this.pnlButtons.Location = new Point(0, 650);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new Size(1200, 60);
            this.pnlButtons.TabIndex = 3;

            // 
            // btnGenerateReport
            // 
            this.btnGenerateReport.BackColor = Color.FromArgb(39, 174, 96);
            this.btnGenerateReport.FlatAppearance.BorderSize = 0;
            this.btnGenerateReport.FlatStyle = FlatStyle.Flat;
            this.btnGenerateReport.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            this.btnGenerateReport.ForeColor = Color.White;
            this.btnGenerateReport.Location = new Point(950, 15);
            this.btnGenerateReport.Name = "btnGenerateReport";
            this.btnGenerateReport.Size = new Size(120, 35);
            this.btnGenerateReport.TabIndex = 0;
            this.btnGenerateReport.Text = "إنشاء التقرير";
            this.btnGenerateReport.UseVisualStyleBackColor = false;

            // 
            // btnExportReport
            // 
            this.btnExportReport.BackColor = Color.FromArgb(52, 152, 219);
            this.btnExportReport.FlatAppearance.BorderSize = 0;
            this.btnExportReport.FlatStyle = FlatStyle.Flat;
            this.btnExportReport.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            this.btnExportReport.ForeColor = Color.White;
            this.btnExportReport.Location = new Point(820, 15);
            this.btnExportReport.Name = "btnExportReport";
            this.btnExportReport.Size = new Size(120, 35);
            this.btnExportReport.TabIndex = 1;
            this.btnExportReport.Text = "تصدير التقرير";
            this.btnExportReport.UseVisualStyleBackColor = false;

            // 
            // btnClose
            // 
            this.btnClose.BackColor = Color.FromArgb(231, 76, 60);
            this.btnClose.FlatAppearance.BorderSize = 0;
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            this.btnClose.ForeColor = Color.White;
            this.btnClose.Location = new Point(30, 15);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 35);
            this.btnClose.TabIndex = 2;
            this.btnClose.Text = "إغلاق";
            this.btnClose.UseVisualStyleBackColor = false;

            // 
            // pnlContent
            // 
            this.pnlContent.Controls.Add(this.splitContainer);
            this.pnlContent.Dock = DockStyle.Fill;
            this.pnlContent.Location = new Point(0, 140);
            this.pnlContent.Name = "pnlContent";
            this.pnlContent.Size = new Size(1200, 510);
            this.pnlContent.TabIndex = 2;

            // 
            // splitContainer
            // 
            this.splitContainer.Dock = DockStyle.Fill;
            this.splitContainer.Location = new Point(0, 0);
            this.splitContainer.Name = "splitContainer";
            this.splitContainer.Orientation = Orientation.Horizontal;

            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.rtbSummary);
            this.splitContainer.Panel1MinSize = 150;

            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.lblReportTitle);
            this.splitContainer.Panel2.Controls.Add(this.dgvReport);
            this.splitContainer.Panel2MinSize = 200;

            this.splitContainer.Size = new Size(1200, 510);
            this.splitContainer.SplitterDistance = 200;
            this.splitContainer.TabIndex = 0;

            // 
            // rtbSummary
            // 
            this.rtbSummary.BackColor = Color.White;
            this.rtbSummary.BorderStyle = BorderStyle.FixedSingle;
            this.rtbSummary.Dock = DockStyle.Fill;
            this.rtbSummary.Font = new Font("Segoe UI", 10F);
            this.rtbSummary.Location = new Point(0, 0);
            this.rtbSummary.Name = "rtbSummary";
            this.rtbSummary.ReadOnly = true;
            this.rtbSummary.Size = new Size(1200, 200);
            this.rtbSummary.TabIndex = 0;
            this.rtbSummary.Text = "اختر نوع التقرير واضغط على 'إنشاء التقرير' لعرض النتائج";

            // 
            // lblReportTitle
            // 
            this.lblReportTitle.BackColor = Color.FromArgb(52, 73, 94);
            this.lblReportTitle.Dock = DockStyle.Top;
            this.lblReportTitle.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            this.lblReportTitle.ForeColor = Color.White;
            this.lblReportTitle.Location = new Point(0, 0);
            this.lblReportTitle.Name = "lblReportTitle";
            this.lblReportTitle.Size = new Size(1200, 35);
            this.lblReportTitle.TabIndex = 1;
            this.lblReportTitle.Text = "تفاصيل التقرير";
            this.lblReportTitle.TextAlign = ContentAlignment.MiddleCenter;

            // 
            // dgvReport
            // 
            this.dgvReport.AllowUserToAddRows = false;
            this.dgvReport.AllowUserToDeleteRows = false;
            this.dgvReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvReport.BackgroundColor = Color.White;
            this.dgvReport.BorderStyle = BorderStyle.Fixed3D;
            this.dgvReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvReport.Dock = DockStyle.Fill;
            this.dgvReport.Location = new Point(0, 35);
            this.dgvReport.MultiSelect = false;
            this.dgvReport.Name = "dgvReport";
            this.dgvReport.ReadOnly = true;
            this.dgvReport.RowHeadersWidth = 25;
            this.dgvReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvReport.Size = new Size(1200, 271);
            this.dgvReport.TabIndex = 0;

            // 
            // ReportsForm
            // 
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.White;
            this.ClientSize = new Size(1200, 710);
            this.Controls.Add(this.pnlContent);
            this.Controls.Add(this.pnlButtons);
            this.Controls.Add(this.pnlFilters);
            this.Controls.Add(this.pnlTop);
            this.Font = new Font("Segoe UI", 9F);
            this.MinimumSize = new Size(1000, 600);
            this.Name = "ReportsForm";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "التقارير والإحصائيات";
            this.WindowState = FormWindowState.Maximized;

            this.pnlTop.ResumeLayout(false);
            this.pnlTop.PerformLayout();
            this.pnlFilters.ResumeLayout(false);
            this.pnlFilters.PerformLayout();
            this.pnlButtons.ResumeLayout(false);
            this.pnlContent.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            this.splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvReport)).EndInit();
            this.ResumeLayout(false);
        }
    }
}
