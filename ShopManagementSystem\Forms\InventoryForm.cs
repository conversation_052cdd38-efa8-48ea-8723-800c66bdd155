using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ShopManagementSystem.Models;
using ShopManagementSystem.Services;

namespace ShopManagementSystem.Forms
{
    public partial class InventoryForm : Form
    {
        private readonly InventoryService _inventoryService;
        private readonly ProductService _productService;
        private readonly User _currentUser;
        private List<Product> _products;
        private List<InventoryMovement> _movements;
        private List<InventoryAlert> _alerts;

        public InventoryForm(User currentUser)
        {
            InitializeComponent();
            _inventoryService = new InventoryService();
            _productService = new ProductService();
            _currentUser = currentUser;
            _products = new List<Product>();
            _movements = new List<InventoryMovement>();
            _alerts = new List<InventoryAlert>();

            InitializeData();
        }

        private async void InitializeData()
        {
            try
            {
                await LoadProducts();
                await LoadOverviewData();
                await LoadMovements();
                await Load<PERSON>lerts();
                SetupDataGridViews();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadProducts()
        {
            _products = _productService.GetAllProducts();

            // تحميل المنتجات في ComboBoxes
            cmbMovementProduct.Items.Clear();
            cmbAdjustmentProduct.Items.Clear();

            cmbMovementProduct.Items.Add(new ComboBoxItem { Text = "جميع المنتجات", Value = 0 });

            foreach (var product in _products)
            {
                var item = new ComboBoxItem { Text = product.Name, Value = product.Id };
                cmbMovementProduct.Items.Add(item);
                cmbAdjustmentProduct.Items.Add(item);
            }

            if (cmbMovementProduct.Items.Count > 0)
                cmbMovementProduct.SelectedIndex = 0;

            if (cmbAdjustmentProduct.Items.Count > 0)
                cmbAdjustmentProduct.SelectedIndex = 0;
        }

        private async Task LoadOverviewData()
        {
            try
            {
                var stats = await _inventoryService.GetInventoryStatsAsync();
                var lowStockProducts = await _inventoryService.GetLowStockProductsAsync();

                // تحديث الإحصائيات
                lblTotalProducts.Text = $"إجمالي المنتجات: {stats.TotalProducts}";
                lblLowStock.Text = $"مخزون منخفض: {stats.LowStockProducts}";
                lblOutOfStock.Text = $"مخزون نافد: {stats.OutOfStockProducts}";
                lblTotalValue.Text = $"إجمالي قيمة المخزون: {stats.TotalInventoryValue:C}";
                lblActiveAlerts.Text = $"تنبيهات نشطة: {stats.ActiveAlerts}";

                // تحديث جدول المنتجات منخفضة المخزون
                dgvLowStockProducts.DataSource = lowStockProducts.Select(p => new
                {
                    الرقم = p.Id,
                    اسم_المنتج = p.Name,
                    الكود = p.Code,
                    الكمية_الحالية = p.Quantity,
                    الحد_الأدنى = p.MinimumQuantity,
                    الفئة = p.Category?.Name ?? "غير محدد",
                    السعر = p.Price
                }).ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات النظرة العامة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadMovements()
        {
            try
            {
                _movements = await _inventoryService.GetInventoryMovementsAsync();
                UpdateMovementsGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حركات المخزون: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateMovementsGrid()
        {
            dgvMovements.DataSource = _movements.Select(m => new
            {
                الرقم = m.Id,
                المنتج = m.ProductName,
                نوع_الحركة = GetMovementTypeText(m.MovementType),
                الكمية = m.Quantity,
                الكمية_السابقة = m.PreviousQuantity,
                الكمية_الجديدة = m.NewQuantity,
                المستخدم = m.UserName,
                التاريخ = m.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                الملاحظات = m.Notes
            }).ToList();
        }

        private async Task LoadAlerts()
        {
            try
            {
                _alerts = await _inventoryService.GetInventoryAlertsAsync(chkShowResolvedAlerts.Checked);
                UpdateAlertsGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateAlertsGrid()
        {
            dgvAlerts.DataSource = _alerts.Select(a => new
            {
                الرقم = a.Id,
                المنتج = a.ProductName,
                نوع_التنبيه = a.AlertType,
                الكمية_الحالية = a.CurrentQuantity,
                الحد_الأدنى = a.MinimumQuantity,
                محلول = a.IsResolved ? "نعم" : "لا",
                تاريخ_الإنشاء = a.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                تاريخ_الحل = a.ResolvedAt?.ToString("yyyy-MM-dd HH:mm") ?? ""
            }).ToList();
        }

        private void SetupDataGridViews()
        {
            // إعداد خصائص DataGridViews
            foreach (DataGridView dgv in new[] { dgvLowStockProducts, dgvMovements, dgvAlerts })
            {
                dgv.AutoGenerateColumns = true;
                dgv.AllowUserToAddRows = false;
                dgv.AllowUserToDeleteRows = false;
                dgv.ReadOnly = true;
                dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgv.MultiSelect = false;
                dgv.RowHeadersVisible = false;
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
        }

        private string GetMovementTypeText(MovementType type)
        {
            return type switch
            {
                MovementType.StockIn => "إدخال مخزون",
                MovementType.StockOut => "إخراج مخزون",
                MovementType.Adjustment => "تعديل مخزون",
                MovementType.Sale => "بيع",
                MovementType.Return => "إرجاع",
                _ => "غير محدد"
            };
        }

        // Event Handlers
        private async void btnRefreshOverview_Click(object sender, EventArgs e)
        {
            await LoadOverviewData();
        }

        private async void btnFilterMovements_Click(object sender, EventArgs e)
        {
            try
            {
                int? productId = null;
                if (cmbMovementProduct.SelectedItem is ComboBoxItem selectedProduct && selectedProduct.Value > 0)
                {
                    productId = selectedProduct.Value;
                }

                _movements = await _inventoryService.GetInventoryMovementsAsync(
                    productId, dtpMovementFrom.Value, dtpMovementTo.Value);
                UpdateMovementsGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فلترة الحركات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnClearMovementFilter_Click(object sender, EventArgs e)
        {
            cmbMovementProduct.SelectedIndex = 0;
            dtpMovementFrom.Value = DateTime.Now.AddDays(-30);
            dtpMovementTo.Value = DateTime.Now;
            await LoadMovements();
        }

        private async void btnRefreshAlerts_Click(object sender, EventArgs e)
        {
            await LoadAlerts();
        }

        private async void btnResolveAlert_Click(object sender, EventArgs e)
        {
            if (dgvAlerts.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار تنبيه لحله", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var selectedRow = dgvAlerts.SelectedRows[0];
                var alertId = Convert.ToInt32(selectedRow.Cells["الرقم"].Value);

                var result = MessageBox.Show("هل أنت متأكد من حل هذا التنبيه؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var success = await _inventoryService.ResolveAlertAsync(alertId);
                    if (success)
                    {
                        MessageBox.Show("تم حل التنبيه بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadAlerts();
                        await LoadOverviewData(); // تحديث الإحصائيات
                    }
                    else
                    {
                        MessageBox.Show("فشل في حل التنبيه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حل التنبيه: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void chkShowResolvedAlerts_CheckedChanged(object sender, EventArgs e)
        {
            await LoadAlerts();
        }

        private void cmbAdjustmentProduct_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbAdjustmentProduct.SelectedItem is ComboBoxItem selectedItem && selectedItem.Value > 0)
            {
                var product = _products.FirstOrDefault(p => p.Id == selectedItem.Value);
                if (product != null)
                {
                    lblCurrentQuantity.Text = $"الكمية الحالية: {product.Quantity}";
                    lblCurrentMinimum.Text = $"الحد الأدنى: {product.MinimumQuantity}";
                    nudNewQuantity.Value = product.Quantity;
                    nudMinimumQuantity.Value = product.MinimumQuantity;
                }
            }
            else
            {
                lblCurrentQuantity.Text = "الكمية الحالية: 0";
                lblCurrentMinimum.Text = "الحد الأدنى: 0";
                nudNewQuantity.Value = 0;
                nudMinimumQuantity.Value = 0;
            }
        }

        private async void btnStockIn_Click(object sender, EventArgs e)
        {
            await PerformStockOperation(MovementType.StockIn);
        }

        private async void btnStockOut_Click(object sender, EventArgs e)
        {
            await PerformStockOperation(MovementType.StockOut);
        }

        private async void btnAdjustStock_Click(object sender, EventArgs e)
        {
            await PerformStockOperation(MovementType.Adjustment);
        }

        private async Task PerformStockOperation(MovementType operationType)
        {
            if (!(cmbAdjustmentProduct.SelectedItem is ComboBoxItem selectedItem) || selectedItem.Value <= 0)
            {
                MessageBox.Show("يرجى اختيار منتج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var product = _products.FirstOrDefault(p => p.Id == selectedItem.Value);
            if (product == null)
            {
                MessageBox.Show("المنتج المحدد غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                bool success = false;
                var notes = txtAdjustmentNotes.Text.Trim();
                var quantity = (int)nudNewQuantity.Value;

                switch (operationType)
                {
                    case MovementType.StockIn:
                        if (quantity <= 0)
                        {
                            MessageBox.Show("يرجى إدخال كمية صحيحة للإدخال", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                        success = await _inventoryService.StockInAsync(product.Id, quantity, notes, _currentUser);
                        break;

                    case MovementType.StockOut:
                        if (quantity <= 0)
                        {
                            MessageBox.Show("يرجى إدخال كمية صحيحة للإخراج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                        if (quantity > product.Quantity)
                        {
                            MessageBox.Show("الكمية المطلوب إخراجها أكبر من الكمية المتاحة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                        success = await _inventoryService.StockOutAsync(product.Id, quantity, notes, _currentUser);
                        break;

                    case MovementType.Adjustment:
                        if (quantity < 0)
                        {
                            MessageBox.Show("يرجى إدخال كمية صحيحة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                        success = await _inventoryService.AdjustStockAsync(product.Id, quantity, notes, _currentUser);
                        break;
                }

                if (success)
                {
                    // تحديث الحد الأدنى إذا تم تغييره
                    if (nudMinimumQuantity.Value != product.MinimumQuantity)
                    {
                        product.MinimumQuantity = (int)nudMinimumQuantity.Value;
                        _productService.UpdateProduct(product);
                    }

                    MessageBox.Show("تمت العملية بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // إعادة تحميل البيانات
                    await LoadProducts();
                    await LoadOverviewData();
                    await LoadMovements();
                    await LoadAlerts();

                    // مسح الحقول
                    txtAdjustmentNotes.Clear();
                    cmbAdjustmentProduct.SelectedIndex = 0;
                }
                else
                {
                    MessageBox.Show("فشلت العملية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تنفيذ العملية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }

    // Helper class for ComboBox items
    public class ComboBoxItem
    {
        public string Text { get; set; } = string.Empty;
        public int Value { get; set; }

        public override string ToString()
        {
            return Text;
        }
    }
}
