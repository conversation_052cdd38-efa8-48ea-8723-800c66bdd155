using System;

namespace ShopManagementSystem.Models
{
    public enum MovementType
    {
        StockIn = 1,    // إدخال مخزون
        StockOut = 2,   // إخراج مخزون
        Adjustment = 3, // تعديل مخزون
        Sale = 4,       // بيع
        Return = 5      // إرجاع
    }

    public class InventoryMovement
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public MovementType MovementType { get; set; }
        public int Quantity { get; set; }
        public int PreviousQuantity { get; set; }
        public int NewQuantity { get; set; }
        public string Notes { get; set; } = string.Empty;
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public Product? Product { get; set; }
        public User? User { get; set; }
    }

    public class InventoryAlert
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int CurrentQuantity { get; set; }
        public int MinimumQuantity { get; set; }
        public string AlertType { get; set; } = string.Empty; // "LOW_STOCK", "OUT_OF_STOCK"
        public bool IsResolved { get; set; } = false;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? ResolvedAt { get; set; }
        
        // Navigation property
        public Product? Product { get; set; }
    }
}
