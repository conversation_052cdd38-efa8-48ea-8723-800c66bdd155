using System;

namespace ShopManagementSystem.Models
{
    public enum UserRole
    {
        Admin = 1,
        Cashier = 2
    }

    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty; // في التطبيق الحقيقي يجب تشفير كلمة المرور
        public string FullName { get; set; } = string.Empty;
        public UserRole Role { get; set; } = UserRole.Cashier;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastLogin { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
