using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class CategoriesForm : Form
    {
        private List<Category> _categories = new List<Category>();
        private Category? _selectedCategory;
        private bool _isEditing = false;

        public CategoriesForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private async void InitializeForm()
        {
            try
            {
                await LoadCategoriesAsync();
                SetupDataGridView();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadCategoriesAsync()
        {
            try
            {
                _categories = await CategoryService.GetAllCategoriesAsync();
                dgvCategories.DataSource = null;
                dgvCategories.DataSource = _categories;
                
                lblTotalCategories.Text = $"إجمالي الفئات: {_categories.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupDataGridView()
        {
            dgvCategories.AutoGenerateColumns = false;
            dgvCategories.Columns.Clear();

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Id",
                HeaderText = "الرقم",
                Name = "Id",
                Width = 60,
                ReadOnly = true
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Name",
                HeaderText = "اسم الفئة",
                Name = "Name",
                Width = 200,
                ReadOnly = true
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Description",
                HeaderText = "الوصف",
                Name = "Description",
                Width = 250,
                ReadOnly = true
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "ProductCount",
                HeaderText = "عدد المنتجات",
                Name = "ProductCount",
                Width = 100,
                ReadOnly = true
            });

            dgvCategories.Columns.Add(new DataGridViewCheckBoxColumn
            {
                DataPropertyName = "IsActive",
                HeaderText = "نشط",
                Name = "IsActive",
                Width = 60,
                ReadOnly = true
            });

            dgvCategories.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "CreatedAt",
                HeaderText = "تاريخ الإنشاء",
                Name = "CreatedAt",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
            });

            dgvCategories.SelectionChanged += DgvCategories_SelectionChanged;
            dgvCategories.RightToLeft = RightToLeft.Yes;
        }

        private void DgvCategories_SelectionChanged(object? sender, EventArgs e)
        {
            if (dgvCategories.SelectedRows.Count > 0)
            {
                var selectedIndex = dgvCategories.SelectedRows[0].Index;
                if (selectedIndex >= 0 && selectedIndex < _categories.Count)
                {
                    _selectedCategory = _categories[selectedIndex];
                    LoadCategoryToForm(_selectedCategory);
                    
                    btnEdit.Enabled = true;
                    btnDelete.Enabled = true;
                    btnViewProducts.Enabled = true;
                }
            }
            else
            {
                _selectedCategory = null;
                btnEdit.Enabled = false;
                btnDelete.Enabled = false;
                btnViewProducts.Enabled = false;
            }
        }

        private void LoadCategoryToForm(Category category)
        {
            txtName.Text = category.Name;
            txtDescription.Text = category.Description ?? "";
            chkIsActive.Checked = category.IsActive;
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtDescription.Clear();
            chkIsActive.Checked = true;
            
            _selectedCategory = null;
            _isEditing = false;
            
            btnSave.Text = "إضافة";
            btnCancel.Text = "مسح";
            btnEdit.Enabled = false;
            btnDelete.Enabled = false;
            btnViewProducts.Enabled = false;
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            ClearForm();
            txtName.Focus();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedCategory == null) return;
            
            _isEditing = true;
            btnSave.Text = "تحديث";
            btnCancel.Text = "إلغاء";
            txtName.Focus();
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var category = new Category
                {
                    Name = txtName.Text.Trim(),
                    Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                bool success;
                if (_isEditing && _selectedCategory != null)
                {
                    category.Id = _selectedCategory.Id;
                    success = await CategoryService.UpdateCategoryAsync(category);
                }
                else
                {
                    success = await CategoryService.AddCategoryAsync(category);
                }

                if (success)
                {
                    MessageBox.Show(_isEditing ? "تم تحديث الفئة بنجاح" : "تم إضافة الفئة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    await LoadCategoriesAsync();
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الفئة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفئة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الفئة", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (_isEditing)
            {
                ClearForm();
            }
            else
            {
                ClearForm();
            }
        }

        private async void BtnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedCategory == null) return;

            var result = MessageBox.Show($"هل أنت متأكد من حذف الفئة '{_selectedCategory.Name}'؟\nملاحظة: لا يمكن حذف الفئة إذا كانت تحتوي على منتجات", "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                try
                {
                    var success = await CategoryService.DeleteCategoryAsync(_selectedCategory.Id);
                    if (success)
                    {
                        MessageBox.Show("تم حذف الفئة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        await LoadCategoriesAsync();
                        ClearForm();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الفئة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفئة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                
                List<Category> filteredCategories;
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    filteredCategories = await CategoryService.SearchCategoriesAsync(searchTerm);
                }
                else
                {
                    filteredCategories = await CategoryService.GetAllCategoriesAsync();
                }

                dgvCategories.DataSource = null;
                dgvCategories.DataSource = filteredCategories;
                _categories = filteredCategories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            InitializeForm();
        }

        private async void BtnViewProducts_Click(object sender, EventArgs e)
        {
            if (_selectedCategory == null) return;

            try
            {
                var products = await CategoryService.GetProductsByCategoryAsync(_selectedCategory.Id);
                
                var productsListForm = new Form
                {
                    Text = $"منتجات الفئة: {_selectedCategory.Name}",
                    Size = new Size(800, 600),
                    StartPosition = FormStartPosition.CenterParent,
                    RightToLeft = RightToLeft.Yes,
                    RightToLeftLayout = true
                };

                var dgv = new DataGridView
                {
                    Dock = DockStyle.Fill,
                    DataSource = products,
                    ReadOnly = true,
                    AllowUserToAddRows = false,
                    AllowUserToDeleteRows = false,
                    SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                    RightToLeft = RightToLeft.Yes
                };

                var closeButton = new Button
                {
                    Text = "إغلاق",
                    Dock = DockStyle.Bottom,
                    Height = 40,
                    UseVisualStyleBackColor = true
                };
                closeButton.Click += (s, e) => productsListForm.Close();

                productsListForm.Controls.Add(dgv);
                productsListForm.Controls.Add(closeButton);
                
                productsListForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
