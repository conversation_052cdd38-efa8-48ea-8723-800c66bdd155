using Microsoft.Data.Sqlite;
using ShopManagementSystem.Data;
using ShopManagementSystem.Models;

namespace ShopManagementSystem.Services
{
    public class InventoryService
    {
        private readonly DatabaseManager _databaseManager;
        private readonly ProductService _productService;

        public InventoryService()
        {
            _databaseManager = new DatabaseManager();
            _productService = new ProductService();
        }

        // إضافة حركة مخزون
        public async Task<bool> AddInventoryMovementAsync(InventoryMovement movement)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    INSERT INTO InventoryMovements 
                    (ProductId, ProductName, MovementType, Quantity, PreviousQuantity, NewQuantity, Notes, UserId, UserName, CreatedAt)
                    VALUES (@productId, @productName, @movementType, @quantity, @previousQuantity, @newQuantity, @notes, @userId, @userName, @createdAt)";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@productId", movement.ProductId);
                command.Parameters.AddWithValue("@productName", movement.ProductName);
                command.Parameters.AddWithValue("@movementType", (int)movement.MovementType);
                command.Parameters.AddWithValue("@quantity", movement.Quantity);
                command.Parameters.AddWithValue("@previousQuantity", movement.PreviousQuantity);
                command.Parameters.AddWithValue("@newQuantity", movement.NewQuantity);
                command.Parameters.AddWithValue("@notes", movement.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@userId", movement.UserId);
                command.Parameters.AddWithValue("@userName", movement.UserName);
                command.Parameters.AddWithValue("@createdAt", movement.CreatedAt);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة حركة المخزون: {ex.Message}");
            }
        }

        // تحديث كمية المنتج مع تسجيل الحركة
        public async Task<bool> UpdateProductQuantityAsync(int productId, int newQuantity, MovementType movementType, string notes, User user)
        {
            try
            {
                var product = _productService.GetProductById(productId);
                if (product == null)
                    throw new Exception("المنتج غير موجود");

                var previousQuantity = product.Quantity;
                var quantityChange = newQuantity - previousQuantity;

                // تحديث كمية المنتج
                product.Quantity = newQuantity;
                _productService.UpdateProduct(product);

                // تسجيل حركة المخزون
                var movement = new InventoryMovement
                {
                    ProductId = productId,
                    ProductName = product.Name,
                    MovementType = movementType,
                    Quantity = Math.Abs(quantityChange),
                    PreviousQuantity = previousQuantity,
                    NewQuantity = newQuantity,
                    Notes = notes,
                    UserId = user.Id,
                    UserName = user.FullName,
                    CreatedAt = DateTime.Now
                };

                await AddInventoryMovementAsync(movement);

                // فحص التنبيهات
                await CheckAndCreateAlertsAsync(product);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث كمية المنتج: {ex.Message}");
            }
        }

        // إدخال مخزون
        public async Task<bool> StockInAsync(int productId, int quantity, string notes, User user)
        {
            var product = _productService.GetProductById(productId);
            if (product == null)
                throw new Exception("المنتج غير موجود");

            var newQuantity = product.Quantity + quantity;
            return await UpdateProductQuantityAsync(productId, newQuantity, MovementType.StockIn, notes, user);
        }

        // إخراج مخزون
        public async Task<bool> StockOutAsync(int productId, int quantity, string notes, User user)
        {
            var product = _productService.GetProductById(productId);
            if (product == null)
                throw new Exception("المنتج غير موجود");

            if (product.Quantity < quantity)
                throw new Exception("الكمية المطلوبة غير متوفرة في المخزون");

            var newQuantity = product.Quantity - quantity;
            return await UpdateProductQuantityAsync(productId, newQuantity, MovementType.StockOut, notes, user);
        }

        // تعديل مخزون
        public async Task<bool> AdjustStockAsync(int productId, int newQuantity, string notes, User user)
        {
            return await UpdateProductQuantityAsync(productId, newQuantity, MovementType.Adjustment, notes, user);
        }

        // الحصول على حركات المخزون
        public async Task<List<InventoryMovement>> GetInventoryMovementsAsync(int? productId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var movements = new List<InventoryMovement>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, ProductId, ProductName, MovementType, Quantity, PreviousQuantity, NewQuantity, Notes, UserId, UserName, CreatedAt
                    FROM InventoryMovements
                    WHERE 1=1";

                if (productId.HasValue)
                    query += " AND ProductId = @productId";

                if (fromDate.HasValue)
                    query += " AND DATE(CreatedAt) >= DATE(@fromDate)";

                if (toDate.HasValue)
                    query += " AND DATE(CreatedAt) <= DATE(@toDate)";

                query += " ORDER BY CreatedAt DESC";

                using var command = connection.CreateCommand();
                command.CommandText = query;

                if (productId.HasValue)
                    command.Parameters.AddWithValue("@productId", productId.Value);

                if (fromDate.HasValue)
                    command.Parameters.AddWithValue("@fromDate", fromDate.Value.ToString("yyyy-MM-dd"));

                if (toDate.HasValue)
                    command.Parameters.AddWithValue("@toDate", toDate.Value.ToString("yyyy-MM-dd"));

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    movements.Add(new InventoryMovement
                    {
                        Id = reader.GetInt32(0),
                        ProductId = reader.GetInt32(1),
                        ProductName = reader.GetString(2),
                        MovementType = (MovementType)reader.GetInt32(3),
                        Quantity = reader.GetInt32(4),
                        PreviousQuantity = reader.GetInt32(5),
                        NewQuantity = reader.GetInt32(6),
                        Notes = reader.IsDBNull(7) ? string.Empty : reader.GetString(7),
                        UserId = reader.GetInt32(8),
                        UserName = reader.GetString(9),
                        CreatedAt = reader.GetDateTime(10)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب حركات المخزون: {ex.Message}");
            }

            return movements;
        }

        // فحص وإنشاء التنبيهات
        public async Task CheckAndCreateAlertsAsync(Product product)
        {
            try
            {
                if (product.Quantity <= product.MinimumQuantity)
                {
                    var alertType = product.Quantity == 0 ? "OUT_OF_STOCK" : "LOW_STOCK";
                    
                    // فحص إذا كان التنبيه موجود مسبقاً
                    var existingAlert = await GetActiveAlertForProductAsync(product.Id);
                    if (existingAlert == null)
                    {
                        await CreateInventoryAlertAsync(new InventoryAlert
                        {
                            ProductId = product.Id,
                            ProductName = product.Name,
                            CurrentQuantity = product.Quantity,
                            MinimumQuantity = product.MinimumQuantity,
                            AlertType = alertType,
                            CreatedAt = DateTime.Now
                        });
                    }
                }
                else
                {
                    // حل التنبيهات الموجودة إذا تم تجديد المخزون
                    await ResolveAlertsForProductAsync(product.Id);
                }
            }
            catch (Exception ex)
            {
                // لا نريد أن تفشل العملية بسبب التنبيهات
                Console.WriteLine($"خطأ في فحص التنبيهات: {ex.Message}");
            }
        }

        // إنشاء تنبيه مخزون
        private async Task<bool> CreateInventoryAlertAsync(InventoryAlert alert)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    INSERT INTO InventoryAlerts 
                    (ProductId, ProductName, CurrentQuantity, MinimumQuantity, AlertType, IsResolved, CreatedAt)
                    VALUES (@productId, @productName, @currentQuantity, @minimumQuantity, @alertType, @isResolved, @createdAt)";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@productId", alert.ProductId);
                command.Parameters.AddWithValue("@productName", alert.ProductName);
                command.Parameters.AddWithValue("@currentQuantity", alert.CurrentQuantity);
                command.Parameters.AddWithValue("@minimumQuantity", alert.MinimumQuantity);
                command.Parameters.AddWithValue("@alertType", alert.AlertType);
                command.Parameters.AddWithValue("@isResolved", alert.IsResolved);
                command.Parameters.AddWithValue("@createdAt", alert.CreatedAt);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تنبيه المخزون: {ex.Message}");
            }
        }

        // الحصول على التنبيه النشط للمنتج
        private async Task<InventoryAlert?> GetActiveAlertForProductAsync(int productId)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, ProductId, ProductName, CurrentQuantity, MinimumQuantity, AlertType, IsResolved, CreatedAt, ResolvedAt
                    FROM InventoryAlerts
                    WHERE ProductId = @productId AND IsResolved = 0
                    ORDER BY CreatedAt DESC
                    LIMIT 1";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@productId", productId);

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return new InventoryAlert
                    {
                        Id = reader.GetInt32(0),
                        ProductId = reader.GetInt32(1),
                        ProductName = reader.GetString(2),
                        CurrentQuantity = reader.GetInt32(3),
                        MinimumQuantity = reader.GetInt32(4),
                        AlertType = reader.GetString(5),
                        IsResolved = reader.GetBoolean(6),
                        CreatedAt = reader.GetDateTime(7),
                        ResolvedAt = reader.IsDBNull(8) ? null : reader.GetDateTime(8)
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب التنبيه: {ex.Message}");
            }

            return null;
        }

        // حل التنبيهات للمنتج
        private async Task<bool> ResolveAlertsForProductAsync(int productId)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    UPDATE InventoryAlerts 
                    SET IsResolved = 1, ResolvedAt = @resolvedAt
                    WHERE ProductId = @productId AND IsResolved = 0";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@productId", productId);
                command.Parameters.AddWithValue("@resolvedAt", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حل التنبيهات: {ex.Message}");
            }
        }

        // الحصول على جميع التنبيهات
        public async Task<List<InventoryAlert>> GetInventoryAlertsAsync(bool includeResolved = false)
        {
            var alerts = new List<InventoryAlert>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, ProductId, ProductName, CurrentQuantity, MinimumQuantity, AlertType, IsResolved, CreatedAt, ResolvedAt
                    FROM InventoryAlerts";

                if (!includeResolved)
                    query += " WHERE IsResolved = 0";

                query += " ORDER BY CreatedAt DESC";

                using var command = connection.CreateCommand();
                command.CommandText = query;

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    alerts.Add(new InventoryAlert
                    {
                        Id = reader.GetInt32(0),
                        ProductId = reader.GetInt32(1),
                        ProductName = reader.GetString(2),
                        CurrentQuantity = reader.GetInt32(3),
                        MinimumQuantity = reader.GetInt32(4),
                        AlertType = reader.GetString(5),
                        IsResolved = reader.GetBoolean(6),
                        CreatedAt = reader.GetDateTime(7),
                        ResolvedAt = reader.IsDBNull(8) ? null : reader.GetDateTime(8)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب التنبيهات: {ex.Message}");
            }

            return alerts;
        }

        // الحصول على المنتجات منخفضة المخزون
        public async Task<List<Product>> GetLowStockProductsAsync()
        {
            var products = new List<Product>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT p.Id, p.Name, p.Code, p.Barcode, p.Price, p.Quantity, p.MinimumQuantity, p.CategoryId, p.Description, p.CreatedAt, p.UpdatedAt, p.IsActive, c.Name as CategoryName
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryId = c.Id
                    WHERE p.IsActive = 1 AND p.Quantity <= p.MinimumQuantity
                    ORDER BY p.Quantity ASC";

                using var command = connection.CreateCommand();
                command.CommandText = query;

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    products.Add(new Product
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Code = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        Barcode = reader.IsDBNull(3) ? string.Empty : reader.GetString(3),
                        Price = reader.GetDecimal(4),
                        Quantity = reader.GetInt32(5),
                        MinimumQuantity = reader.GetInt32(6),
                        CategoryId = reader.GetInt32(7),
                        Description = reader.IsDBNull(8) ? string.Empty : reader.GetString(8),
                        CreatedAt = reader.GetDateTime(9),
                        UpdatedAt = reader.GetDateTime(10),
                        IsActive = reader.GetBoolean(11),
                        Category = reader.IsDBNull(12) ? null : new Category
                        {
                            Id = reader.GetInt32(7),
                            Name = reader.GetString(12)
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب المنتجات منخفضة المخزون: {ex.Message}");
            }

            return products;
        }

        // الحصول على إحصائيات المخزون
        public async Task<InventoryStats> GetInventoryStatsAsync()
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var stats = new InventoryStats();

                // إجمالي المنتجات
                var totalProductsQuery = "SELECT COUNT(*) FROM Products WHERE IsActive = 1";
                using var totalProductsCommand = connection.CreateCommand();
                totalProductsCommand.CommandText = totalProductsQuery;
                stats.TotalProducts = Convert.ToInt32(totalProductsCommand.ExecuteScalar());

                // المنتجات منخفضة المخزون
                var lowStockQuery = "SELECT COUNT(*) FROM Products WHERE IsActive = 1 AND Quantity <= MinimumQuantity AND Quantity > 0";
                using var lowStockCommand = connection.CreateCommand();
                lowStockCommand.CommandText = lowStockQuery;
                stats.LowStockProducts = Convert.ToInt32(lowStockCommand.ExecuteScalar());

                // المنتجات نافدة المخزون
                var outOfStockQuery = "SELECT COUNT(*) FROM Products WHERE IsActive = 1 AND Quantity = 0";
                using var outOfStockCommand = connection.CreateCommand();
                outOfStockCommand.CommandText = outOfStockQuery;
                stats.OutOfStockProducts = Convert.ToInt32(outOfStockCommand.ExecuteScalar());

                // إجمالي قيمة المخزون
                var totalValueQuery = "SELECT SUM(Price * Quantity) FROM Products WHERE IsActive = 1";
                using var totalValueCommand = connection.CreateCommand();
                totalValueCommand.CommandText = totalValueQuery;
                var totalValue = totalValueCommand.ExecuteScalar();
                stats.TotalInventoryValue = totalValue != DBNull.Value ? Convert.ToDecimal(totalValue) : 0;

                // التنبيهات النشطة
                var activeAlertsQuery = "SELECT COUNT(*) FROM InventoryAlerts WHERE IsResolved = 0";
                using var activeAlertsCommand = connection.CreateCommand();
                activeAlertsCommand.CommandText = activeAlertsQuery;
                stats.ActiveAlerts = Convert.ToInt32(activeAlertsCommand.ExecuteScalar());

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب إحصائيات المخزون: {ex.Message}");
            }
        }

        // حل تنبيه محدد
        public async Task<bool> ResolveAlertAsync(int alertId)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    UPDATE InventoryAlerts
                    SET IsResolved = 1, ResolvedAt = @resolvedAt
                    WHERE Id = @alertId";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@alertId", alertId);
                command.Parameters.AddWithValue("@resolvedAt", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حل التنبيه: {ex.Message}");
            }
        }
    }

    // فئة إحصائيات المخزون
    public class InventoryStats
    {
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public int ActiveAlerts { get; set; }
    }
}
