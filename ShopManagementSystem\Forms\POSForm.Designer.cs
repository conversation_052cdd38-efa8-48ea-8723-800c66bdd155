namespace ShopManagementSystem.Forms
{
    partial class POSForm
    {
        private System.ComponentModel.IContainer components = null;
        private Label lblTitle;
        private Button btnClose;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.lblTitle = new Label();
            this.btnClose = new Button();
            this.SuspendLayout();

            // 
            // POSForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 20F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.btnClose);
            this.Name = "POSForm";
            this.RightToLeft = RightToLeft.Yes;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "نقطة البيع";

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Segoe UI", 16F, FontStyle.Bold);
            this.lblTitle.Location = new Point(300, 250);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 30);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "نقطة البيع - قيد التطوير";

            // 
            // btnClose
            // 
            this.btnClose.Location = new Point(350, 350);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(100, 35);
            this.btnClose.TabIndex = 1;
            this.btnClose.Text = "إغلاق";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            this.ResumeLayout(false);
            this.PerformLayout();
        }
    }
}
