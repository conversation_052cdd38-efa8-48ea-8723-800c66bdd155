using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSManager.Models;
using POSManager.Services;

namespace POSManager.Forms
{
    public partial class AdvancedInventoryForm : Form
    {
        private readonly AdvancedInventoryService _inventoryService;
        private readonly User _currentUser;
        private TabControl tabControl;
        private DataGridView dgvMovements, dgvAlerts, dgvSettings;
        private Panel pnlStats;
        private Label lblTotalProducts, lblLowStock, lblOutOfStock, lblTotalValue, lblUnresolvedAlerts;
        private ComboBox cmbMovementType, cmbAlertLevel;
        private DateTimePicker dtpFromDate, dtpToDate;
        private Button btnRefresh, btnExport, btnResolveAlert, btnMarkRead;
        private TextBox txtSearch;

        public AdvancedInventoryForm(User currentUser)
        {
            _currentUser = currentUser;
            _inventoryService = AdvancedInventoryService.Instance;
            InitializeComponent();
            SetupForm();
            _ = LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النافذة الأساسية
            this.Text = "إدارة المخزون المتقدمة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // إنشاء التبويبات الرئيسية
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            // تبويب الإحصائيات والتنبيهات
            var tabStats = new TabPage("الإحصائيات والتنبيهات");
            SetupStatsTab(tabStats);
            tabControl.TabPages.Add(tabStats);

            // تبويب حركات المخزون
            var tabMovements = new TabPage("حركات المخزون");
            SetupMovementsTab(tabMovements);
            tabControl.TabPages.Add(tabMovements);

            // تبويب إعدادات المخزون
            var tabSettings = new TabPage("إعدادات المخزون");
            SetupSettingsTab(tabSettings);
            tabControl.TabPages.Add(tabSettings);

            // تبويب إدارة الموردين
            var tabSuppliers = new TabPage("إدارة الموردين");
            SetupSuppliersTab(tabSuppliers);
            tabControl.TabPages.Add(tabSuppliers);

            // تبويب أوامر الشراء
            var tabPurchaseOrders = new TabPage("أوامر الشراء");
            SetupPurchaseOrdersTab(tabPurchaseOrders);
            tabControl.TabPages.Add(tabPurchaseOrders);

            this.Controls.Add(tabControl);
        }

        private void SetupStatsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // لوحة الإحصائيات
            pnlStats = new Panel
            {
                Height = 120,
                Dock = DockStyle.Top,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.LightBlue
            };

            SetupStatsPanel();
            mainPanel.Controls.Add(pnlStats);

            // لوحة التنبيهات
            var pnlAlerts = new Panel { Dock = DockStyle.Fill };
            var lblAlertsTitle = new Label
            {
                Text = "تنبيهات المخزون",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Height = 30,
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight
            };

            // أدوات التحكم في التنبيهات
            var pnlAlertControls = new Panel { Height = 40, Dock = DockStyle.Top };
            
            cmbAlertLevel = new ComboBox
            {
                Width = 150,
                Location = new Point(10, 8),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbAlertLevel.Items.AddRange(new[] { "جميع المستويات", "معلومات", "تحذير", "حرج", "طوارئ" });
            cmbAlertLevel.SelectedIndex = 0;
            cmbAlertLevel.SelectedIndexChanged += CmbAlertLevel_SelectedIndexChanged;

            btnMarkRead = new Button
            {
                Text = "تحديد كمقروء",
                Width = 100,
                Location = new Point(170, 8),
                BackColor = Color.LightGreen
            };
            btnMarkRead.Click += BtnMarkRead_Click;

            btnResolveAlert = new Button
            {
                Text = "حل التنبيه",
                Width = 100,
                Location = new Point(280, 8),
                BackColor = Color.Orange
            };
            btnResolveAlert.Click += BtnResolveAlert_Click;

            pnlAlertControls.Controls.AddRange(new Control[] { cmbAlertLevel, btnMarkRead, btnResolveAlert });

            // جدول التنبيهات
            dgvAlerts = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupAlertsGrid();

            pnlAlerts.Controls.Add(dgvAlerts);
            pnlAlerts.Controls.Add(pnlAlertControls);
            pnlAlerts.Controls.Add(lblAlertsTitle);

            mainPanel.Controls.Add(pnlAlerts);
            tab.Controls.Add(mainPanel);
        }

        private void SetupStatsPanel()
        {
            var statsLabels = new[]
            {
                ("إجمالي المنتجات", "lblTotalProducts"),
                ("مخزون منخفض", "lblLowStock"),
                ("نافد المخزون", "lblOutOfStock"),
                ("قيمة المخزون", "lblTotalValue"),
                ("تنبيهات غير محلولة", "lblUnresolvedAlerts")
            };

            int x = 20, y = 20;
            foreach (var (title, name) in statsLabels)
            {
                var titleLabel = new Label
                {
                    Text = title,
                    Location = new Point(x, y),
                    Size = new Size(120, 20),
                    Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                    TextAlign = ContentAlignment.MiddleRight
                };

                var valueLabel = new Label
                {
                    Name = name,
                    Text = "0",
                    Location = new Point(x, y + 25),
                    Size = new Size(120, 30),
                    Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                    ForeColor = Color.DarkBlue,
                    TextAlign = ContentAlignment.MiddleCenter,
                    BorderStyle = BorderStyle.FixedSingle,
                    BackColor = Color.White
                };

                // حفظ المراجع للتحديث لاحقاً
                switch (name)
                {
                    case "lblTotalProducts": lblTotalProducts = valueLabel; break;
                    case "lblLowStock": lblLowStock = valueLabel; break;
                    case "lblOutOfStock": lblOutOfStock = valueLabel; break;
                    case "lblTotalValue": lblTotalValue = valueLabel; break;
                    case "lblUnresolvedAlerts": lblUnresolvedAlerts = valueLabel; break;
                }

                pnlStats.Controls.Add(titleLabel);
                pnlStats.Controls.Add(valueLabel);

                x += 140;
            }

            // زر التحديث
            btnRefresh = new Button
            {
                Text = "تحديث",
                Location = new Point(x, y + 10),
                Size = new Size(80, 40),
                BackColor = Color.LightGreen,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            btnRefresh.Click += BtnRefresh_Click;
            pnlStats.Controls.Add(btnRefresh);
        }

        private void SetupAlertsGrid()
        {
            dgvAlerts.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "Level", HeaderText = "المستوى", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "ProductName", HeaderText = "المنتج", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ProductCode", HeaderText = "الكود", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Title", HeaderText = "العنوان", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Message", HeaderText = "الرسالة", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "CurrentStock", HeaderText = "الرصيد الحالي", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "MinimumStock", HeaderText = "الحد الأدنى", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "تاريخ الإنشاء", Width = 120 },
                new DataGridViewCheckBoxColumn { Name = "IsRead", HeaderText = "مقروء", Width = 60 },
                new DataGridViewCheckBoxColumn { Name = "IsResolved", HeaderText = "محلول", Width = 60 }
            });

            // تخصيص ألوان الصفوف حسب مستوى التنبيه
            dgvAlerts.CellFormatting += DgvAlerts_CellFormatting;
        }

        private void SetupMovementsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // أدوات التحكم والفلترة
            var pnlControls = new Panel { Height = 50, Dock = DockStyle.Top };

            txtSearch = new TextBox
            {
                PlaceholderText = "البحث في المنتجات...",
                Width = 200,
                Location = new Point(10, 15)
            };

            cmbMovementType = new ComboBox
            {
                Width = 150,
                Location = new Point(220, 15),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbMovementType.Items.AddRange(new[] { "جميع الأنواع", "شراء", "بيع", "مرتجع", "تسوية", "نقل", "تالف", "فقدان", "عثور عليه", "إنتاج", "استهلاك" });
            cmbMovementType.SelectedIndex = 0;

            dtpFromDate = new DateTimePicker
            {
                Width = 120,
                Location = new Point(380, 15),
                Value = DateTime.Now.AddDays(-30)
            };

            dtpToDate = new DateTimePicker
            {
                Width = 120,
                Location = new Point(510, 15),
                Value = DateTime.Now
            };

            var btnSearch = new Button
            {
                Text = "بحث",
                Width = 80,
                Location = new Point(640, 15),
                BackColor = Color.LightBlue
            };
            btnSearch.Click += BtnSearch_Click;

            btnExport = new Button
            {
                Text = "تصدير",
                Width = 80,
                Location = new Point(730, 15),
                BackColor = Color.LightGreen
            };
            btnExport.Click += BtnExport_Click;

            pnlControls.Controls.AddRange(new Control[] { txtSearch, cmbMovementType, dtpFromDate, dtpToDate, btnSearch, btnExport });

            // جدول الحركات
            dgvMovements = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupMovementsGrid();

            mainPanel.Controls.Add(dgvMovements);
            mainPanel.Controls.Add(pnlControls);
            tab.Controls.Add(mainPanel);
        }

        private void SetupMovementsGrid()
        {
            dgvMovements.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "المعرف", Visible = false },
                new DataGridViewTextBoxColumn { Name = "MovementDate", HeaderText = "التاريخ", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "ProductName", HeaderText = "المنتج", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "ProductCode", HeaderText = "الكود", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "MovementType", HeaderText = "نوع الحركة", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Quantity", HeaderText = "الكمية", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "UnitCost", HeaderText = "التكلفة", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "TotalCost", HeaderText = "الإجمالي", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "BalanceAfter", HeaderText = "الرصيد بعد", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ReferenceNumber", HeaderText = "رقم المرجع", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "UserName", HeaderText = "المستخدم", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Notes", HeaderText = "ملاحظات", Width = 150 }
            });
        }

        private void SetupSettingsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var lblTitle = new Label
            {
                Text = "إعدادات المخزون للمنتجات",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Height = 30,
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight
            };

            // جدول الإعدادات
            dgvSettings = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = new Font("Segoe UI", 9F),
                RightToLeft = RightToLeft.Yes
            };

            SetupSettingsGrid();

            mainPanel.Controls.Add(dgvSettings);
            mainPanel.Controls.Add(lblTitle);
            tab.Controls.Add(mainPanel);
        }

        private void SetupSettingsGrid()
        {
            dgvSettings.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "ProductId", HeaderText = "معرف المنتج", Visible = false },
                new DataGridViewTextBoxColumn { Name = "ProductName", HeaderText = "المنتج", Width = 150, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "ProductCode", HeaderText = "الكود", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "CurrentStock", HeaderText = "الرصيد الحالي", Width = 100, ReadOnly = true },
                new DataGridViewTextBoxColumn { Name = "MinimumStock", HeaderText = "الحد الأدنى", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ReorderLevel", HeaderText = "مستوى إعادة الطلب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "MaximumStock", HeaderText = "الحد الأقصى", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "ReorderQuantity", HeaderText = "كمية إعادة الطلب", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "LeadTimeDays", HeaderText = "مدة التوريد (أيام)", Width = 120 },
                new DataGridViewCheckBoxColumn { Name = "AutoReorder", HeaderText = "طلب تلقائي", Width = 80 },
                new DataGridViewCheckBoxColumn { Name = "EnableAlerts", HeaderText = "تفعيل التنبيهات", Width = 100 }
            });

            dgvSettings.CellEndEdit += DgvSettings_CellEndEdit;
        }

        private void SetupSuppliersTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var lblTitle = new Label
            {
                Text = "إدارة الموردين",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Height = 30,
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight
            };

            var btnManageSuppliers = new Button
            {
                Text = "فتح إدارة الموردين",
                Size = new Size(150, 40),
                Location = new Point(10, 40),
                BackColor = Color.LightBlue,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnManageSuppliers.Click += BtnManageSuppliers_Click;

            var lblDescription = new Label
            {
                Text = "من هنا يمكنك إدارة جميع الموردين، إضافة موردين جدد، تعديل بياناتهم، ومتابعة أدائهم.",
                Location = new Point(10, 90),
                Size = new Size(500, 40),
                Font = new Font("Segoe UI", 9F),
                TextAlign = ContentAlignment.TopRight
            };

            mainPanel.Controls.Add(lblDescription);
            mainPanel.Controls.Add(btnManageSuppliers);
            mainPanel.Controls.Add(lblTitle);
            tab.Controls.Add(mainPanel);
        }

        private async Task LoadDataAsync()
        {
            await LoadStatisticsAsync();
            await LoadAlertsAsync();
            await LoadMovementsAsync();
            await LoadSettingsAsync();
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var stats = await _inventoryService.GetInventoryStatisticsAsync();
                
                lblTotalProducts.Text = stats.TotalProducts.ToString();
                lblLowStock.Text = stats.LowStockProducts.ToString();
                lblOutOfStock.Text = stats.OutOfStockProducts.ToString();
                lblTotalValue.Text = stats.TotalInventoryValue.ToString("C");
                lblUnresolvedAlerts.Text = stats.UnresolvedAlerts.ToString();

                // تلوين التنبيهات
                lblLowStock.ForeColor = stats.LowStockProducts > 0 ? Color.Orange : Color.Green;
                lblOutOfStock.ForeColor = stats.OutOfStockProducts > 0 ? Color.Red : Color.Green;
                lblUnresolvedAlerts.ForeColor = stats.UnresolvedAlerts > 0 ? Color.Red : Color.Green;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadAlertsAsync()
        {
            try
            {
                var alerts = await _inventoryService.GetAlertsAsync(unresolvedOnly: true);
                dgvAlerts.DataSource = alerts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadMovementsAsync()
        {
            try
            {
                var movements = await _inventoryService.GetInventoryMovementsAsync(
                    fromDate: dtpFromDate.Value,
                    toDate: dtpToDate.Value,
                    limit: 1000
                );
                dgvMovements.DataSource = movements;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل حركات المخزون: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadSettingsAsync()
        {
            try
            {
                // هذه الوظيفة ستحتاج لتطوير إضافي لجلب جميع المنتجات مع إعداداتها
                // سيتم تطويرها في المرحلة التالية
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Event Handlers

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private async void BtnSearch_Click(object sender, EventArgs e)
        {
            await LoadMovementsAsync();
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            // سيتم تطوير وظيفة التصدير لاحقاً
            MessageBox.Show("وظيفة التصدير قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void BtnMarkRead_Click(object sender, EventArgs e)
        {
            if (dgvAlerts.SelectedRows.Count > 0)
            {
                foreach (DataGridViewRow row in dgvAlerts.SelectedRows)
                {
                    var alertId = Convert.ToInt32(row.Cells["Id"].Value);
                    await _inventoryService.MarkAlertAsReadAsync(alertId);
                }
                await LoadAlertsAsync();
            }
        }

        private async void BtnResolveAlert_Click(object sender, EventArgs e)
        {
            if (dgvAlerts.SelectedRows.Count > 0)
            {
                foreach (DataGridViewRow row in dgvAlerts.SelectedRows)
                {
                    var alertId = Convert.ToInt32(row.Cells["Id"].Value);
                    await _inventoryService.ResolveAlertAsync(alertId, _currentUser.Id);
                }
                await LoadAlertsAsync();
                await LoadStatisticsAsync();
            }
        }

        private async void CmbAlertLevel_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadAlertsAsync();
        }

        private void DgvAlerts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && dgvAlerts.Columns[e.ColumnIndex].Name == "Level")
            {
                var level = e.Value?.ToString();
                switch (level)
                {
                    case "1": // Info
                        dgvAlerts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightBlue;
                        e.Value = "معلومات";
                        break;
                    case "2": // Warning
                        dgvAlerts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightYellow;
                        e.Value = "تحذير";
                        break;
                    case "3": // Critical
                        dgvAlerts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightCoral;
                        e.Value = "حرج";
                        break;
                    case "4": // Emergency
                        dgvAlerts.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.Red;
                        dgvAlerts.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.White;
                        e.Value = "طوارئ";
                        break;
                }
            }
        }

        private async void DgvSettings_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            // سيتم تطوير حفظ الإعدادات لاحقاً
        }

        private void BtnManageSuppliers_Click(object sender, EventArgs e)
        {
            var supplierForm = new SupplierManagementForm(_currentUser);
            supplierForm.ShowDialog();
        }

        private void SetupPurchaseOrdersTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var lblTitle = new Label
            {
                Text = "إدارة أوامر الشراء",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Height = 30,
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight
            };

            var btnManagePurchaseOrders = new Button
            {
                Text = "فتح إدارة أوامر الشراء",
                Size = new Size(180, 40),
                Location = new Point(10, 40),
                BackColor = Color.LightGreen,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnManagePurchaseOrders.Click += BtnManagePurchaseOrders_Click;

            var lblDescription = new Label
            {
                Text = "من هنا يمكنك إدارة جميع أوامر الشراء، إنشاء أوامر جديدة، متابعة حالة الأوامر، واستلام البضائع.",
                Location = new Point(10, 90),
                Size = new Size(600, 40),
                Font = new Font("Segoe UI", 9F),
                TextAlign = ContentAlignment.TopRight
            };

            mainPanel.Controls.Add(lblDescription);
            mainPanel.Controls.Add(btnManagePurchaseOrders);
            mainPanel.Controls.Add(lblTitle);
            tab.Controls.Add(mainPanel);
        }

        private void BtnManagePurchaseOrders_Click(object sender, EventArgs e)
        {
            var purchaseOrderForm = new PurchaseOrderForm(_currentUser);
            purchaseOrderForm.ShowDialog();
        }

        #endregion
    }
}
