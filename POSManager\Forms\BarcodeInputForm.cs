using System;
using System.Drawing;
using System.Windows.Forms;

namespace POSManager.Forms
{
    public partial class BarcodeInputForm : Form
    {
        public string Barcode { get; private set; } = "";

        private TextBox txtBarcode;
        private Button btnOK, btnCancel;

        public BarcodeInputForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(400, 200);
            this.Text = "إدخال الباركود";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var lblInstruction = new Label 
            { 
                Text = "امسح الباركود أو أدخله يدوياً:",
                Location = new Point(0, 20),
                Size = new Size(300, 20),
                Font = new Font(this.Font, FontStyle.Bold)
            };

            txtBarcode = new TextBox 
            { 
                Location = new Point(0, 50),
                Size = new Size(300, 30),
                Font = new Font(this.Font.FontFamily, 12),
                PlaceholderText = "أدخل الباركود هنا..."
            };

            btnOK = new Button 
            { 
                Text = "موافق",
                Location = new Point(0, 100),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font, FontStyle.Bold)
            };

            btnCancel = new Button 
            { 
                Text = "إلغاء",
                Location = new Point(90, 100),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font(this.Font, FontStyle.Bold)
            };

            panel.Controls.AddRange(new Control[] { lblInstruction, txtBarcode, btnOK, btnCancel });
            this.Controls.Add(panel);
        }

        private void SetupForm()
        {
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            txtBarcode.KeyDown += TxtBarcode_KeyDown;
            
            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
            
            // Focus on textbox when form loads
            this.Load += (s, e) => txtBarcode.Focus();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBarcode.Text))
            {
                MessageBox.Show("يرجى إدخال الباركود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtBarcode.Focus();
                return;
            }

            Barcode = txtBarcode.Text.Trim();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void TxtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                BtnOK_Click(sender, e);
            }
            else if (e.KeyCode == Keys.Escape)
            {
                BtnCancel_Click(sender, e);
            }
        }
    }
}
