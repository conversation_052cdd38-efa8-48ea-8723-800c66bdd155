using Microsoft.Data.Sqlite;
using POSManager.Data;
using POSManager.Models;

namespace POSManager.Services
{
    public class AuthService
    {
        private static User? _currentUser;
        private static UserPermissions? _currentPermissions;
        private static DateTime _lastActivity = DateTime.Now;
        private static readonly object _lockObject = new object();
        
        public static User? CurrentUser 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _currentUser;
                }
            } 
        }
        
        public static UserPermissions? CurrentPermissions 
        { 
            get 
            { 
                lock (_lockObject)
                {
                    return _currentPermissions;
                }
            } 
        }
        
        public static bool IsLoggedIn => CurrentUser != null;

        public static User? GetCurrentUser()
        {
            return CurrentUser;
        }
        
        public static async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    SELECT Id, Username, PasswordHash, FullName, Role, IsActive, CreatedAt, LastLogin
                    FROM Users 
                    WHERE Username = @username AND IsActive = 1";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@username", username);
                
                using var reader = await command.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    var storedPasswordHash = reader["PasswordHash"].ToString() ?? "";
                    var isActive = Convert.ToInt64(reader["IsActive"]) == 1;
                    
                    if (!isActive)
                    {
                        return new LoginResult { Success = false, Message = "الحساب غير مفعل" };
                    }
                    
                    if (!DatabaseManager.VerifyPassword(password, storedPasswordHash))
                    {
                        await LogActivityAsync(0, username, "فشل تسجيل الدخول", "Users", null, "كلمة مرور خاطئة");
                        return new LoginResult { Success = false, Message = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                    }
                    
                    // إنشاء كائن المستخدم
                    var user = new User
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Username = reader["Username"].ToString() ?? "",
                        PasswordHash = storedPasswordHash,
                        FullName = reader["FullName"].ToString() ?? "",
                        Role = Enum.Parse<UserRole>(reader["Role"].ToString() ?? "Cashier"),
                        IsActive = isActive,
                        CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString() ?? DateTime.Now.ToString()),
                        LastLogin = reader["LastLogin"] == DBNull.Value ? null : DateTime.Parse(reader["LastLogin"].ToString() ?? DateTime.Now.ToString())
                    };
                    
                    // تحديث وقت آخر تسجيل دخول
                    await UpdateLastLoginAsync(connection, user.Id);
                    
                    // تعيين المستخدم الحالي والصلاحيات
                    lock (_lockObject)
                    {
                        _currentUser = user;
                        _currentPermissions = UserPermissions.GetPermissions(user.Role);
                        _lastActivity = DateTime.Now;
                    }
                    
                    // تسجيل النشاط
                    await LogActivityAsync(user.Id, user.Username, "تسجيل الدخول", "Users", user.Id, "تسجيل دخول ناجح");
                    
                    return new LoginResult { Success = true, Message = "تم تسجيل الدخول بنجاح", User = user };
                }
                else
                {
                    await LogActivityAsync(0, username, "فشل تسجيل الدخول", "Users", null, "اسم مستخدم غير موجود");
                    return new LoginResult { Success = false, Message = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }
            }
            catch (Exception ex)
            {
                return new LoginResult { Success = false, Message = $"خطأ في تسجيل الدخول: {ex.Message}" };
            }
        }
        
        public static async Task LogoutAsync()
        {
            if (_currentUser != null)
            {
                await LogActivityAsync(_currentUser.Id, _currentUser.Username, "تسجيل الخروج", "Users", _currentUser.Id, "تسجيل خروج");
            }
            
            lock (_lockObject)
            {
                _currentUser = null;
                _currentPermissions = null;
            }
        }
        
        public static void UpdateActivity()
        {
            lock (_lockObject)
            {
                _lastActivity = DateTime.Now;
            }
        }
        
        public static bool IsSessionExpired()
        {
            if (!IsLoggedIn) return true;
            
            lock (_lockObject)
            {
                var autoLockMinutes = GetAutoLockMinutes();
                return DateTime.Now.Subtract(_lastActivity).TotalMinutes > autoLockMinutes;
            }
        }
        
        public static async Task<bool> CheckPermissionAsync(string permission)
        {
            if (!IsLoggedIn || CurrentPermissions == null) return false;
            
            UpdateActivity();
            
            if (IsSessionExpired())
            {
                await LogoutAsync();
                return false;
            }
            
            return permission switch
            {
                "ManageUsers" => CurrentPermissions.CanManageUsers,
                "ManageProducts" => CurrentPermissions.CanManageProducts,
                "ProcessSales" => CurrentPermissions.CanProcessSales,
                "ViewReports" => CurrentPermissions.CanViewReports,
                "ManageSettings" => CurrentPermissions.CanManageSettings,
                "ManageInventory" => CurrentPermissions.CanManageInventory,
                "DeleteSales" => CurrentPermissions.CanDeleteSales,
                "GiveDiscounts" => CurrentPermissions.CanGiveDiscounts,
                _ => false
            };
        }
        
        private static async Task UpdateLastLoginAsync(SqliteConnection connection, int userId)
        {
            var updateQuery = "UPDATE Users SET LastLogin = @lastLogin WHERE Id = @userId";
            using var command = new SqliteCommand(updateQuery, connection);
            command.Parameters.AddWithValue("@lastLogin", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@userId", userId);
            await command.ExecuteNonQueryAsync();
        }
        
        private static async Task LogActivityAsync(int userId, string userName, string action, string tableName, int? recordId, string details)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                await connection.OpenAsync();
                
                var query = @"
                    INSERT INTO ActivityLog (UserId, UserName, Action, TableName, RecordId, Details, IPAddress, CreatedAt)
                    VALUES (@userId, @userName, @action, @tableName, @recordId, @details, @ipAddress, @createdAt)";
                
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@userId", userId);
                command.Parameters.AddWithValue("@userName", userName);
                command.Parameters.AddWithValue("@action", action);
                command.Parameters.AddWithValue("@tableName", tableName ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@recordId", recordId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@details", details ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ipAddress", "127.0.0.1"); // Local application
                command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                
                await command.ExecuteNonQueryAsync();
            }
            catch
            {
                // تجاهل أخطاء تسجيل النشاط لتجنب توقف التطبيق
            }
        }
        
        private static int GetAutoLockMinutes()
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();
                
                var query = "SELECT Value FROM Settings WHERE Key = 'AutoLockMinutes'";
                using var command = new SqliteCommand(query, connection);
                var result = command.ExecuteScalar()?.ToString();
                
                return int.TryParse(result, out var minutes) ? minutes : 30;
            }
            catch
            {
                return 30; // القيمة الافتراضية
            }
        }
    }
    
    public class LoginResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
    }
}
