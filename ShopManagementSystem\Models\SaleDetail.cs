using System;

namespace ShopManagementSystem.Models
{
    public class SaleDetail
    {
        public int Id { get; set; }
        public int SaleId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
        public decimal TotalPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal NetPrice { get; set; }
        
        // Navigation properties
        public Sale? Sale { get; set; }
        public Product? Product { get; set; }
    }
}
