using Microsoft.Data.Sqlite;
using ShopManagementSystem.Data;
using ShopManagementSystem.Models;

namespace ShopManagementSystem.Services
{
    public class CategoryService
    {
        public List<Category> GetAllCategories()
        {
            var categories = new List<Category>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Name, Description, CreatedAt, UpdatedAt, IsActive
                    FROM Categories
                    WHERE IsActive = 1
                    ORDER BY Name";

                using var command = connection.CreateCommand();
                command.CommandText = query;

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    categories.Add(new Category
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Description = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        CreatedAt = reader.GetDateTime(3),
                        UpdatedAt = reader.GetDateTime(4),
                        IsActive = reader.GetBoolean(5)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الفئات: {ex.Message}");
            }

            return categories;
        }

        public Category? GetCategoryById(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Name, Description, CreatedAt, UpdatedAt, IsActive
                    FROM Categories
                    WHERE Id = @id AND IsActive = 1";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", id);

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return new Category
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Description = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        CreatedAt = reader.GetDateTime(3),
                        UpdatedAt = reader.GetDateTime(4),
                        IsActive = reader.GetBoolean(5)
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الفئة: {ex.Message}");
            }

            return null;
        }

        public bool AddCategory(Category category)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    INSERT INTO Categories (Name, Description)
                    VALUES (@name, @description)";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@name", category.Name);
                command.Parameters.AddWithValue("@description", category.Description ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الفئة: {ex.Message}");
            }
        }

        public bool UpdateCategory(Category category)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    UPDATE Categories
                    SET Name = @name, Description = @description, UpdatedAt = @updatedAt
                    WHERE Id = @id";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", category.Id);
                command.Parameters.AddWithValue("@name", category.Name);
                command.Parameters.AddWithValue("@description", category.Description ?? string.Empty);
                command.Parameters.AddWithValue("@updatedAt", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الفئة: {ex.Message}");
            }
        }

        public bool DeleteCategory(int id)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                // حذف منطقي - تغيير IsActive إلى false
                var query = "UPDATE Categories SET IsActive = 0, UpdatedAt = @updatedAt WHERE Id = @id";
                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@id", id);
                command.Parameters.AddWithValue("@updatedAt", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الفئة: {ex.Message}");
            }
        }

        public List<Category> SearchCategories(string searchTerm)
        {
            var categories = new List<Category>();

            try
            {
                using var connection = new SqliteConnection(DatabaseManager.GetConnectionString());
                connection.Open();

                var query = @"
                    SELECT Id, Name, Description, CreatedAt, UpdatedAt, IsActive
                    FROM Categories
                    WHERE IsActive = 1
                    AND (Name LIKE @searchTerm OR Description LIKE @searchTerm)
                    ORDER BY Name";

                using var command = connection.CreateCommand();
                command.CommandText = query;
                command.Parameters.AddWithValue("@searchTerm", $"%{searchTerm}%");

                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    categories.Add(new Category
                    {
                        Id = reader.GetInt32(0),
                        Name = reader.GetString(1),
                        Description = reader.IsDBNull(2) ? string.Empty : reader.GetString(2),
                        CreatedAt = reader.GetDateTime(3),
                        UpdatedAt = reader.GetDateTime(4),
                        IsActive = reader.GetBoolean(5)
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن الفئات: {ex.Message}");
            }

            return categories;
        }
    }
}